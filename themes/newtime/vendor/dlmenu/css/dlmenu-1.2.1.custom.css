/**
 * Modified DLmenu - v1.2.1
 *
 * Licensed under the MIT license.
 * http://www.opensource.org/licenses/mit-license.php
 *
 * Original DLmenu by Codrops
 * https://github.com/codrops/ResponsiveMultiLevelMenu
 */
 
/* Wrapper 
  -------------------------------------------------------------------------- */

  .dl-menuwrapper {
    position: relative;
    z-index: 2000;
    width: 100%;
    max-width: 540px;

    -webkit-perspective: 1000px;
    perspective:         1000px;
    -webkit-perspective-origin: 50% 200%;
    perspective-origin:         50% 200%;
  }

/* Menu button 
  -------------------------------------------------------------------------- */

  .dl-button {
    position: relative;
    border: none;
    width: 60px;
    height: 60px;
    padding: 0;
    font-size: 0;
    line-height: 0;
    text-indent: -9999px; /* hide text */
    text-align: center;
    overflow: hidden;
    cursor: pointer;
    outline: none;
    text-transform: uppercase;
  }

  /* Show text, if data-dl-show-button-text = yes or true */
  .dl-button[data-dl-show-button-text="yes"],
  .dl-button[data-dl-show-button-text="true"] {
    width: 100%;
    line-height: 60px; /* same as height */
    font-size: 17px;
    letter-spacing: 1px;
    text-indent: 0; /* show text */
  }

  /* Burger icon */
  .dl-burger-icon {
    position: absolute;
    width: 60%;
    height: 4px;
    top: 50%;
    left: 20%;
    margin-top: -2px;
  }

  /* Burger icon if text is visible */
  .dl-button[data-dl-show-button-text="yes"] .dl-burger-icon,
  .dl-button[data-dl-show-button-text="true"] .dl-burger-icon {
    width: 30px;
    height: 3px;
    left: 15px;
  }

  /* Create burger bars with pseudo elements */
  .dl-burger-icon:before,
  .dl-burger-icon:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 4px;
    background: #fff;
    left: 0;
  }

  /* Thinner bars if text is visible */
  .dl-button[data-dl-show-button-text="yes"] .dl-burger-icon:before,
  .dl-button[data-dl-show-button-text="true"] .dl-burger-icon:before,
  .dl-button[data-dl-show-button-text="yes"] .dl-burger-icon:after,
  .dl-button[data-dl-show-button-text="true"] .dl-burger-icon:after {
    height: 3px;
  }

  /* Space between bars */
  .dl-burger-icon:before { top:    -9px; }
  .dl-burger-icon:after  { bottom: -9px; }


  /* Less space between bars if text is visible */
  .dl-button[data-dl-show-button-text="yes"] .dl-burger-icon:before,
  .dl-button[data-dl-show-button-text="true"] .dl-burger-icon:before { 
    top:    -8px; 
  }
  .dl-button[data-dl-show-button-text="yes"] .dl-burger-icon:after, 
  .dl-button[data-dl-show-button-text="true"] .dl-burger-icon:after { 
    bottom: -8px; 
  }

/* Menu button animations 
  -------------------------------------------------------------------------- */

  /* Default state */

  .csstransforms.csstransitions .dl-button .dl-burger-icon {
      -webkit-transition: background 0s 0.21s;
         -moz-transition: background 0s 0.21s;
           -o-transition: background 0s 0.21s;
              transition: background 0s 0.21s;
  }

  .csstransforms.csstransitions .dl-button .dl-burger-icon::before {
      -webkit-transition: -webkit-transform 0.23s ease-in, top 0.12s ease-out 0.21s;
         -moz-transition:    -moz-transform 0.23s ease-in, top 0.12s ease-out 0.21s;
           -o-transition:      -o-transform 0.23s ease-in, top 0.12s ease-out 0.21s;
              transition:         transform 0.23s ease-in, top 0.12s ease-out 0.21s;
  }

  .csstransforms.csstransitions .dl-button .dl-burger-icon::after {
      -webkit-transition: -webkit-transform 0.23s ease-in, bottom 0.12s ease-out 0.21s;
         -moz-transition:    -moz-transform 0.23s ease-in, bottom 0.12s ease-out 0.21s;
           -o-transition:      -o-transform 0.23s ease-in, bottom 0.12s ease-out 0.21s;
              transition:         transform 0.23s ease-in, bottom 0.12s ease-out 0.21s;
  }

  /* Active state */

  .csstransforms.csstransitions .dl-button.dl-active .dl-burger-icon::before {
    top: 0;

    -webkit-transform: rotate(-45deg);
        -ms-transform: rotate(-45deg);
            transform: rotate(-45deg);
    -webkit-transition: top 0.12s ease-in, -webkit-transform 0.25s ease-out 0.10s;
       -moz-transition: top 0.12s ease-in,    -moz-transform 0.25s ease-out 0.10s;
         -o-transition: top 0.12s ease-in,      -o-transform 0.25s ease-out 0.10s;
            transition: top 0.12s ease-in,         transform 0.25s ease-out 0.10s;
  }

  .csstransforms.csstransitions .dl-button.dl-active .dl-burger-icon::after {
    bottom: 0;

    -webkit-transform: rotate(45deg);
        -ms-transform: rotate(45deg);
            transform: rotate(45deg);
    -webkit-transition: bottom 0.12s ease-in, -webkit-transform 0.25s ease-out 0.10s;
       -moz-transition: bottom 0.12s ease-in,    -moz-transform 0.25s ease-out 0.10s;
         -o-transition: bottom 0.12s ease-in,      -o-transform 0.25s ease-out 0.10s;
            transition: bottom 0.12s ease-in,         transform 0.25s ease-out 0.10s;
  }

  .csstransforms.csstransitions .dl-button.dl-active .dl-burger-icon {
    background: transparent;

    -webkit-transition: background 0s 0.10s;
       -moz-transition: background 0s 0.10s;
         -o-transition: background 0s 0.10s;
            transition: background 0s 0.10s;
  }

/* General styles 
  -------------------------------------------------------------------------- */

  .dl-menuwrapper ul {
    padding: 0;
    list-style: none;

    -webkit-transform-style: preserve-3d;
    transform-style:         preserve-3d;
  }

  .dl-menuwrapper li {
    position: relative;
  }

  .dl-menuwrapper li a {
    display: block;
    position: relative;
    padding: 20px 40px 20px 20px;
    font-size: 16px;
    line-height: 1.25;
    outline: none;
    text-decoration: none !important;
  }

  /* back button */
  .dl-menuwrapper li.dl-back > a {
    padding-left: 40px;
    position: static; /* IE 8 absolute/relative bug fix */
  }

  /* back arrow */
  .dl-menuwrapper li.dl-back:after {
    content: '';
    position: absolute;
    left: 15px;
    top: 50%;
    margin-top: -7px;
    height: 0;
    border: 6px solid transparent;

  }

  /* sub menu forward arrow */
  .dl-menuwrapper li > a:not(:only-child):after {
    content: '';
    position: absolute;
    right: 20px;
    top: 50%;
    margin-top: -7px;
    height: 0;
    border: 6px solid transparent;
  }

  /* Parent title  */
  .dl-menuwrapper li.dl-parent-title {
    padding: 11px 15px 13px;
    font-size: 12px;
    letter-spacing: 1px;
    text-transform: uppercase;
    text-align: center;
  }

/* Main menu 
  -------------------------------------------------------------------------- */

  .dl-menuwrapper .dl-menu {
    opacity: 0;
    margin: 5px 0 0 0; /* Same as .dl-menuwrapper > .dl-submenu top value */
    position: absolute;
    width: 100%;
    pointer-events: none;
    padding-top: 10px;
    padding-bottom: 10px;
    border-bottom: solid 6px transparent;

    /* no need for -ms- because IE does not supprt transitions */
    -webkit-transform: translateY(10px);
    transform:         translateY(10px);

    -webkit-backface-visibility: hidden;
    backface-visibility:         hidden;
  }

  .lt-ie9 .dl-menuwrapper .dl-menu {
    display: none;
  }

  .dl-menuwrapper .dl-menu.dl-menu-toggle {
    -webkit-transition: all 0.3s ease;
    transition:         all 0.3s ease;
  }

  .dl-menuwrapper .dl-menu.dl-menuopen {
    opacity: 1;
    pointer-events: auto;

    /* no need for -ms- because IE does not supprt transitions */
    -webkit-transform: translateY(0px);
    transform:         translateY(0px);
  }

  .lt-ie9 .dl-menuwrapper .dl-menu.dl-menuopen {
    display: block;
  }

/* Sub menu 
  -------------------------------------------------------------------------- */

  /* Hide the inner submenus */
  .dl-menuwrapper li .dl-submenu {
    display: none;
  }

  /**
   * When a submenu is openend, we will hide all li siblings.
   *
   * For that we give a class to the parent menu called "dl-subview".
   * We also hide the submenu link. 
   * The opened submenu will get the class "dl-subviewopen".
   * All this is done for any sub-level being entered.
   */

  .dl-menu.dl-subview li,
  .dl-menu.dl-subview li.dl-subviewopen > a,
  .dl-menu.dl-subview li.dl-subview > a {
    display: none;
  }

  .dl-menu.dl-subview li.dl-subview,
  .dl-menu.dl-subview li.dl-subview .dl-submenu,
  .dl-menu.dl-subview li.dl-subviewopen,
  .dl-menu.dl-subview li.dl-subviewopen > .dl-submenu,
  .dl-menu.dl-subview li.dl-subviewopen > .dl-submenu > li {
    display: block;
  }

  /* Dynamically added submenu outside of the menu context */
  .dl-menuwrapper > .dl-submenu {
    position: absolute;
    width: 100%;
    top: 5px; /* Same as .dl-menuwrapper .dl-menu margin-top value */
    left: 0;
    margin: 0;
    padding-top: 10px;
    padding-bottom: 10px;
    border-bottom: solid 6px transparent;
  }

/* Animations 
  -------------------------------------------------------------------------- */

  .dl-menu.dl-animate-out-1 {
    -webkit-animation: MenuAnimOut1 0.3s; /* must have same time as dl-submenu.dl-animate-in-1 */
    animation:         MenuAnimOut1 0.3s; /* must have same time as dl-submenu.dl-animate-in-1 */
  }

  @-webkit-keyframes MenuAnimOut1 {
    0% { 
      -webkit-transform: translateZ(0px);
      opacity: 1;
    }
    50% {
      opacity: 0.75;
    }
    100% {
      -webkit-transform: translateZ(-300px);
      opacity: 0;
    }
  }

  @keyframes MenuAnimOut1 {
    0% { 
      opacity: 1;
      -webkit-transform: translateZ(0);
      transform:         translateZ(0);
    }
    50% {
      opacity: 0.75;
    }
    100% {
      -webkit-transform: translateZ(-300px);
      transform:         translateZ(-300px);
      opacity: 0;
    }
  }

  .dl-menu.dl-animate-in-1 {
    -webkit-animation: MenuAnimIn1 0.25s;
    animation:         MenuAnimIn1 0.25s;
  }

  @-webkit-keyframes MenuAnimIn1 {
    0% {
      -webkit-transform: translateZ(-300px);
      opacity: 0;
    }
    33% {
      opacity: 0.75;
    }
    100% {
      -webkit-transform: translateZ(0);
      opacity: 1;
    }
  }

  @keyframes MenuAnimIn1 {
    0% {
      -webkit-transform: translateZ(-300px);
      transform:         translateZ(-300px);
      opacity: 0;
    }
    33% {
      opacity: 0.75;
    }
    100% {
      -webkit-transform: translateZ(0);
      transform:         translateZ(0);
      opacity: 1;
    }
  }

  .dl-menuwrapper > .dl-submenu.dl-animate-in-1 {
    -webkit-animation: SubMenuAnimIn1 0.3s ease; /* must have same time as .dl-menu.dl-animate-out-1 */
    animation:         SubMenuAnimIn1 0.3s ease; /* must have same time as .dl-menu.dl-animate-out-1 */
  }


  @-webkit-keyframes SubMenuAnimIn1 {
    0% {
      -webkit-transform: translateX(50%);
      opacity: 0;
    }
    100% {
      -webkit-transform: translateX(0px);
      opacity: 1;
    }
  }

  @keyframes SubMenuAnimIn1 {
    0% {
      -webkit-transform: translateX(50%);
      transform:         translateX(50%);
      opacity: 0;
    }
    100% {
      -webkit-transform: translateX(0px);
      transform:         translateX(0px);
      opacity: 1;
    }
  }

  .dl-menuwrapper > .dl-submenu.dl-animate-out-1 {
    -webkit-animation: SubMenuAnimOut1 0.3s ease;
    animation: SubMenuAnimOut1 0.3s ease;
  }

  @-webkit-keyframes SubMenuAnimOut1 {
    0% {
      -webkit-transform: translateX(0%);
      opacity: 1;
    }
    100% {
      -webkit-transform: translateX(50%);
      opacity: 0;
    }
  }

  @keyframes SubMenuAnimOut1 {
    0% {
      -webkit-transform: translateX(0%);
      transform:         translateX(0%);
      opacity: 1;
    }
    100% {
      -webkit-transform: translateX(50%);
      transform:         translateX(50%);
      opacity: 0;
    }
  }

/* No JS fallback 
  -------------------------------------------------------------------------- */

  .no-js .dl-menuwrapper .dl-menu {
    position: relative;
    opacity: 1;
    pointer-events: auto;

    -webkit-transform: none;
    transform:         none;
  }

  .no-js .dl-menuwrapper li .dl-submenu {
    display: block;
  }

  .no-js .dl-menuwrapper li.dl-back {
    display: none;
  }

  .no-js .dl-menuwrapper li > a:not(:only-child) {
    background: rgba(0,0,0,0.1);
  }

  .no-js .dl-menuwrapper li > a:not(:only-child):after {
    margin-top: -4px;
    border-left-color: transparent !important;
  }

/* Colors 
  -------------------------------------------------------------------------- */

  /* button color */
  .dl-button,
  .dl-button[data-dl-show-button-text="yes"].dl-active,
  .dl-button[data-dl-show-button-text="true"].dl-active {
    background: #2c324b;
    color: #fff;
  }

  .dl-button.dl-active {
    background: #2c324b;
  }

  /* button burger icon color */
  .dl-burger-icon,
  .dl-burger-icon:before,
  .dl-burger-icon:after {
    background: #fff;
  }

  /* main color */
  .dl-menuwrapper ul {
    background: #2c324b;
  }

  /* bottom border color */
  .dl-menuwrapper .dl-menu,
  .dl-menuwrapper > .dl-submenu {
    border-bottom-color: #1d2030;
  }

  /* mouse hover color */
  .no-touch .dl-menuwrapper li a:hover {
    background: #3b4363; /* about 10% lighter/darker than main color */
  }

  /* back button color */
  .dl-menuwrapper li.dl-back > a {
    background: #1d2030; /* about 10% lighter/darker than main color */
  }

  /* back arrow color */
  .dl-menuwrapper li.dl-back:after{
    border-right-color: #767e9f;
  }

  /* forward arrow color */
  .dl-menuwrapper li > a:not(:only-child):after,
  .no-js .dl-menuwrapper li > a:not(:only-child):after {
    border-left-color: #767e9f;
  }

  .no-js .dl-menuwrapper li > a:not(:only-child):after {
    border-top-color: #767e9f;
  }

  /* link color */
  .dl-menuwrapper li a {
    color: #fff;
  }

  /* Parent color */
  .dl-menuwrapper li.dl-parent-title {
    color: #fff;
  }
