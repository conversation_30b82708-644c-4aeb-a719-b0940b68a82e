/* CSS Guide:

- Use text editor that collapses code based on indents (eg. Sublime Text)
- Indent using 2 spaces
- Collapse everything for a better overview and a faster workflow
- Write media queries right after the original selector and extra indent it
- Use classes for everything and leave tag selectors only for scopes
- Continue the same formatting style and keep it clean

*/

/* BUG FIXES
** ========================================================================== */

/* Fix Windows Phone 8 and Device-Width
  --------------------------------------------------------------------------- */

/* http://timkadlec.com/2013/01/windows-phone-8-and-device-width */

/* @-webkit-viewport {
  width: device-width;
}
@-moz-viewport {
  width: device-width;
}
@-ms-viewport {
  width: device-width;
}
@-o-viewport {
  width: device-width;
}
@viewport {
  width: device-width;
} */
/* Bring back Google maps UI controls
  --------------------------------------------------------------------------- */

/*
  #google-map img {
    max-width: none;
    width: auto;
  }
  */

/* FONTS
** ========================================================================== */

/* Source Sans Pro
  --------------------------------------------------------------------------- */

@font-face {
  font-family: 'source_sans_proregular';
  src: url('../fonts/sourcesanspro-regular-webfont.eot');
  src: url('../fonts/sourcesanspro-regular-webfont.eot?#iefix') format('embedded-opentype'),
    url('../fonts/sourcesanspro-regular-webfont.woff') format('woff'),
    url('../fonts/sourcesanspro-regular-webfont.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'source_sans_prolight';
  src: url('../fonts/sourcesanspro-light-webfont.eot');
  src: url('../fonts/sourcesanspro-light-webfont.eot?#iefix') format('embedded-opentype'),
    url('../fonts/sourcesanspro-light-webfont.woff') format('woff'),
    url('../fonts/sourcesanspro-light-webfont.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'source_sans_proextralight';
  src: url('../fonts/sourcesanspro-extralight-webfont.eot');
  src: url('../fonts/sourcesanspro-extralight-webfont.eot?#iefix') format('embedded-opentype'),
    url('../fonts/sourcesanspro-extralight-webfont.woff') format('woff'),
    url('../fonts/sourcesanspro-extralight-webfont.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'source_sans_prosemibold';
  src: url('../fonts/sourcesanspro-semibold-webfont.eot');
  src: url('../fonts/sourcesanspro-semibold-webfont.eot?#iefix') format('embedded-opentype'),
    url('../fonts/sourcesanspro-semibold-webfont.woff') format('woff'),
    url('../fonts/sourcesanspro-semibold-webfont.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'source_sans_probold';
  src: url('../fonts/SourceSansPro-Bold.eot');
  src: url('../fonts/SourceSansPro-Bold.eot?#iefix') format('embedded-opentype'),
    url('../fonts/SourceSansPro-Bold.woff') format('woff'),
    url('../fonts/SourceSansPro-Bold.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

/* SCOPES
** ========================================================================== */

/* Default scope
  --------------------------------------------------------------------------- */

html,
body {
  width: 100%;
}

body {
  min-width: 300px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

body.page-unclickable header,
body.page-unclickable footer,
body.page-unclickable .menu-side,
body.page-unclickable .sidebar-rates,
body.page-unclickable .breadcrumbs {
  pointer-events: none;
}

body.page-unclickable .footer-menu,
body.page-unclickable .menu-side,
body.page-unclickable .mob-menu,
body.page-unclickable .sidebar-rates a {
	display: none !important;
}

body.page-unclickable .sidebar-rates {
	margin-top: 30px;
}

body.page-unclickable .nav-main {
    visibility: hidden;
}

/* h1,
h2,
h3,
h4,
h5,
h6 {
}

p {
} */

strong,
b {
  font-family: 'source_sans_probold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-weight: normal;
  font-style: normal;
}

i,
em {
  font-family: 'example-italic-font', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-weight: normal;
  font-style: normal;
}





ul,
ol {
  padding-left: 0;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
}



img {
  display: inline-block;
  max-width: 100%;
  width: 100%;
  height: auto;
}
.flex {
  display: flex;
}
.justify-center {
  justify-content: center;
}
/* User Added Content
  --------------------------------------------------------------------------- */

.user-added-content:after {
  content: '';
  display: table;
  clear: both;
}

.user-added-content li {
  position: relative;
  font-size: 16px;
  margin-bottom: 4px;
  padding-left: 20px;
  list-style-type: none;
}

.user-added-content li:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 4px;
  height: 4px;
  margin-top: -2px;
  background: #8ba693;
}

.user-added-content h1,
.user-added-content h2,
.user-added-content h3,
.user-added-content h4,
.user-added-content h5,
.user-added-content h6 {
  color: #165528;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.user-added-content h1 {
  font-size: 32px;
}

.user-added-content h2 {
  font-size: 28px;
}

.user-added-content h3 {
  font-size: 24px;
}

.user-added-content h4 {
  font-size: 20px;
}

.user-added-content p,
.user-added-content span {
  color: #434343;
  font-size: 16px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.user-added-content a {
  text-decoration: none;
  color: #165528;
  border-bottom: 1px solid #165528;
  font-size: 16px;
  /* font-family: "source_sans_prolight", "Helvetica Neue", Helvetica, arial, sans-serif; */
  border-bottom: 1px solid transparent;
  -webkit-transition: 0.3s ease;
  -moz-transition: 0.3s ease;
  -o-transition: 0.3s ease;
  -ms-transition: 0.3s ease;
  transition: 0.3s ease;
}

.user-added-content a:focus,
.user-added-content a:hover,
.user-added-content a:active {
  text-decoration: none;
  color: #165528;
  border-bottom: 1px solid #165528;
}

.pricing-template-page .user-added-content a:focus,
.pricing-template-page .user-added-content a:hover,
.pricing-template-page .user-added-content a:active,
.form-template-page .user-added-content a:focus,
.form-template-page .user-added-content a:hover,
.form-template-page .user-added-content a:active {
  border-bottom: 1px solid #ffffff;
}

.user-added-content b {
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.user-added-content table {
  width: 100%;
}
/* Added Content contact
  --------------------------------------------------------------------------- */

.added-content-contact {
  position: relative;
  float: none;
  width: 100%;
  margin-bottom: 20px;
}

@media (min-width: 768px) {
  .added-content-contact {
    float: right;
    width: 50%;
    margin-left: 15px;
    margin-bottom: 0;
    padding-left: 15px;
    padding-bottom: 15px;
    border-left: 1px solid #d9d9d9;
  }
}

/* TABLESAW OVERWRITE
** ========================================================================== */

/*  td-width
 * -------------------------------------------------------------------------- */

.tablesaw-stack td .tablesaw-cell-label,
.tablesaw-stack th .tablesaw-cell-label {
  width: 40%;
}

/* TABLESAW OVERWRITE
** ========================================================================== */

/*  Table
 * -------------------------------------------------------------------------- */

@media (max-width: 767px) {
  .table > tbody > tr > td,
  .table > tbody > tr > th,
  .table > tfoot > tr > td,
  .table > tfoot > tr > th,
  .table > thead > tr > td,
  .table > thead > tr > th {
    border-top: 0;
  }
}

/* SMALLER MODULES
** ========================================================================== */

/* col y
 * -------------------------------------------------------------------------- */

.col-xs-y,
.col-sm-y,
.col-md-y,
.col-lg-y {
  padding: 0 40px;
  position: relative;
  min-height: 1px;
  width: 20%;
  float: left;
}
/* social-icons
 * -------------------------------------------------------------------------- */
.social-icons {
  padding-left: 0;
  list-style-type: none;
  text-align: center;
}

@media (min-width: 768px) {
  .social-icons {
    display: block;
    text-align: left;
  }
  .right-social-icons {
    float: right;
    margin-left: 20px;
    margin-right: 20px;
    text-align: right;
  }
}

.social-icons-item {
  display: inline-block;
}

.social-icons-link {
  position: relative;
  display: block;
  height: 37px;
  width: 37px;
  background: #8ba693;
  color: #8ba693;
  border-radius: 100%;
  -webkit-transition: background 0.2s ease-in-out;
  -moz-transition: background 0.2s ease-in-out;
  -ms-transition: background 0.2s ease-in-out;
  -o-transition: background 0.2s ease-in-out;
  transition: background 0.2s ease-in-out;
}

.social-icons-link:hover,
.social-icons-link:focus,
.social-icons-link:active {
  background: #165528;
}

.social-icons-link-icon {
  position: absolute;
  display: inline-block;
  left: 50%;
  font-size: 24px;
  margin-left: -10.2px;
  bottom: 50%;
  margin-bottom: -11px;
  color: #fff;
}

.social-icons-link-fb {
  margin-left: -8.2px;
}

.social-icons-link-rss {
  margin-left: -7.2px;
}

.sidebar-social-icons {
  margin-top: 40px;
}
/* mob-social-icons
 * -------------------------------------------------------------------------- */
.mob-social-icons {
  margin-top: 20px;
}
/* social-icons-link-sm
 * -------------------------------------------------------------------------- */

.social-icons-link.social-icons-link-sm {
  height: 25px;
  width: 25px;
}

.social-icons-link-icon-sm {
  font-size: 14px;
  margin-left: -5.2px;
  margin-bottom: -6px;
}
.social-icons-link-icon-sm .social-icons-link-fb {
  margin-left: -3.2px;
}
/* breadcrumbs
 * -------------------------------------------------------------------------- */

.breadcrumbs-list {
  padding-left: 0;
}

.breadcrumbs-item {
  display: inline-block;
  list-style-type: none;
}

.breadcrumbs-link {
  position: relative;
  color: #a5a5a5;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 12px;
  text-decoration: none;
  padding-left: 16px;
  padding-right: 8px;
}

.breadcrumbs-link:focus,
.breadcrumbs-link:hover,
.breadcrumbs-link:active {
  color: #165528;
  text-decoration: none;
}

.breadcrumbs-link:before {
  content: '\f054';
  position: absolute;
  top: 50%;
  left: 0;
  margin-top: -5px;
  font: normal normal normal 8px/1 FontAwesome;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.breadcrumbs-link-current {
  font-family: 'source_sans_probold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
}
/* headings
 * -------------------------------------------------------------------------- */

.primary-heading {
  color: #165528;
  font-size: 35px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.secondary-heading-w-border {
  display: block;
  margin-left: -15px;
  margin-right: -15px;
  margin-bottom: 20px;
  text-align: center;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 30px;
  color: #165528;
  border-bottom: 1px solid #d1d1d1;
}

@media (min-width: 768px) {
  .secondary-heading-w-border {
    margin-left: 0;
    margin-right: 0;
    text-align: left;
  }
}

.heading-no-margin {
  margin-top: 0;
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .groups-primary-heading {
    border-bottom: 1px solid #a5a5a5;
  }
}

.dev-main-heading {
  margin: 0;
  display: inline-block;
  color: #165528;
}

.developments-heading {
  margin-bottom: 15px;
  border-bottom: none;
  text-align: center;
}

@media (min-width: 768px) {
  .developments-heading {
    margin-bottom: 15px;
    border-bottom: 1px solid #a5a5a5;
    text-align: left;
  }
}
/* buttons
 * -------------------------------------------------------------------------- */

.main-button {
  display: inline-block;
  padding: 8px;
  color: #fff;
  background: #165528;
  font-size: 13px;
  text-transform: uppercase;
  border-radius: 0;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  border: none;
  cursor: pointer;
  -o-transition: 0.4s;
  -ms-transition: 0.4s;
  -moz-transition: 0.4s;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.main-button:focus,
.main-button:hover,
.main-button:active {
  text-decoration: none;
  color: #d5ded8;
}

.link-button {
  position: relative;
  display: inline-block;
  padding-right: 15px;
  color: #165528;
  font-size: 15px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  border-bottom: 1px solid transparent;
  -o-transition: 0.5s;
  -ms-transition: 0.5s;
  -moz-transition: 0.5s;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

.link-button:focus,
.link-button:hover,
.link-button:active {
  border-bottom: 1px solid #165528;
  color: #165528;
  text-decoration: none;
}

.link-button:after {
  content: '\f054';
  position: absolute;
  top: 50%;
  right: 0;
  margin-top: -4.5px;
  font: normal normal normal 8px/1 FontAwesome;
  font-size: 9px;
  color: #cbd7cf;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}

.link-button-secondary {
  margin-top: 12px;
  margin-bottom: 10px;
  padding-right: 15px;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
}
/* content-divider
 * -------------------------------------------------------------------------- */

.content-divider {
  position: relative;
  padding: 0 15px;
  margin-right: -15px;
  margin-left: -15px;
  margin-bottom: 130px;
  /*z-index: 0;*/
}

.content-divider:before {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 32.9%; /*-0.4*/
  width: 1px;
  background: #8ba693;
}

@media (min-width: 992px) {
  .content-divider:before {
    left: 24.6%; /*-0.4*/
  }
}

.content-divider:after {
  position: absolute;
  top: 30px;
  bottom: -30px;
  left: 33.3%;
  width: 1px;
  background: #8ba693;
  z-index: -1;
}

@media (min-width: 992px) {
  .content-divider:after {
    left: 25%;
  }
}

@media (min-width: 768px) {
  .content-divider:before,
  .content-divider:after {
    content: '';
  }
}
/* padding-left
 * -------------------------------------------------------------------------- */

.padding-left {
  padding-left: 15px;
}

@media (min-width: 768px) {
  .padding-left {
    padding-left: 30px;
  }
}
/* customboxes
  --------------------------------------------------------------------------- */

.customboxes {
  margin-bottom: 20px;
}

.customboxes-content {
  padding-left: 15px;
  padding-right: 15px;
  height: 113px;
  background-color: #e5f0e8;
}

.customboxes-content-heading {
  display: block;
  font-size: 50px;
  color: #165528;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.customboxes-content-info {
  display: block;
  font-size: 18px;
  color: #165528;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.customboxes-content-right,
.customboxes-content-left {
  background-repeat: no-repeat;
}

.customboxes-content-left {
  background-image: url('../img/invest_box_bg_left.png');
}

.customboxes-content-right {
  background-image: url('../img/invest_box_bg_right.png');
}
/* txt-align
  --------------------------------------------------------------------------- */

@media (max-width: 767px) {
  .txt-center-xs {
    text-align: center;
  }
}

@media (min-width: 768px) {
  .txt-right-med {
    text-align: right;
  }
}
/* relative
  --------------------------------------------------------------------------- */

.relative {
  position: relative;
}
/* failure
  --------------------------------------------------------------------------- */
.failure {
  text-align: center;
}

.failure-heading {
  margin-top: 90px;
  font-size: 45px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #8ba693;
}

.failure-issue {
  margin-bottom: 90px;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 30px;
  color: #165528;
}

.failure-info {
  margin-bottom: 40px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 16px;
  color: #165528;
}

.failure-button {
  margin-bottom: 90px;
}

.failure-social-icons .social-icons {
  text-align: center;
}
/* no-padding
  --------------------------------------------------------------------------- */
/* bottom-border
  --------------------------------------------------------------------------- */

.bottom-border {
  border-bottom: 1px solid #a5a5a5;
}
/* margin-bottom
  --------------------------------------------------------------------------- */

.margin-bottom {
  margin-bottom: 10px;
}

.margin-bottom-lg {
  margin-bottom: 30px;
}
/* custom-cols
  --------------------------------------------------------------------------- */

.custom-col-xs-halfed {
  position: relative;
  float: left;
  width: 50%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}

@media (min-width: 768px) {
  .custom-col-sm-quarter {
    position: relative;
    float: left;
    width: 20%;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
  }
}

/* DL-MENU
** ========================================================================== */

/* dl-menu
  -------------------------------------------------------------------------- */
.dl-menu .menu-item {
  display: block;
}
/* dl-menu arrows
  -------------------------------------------------------------------------- */

.dl-menuwrapper li > a:not(:only-child):after,
.dl-menuwrapper li.dl-back:after {
  position: absolute;
  top: 50%;
  display: inline-block;
  height: 0;
  margin-top: -12.5px;
  font: normal normal normal 25px/1 FontAwesome;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  border: none;
  color: #fff;
}

/* forward arrow */
.dl-menuwrapper li > a:not(:only-child):after {
  content: '\f105';
  right: 20px;
}

/* back arrow */
.dl-menuwrapper li.dl-back:after {
  content: '\f104';
  left: 15px;
}
/* dl-menu-footer
  -------------------------------------------------------------------------- */

.dl-button-footer.dl-button,
.dl-button-footer.dl-button[data-dl-show-button-text='yes'].dl-active,
.dl-button-footer.dl-button[data-dl-show-button-text='true'].dl-active {
  margin: 0 auto;
  background: transparent;
}

.dl-button-footer .dl-burger-icon,
.dl-button-footer .dl-burger-icon:before,
.dl-button-footer .dl-burger-icon:after {
  background: #89a491;
  color: #89a491;
}

.dl-button.dl-button-footer.dl-active {
  background: transparent;
}
/* Colors
  -------------------------------------------------------------------------- */

/* button color */
.dl-button-top.dl-button,
.dl-button-top.dl-button[data-dl-show-button-text='yes'].dl-active,
.dl-button-top.dl-button[data-dl-show-button-text='true'].dl-active {
  background-color: transparent;
  width: max-content;
  height: unset;
  color: #216128;
}

.dl-button.dl-active {
  background: #2c324b;
}

.dl-button-top .dl-burger-icon {
  top: 40%;
}

.dl-button-top-text {
  margin-top: 35px;
}

/* button burger icon color */
.dl-button-top .dl-burger-icon,
.dl-button-top .dl-burger-icon:before,
.dl-button-top .dl-burger-icon:after {
  background: #216128;
}

/* main color */
.dl-menuwrapper ul {
  background: #165528;
}

/* bottom border color */
.dl-menuwrapper .dl-menu,
.dl-menuwrapper > .dl-submenu {
  border-bottom-color: #165528;
}

/* mouse hover color */
.no-touch .dl-menuwrapper li a:hover {
  background: #0a3616; /* about 10% lighter/darker than main color */
}

.dl-menuwrapper .dl-menu.dl-menu-top li a:hover {
  background: #0a3616;
}

/* back button color */
.dl-menuwrapper li.dl-back > a {
  text-transform: uppercase;
  color: #d5ded8;
  background: #0a3616; /* about 10% lighter/darker than main color */
}

/* back arrow color */
.dl-menuwrapper li.dl-back:after {
  border-right-color: #fff;
}

/* forward arrow color */
.dl-menuwrapper li > a:not(:only-child):after,
.no-js .dl-menuwrapper li > a:not(:only-child):after {
  border-left-color: #fff;
}

.no-js .dl-menuwrapper li > a:not(:only-child):after {
  border-top-color: #fff;
}

/* link color */
.dl-menuwrapper li a {
  color: #fff;
}

/* Parent color */
.dl-menuwrapper li.dl-parent-title {
  color: #fff;
}

/* MOB-FOOTER-MENU
** ========================================================================== */

/* mob-footer-menu-trigger
 * -------------------------------------------------------------------------- */

.mob-footer-menu-trigger {
  margin-top: 18px;
  color: #8ba693;
  font-size: 25px;
  text-align: center;
}

/* HEADER
** ========================================================================== */

/* mob-menu
 * -------------------------------------------------------------------------- */

.mob-menu {
  margin-bottom: 20px;
}

.mob-static-menu {
  background-image: url('../img/secondary_menu_bg.svg');
  background-size: 50% 50%;
  background-repeat: repeat-x;
  background-color: #216128;
  color: #fff;
}

/* menu-list
 * -------------------------------------------------------------------------- */

.menu-item {
  display: inline-block;
}
/* secondary-menu
 * -------------------------------------------------------------------------- */

.secondary-menu {
  background-image: url('../img/secondary_menu_bg.svg');
  background-repeat: repeat-x;
  background-color: #216128;
  background-size: auto 100%;
}

.secondary-menu {
  list-style-type: none;
}

.secondary-menu-list {
  margin-bottom: 0;
  text-align: center;
}

.secondary-menu-item {
  display: inline-block;
}

.secondary-menu-link {
  display: inline-block;
  padding: 23px 33px;
  text-decoration: none;
  text-transform: uppercase;
  border-bottom: 3px solid transparent;
  color: #fff;
  font-size: 21px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  line-height: 1;
}

.secondary-menu-link:focus,
.secondary-menu-link:hover,
.secondary-menu-link:active {
  color: #fff;
  text-decoration: none;
  border-bottom: 3px solid #fff;
}

/* Static mobile menu
 * -------------------------------------------------------------------------- */
.static-mobile-menu-list .secondary-menu-link {
  padding: 23px 5px;
  font-size: 15px;
}

/* logo
 * -------------------------------------------------------------------------- */

.logo-col {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.logo {
  display: inline-block;
  width: 200px;
  margin-top: 20px;
  margin-bottom: 20px;
}

@media (min-width: 768px) {
  .logo {
    position: absolute;
    top: 50%;
    margin-top: -27px;
    margin-bottom: 0;
  }
}

.logo-link {
  display: block;
}

@media (min-width: 768px) {
  .logo-col {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
}

/*
  .logo-img {
    width: 100%;
    height: 0;
    display: inline-block;
    background: url('../imgs/pindi_kinnisvara_logo.svg') no-repeat center center;
    -webkit-background-size: contain;
    -moz-background-size: contain;
    -o-background-size: contain;
    background-size: contain;
    min-height: 60px;
    width: 100%;
    padding-bottom: 50%;
  }

  .no-svg .logo-img {
    background-image: url('../img/pindi_kinnisvara_logo.png')
  }*/

/* NAV MISC
** ========================================================================== */

/* nav-misc
 * -------------------------------------------------------------------------- */

.nav-misc {
  margin-top: 10px;
  margin-bottom: 5px;
}

.nav-misc:after {
  content: '';
  display: table;
  clear: both;
}
/* lang
 * -------------------------------------------------------------------------- */

.lang {
  display: block;
  text-align: center;
}

@media (min-width: 768px) {
  .lang {
    float: right;
  }
}

.lang-item {
  display: inline-block;
}

.lang-link {
  padding: 0 6px;
  text-decoration: none;
  text-transform: uppercase;
  color: #a5a5a5;
  font-size: 12px;
}

/* search
 * -------------------------------------------------------------------------- */

.search {
  float: right;
}

.search-box {
  position: relative;
  display: inline-block;
  color: #a5a5a5;
}

.search-box:after {
  content: '';
  position: absolute;
  right: 0;
  left: 0;
  bottom: 0;
  height: 1px;
  background: #a5a5a5;
}

.search-box:before {
  content: '\f002';
  position: absolute;
  top: 0;
  right: 0;
  display: inline-block;
  width: 20px;
  height: 100%;
  margin-top: 4px;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  font: normal normal normal 12px/1 FontAwesome;
}

.search-box-input {
  background: transparent;
  border: none;
  font-size: 13px;
  width: 150px;
  padding-left: 3px;
  padding-right: 25px;
  outline: none;
  overflow: hidden;
}

.search-box-button {
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  opacity: 0;
}

/* MENU
** ========================================================================== */

/* menu-list
 * -------------------------------------------------------------------------- */

.menu-list {
  text-align: right;
}
/* menu-link
 * -------------------------------------------------------------------------- */

.menu-link {
  display: inline-block;
  padding: 9px 10px;
  text-decoration: none;
  font-size: 16px;
  color: #165528;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.menu-link:hover,
.menu-link:active,
.menu-link:focus,
.current_page_item .menu-link,
.current_page_ancestor .menu-link {
  background: #165528;
  color: #fff;
  text-decoration: none;
}

/* TOP-INTRO
** ========================================================================== */

/* top-intro-heading
 * -------------------------------------------------------------------------- */
.top-intro {
  position: relative;
}

.top-intro-heading {
  position: absolute;
  right: 30px;
  bottom: 50%;
  margin-top: 0;
  margin-bottom: -22.5px;
  line-height: 1;
  font-family: 'source_sans_proextralight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 41px;
  color: #fff;
  text-shadow: 0px 0px 5px rgba(180, 180, 180, 1);
}
/* top-intro-thumbnail
 * -------------------------------------------------------------------------- */

.top-intro-thumbnail {
  width: 100%;
  height: 150px;
  overflow: hidden;
}

.top-intro-img {
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  margin-top: 75px;
}

/* SERVICES
** ========================================================================== */

/* services
 * -------------------------------------------------------------------------- */

.services {
  position: relative;
  padding: 0 15px;
  margin-right: -15px;
  margin-left: -15px;
}

.services:after {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 1px;
  background: #d1d1d1;
}

.services.offer-services:after {
  background: transparent;
}

@media (min-width: 768px) {
  .services:after {
    content: '';
  }
}
/* services headings
 * -------------------------------------------------------------------------- */

.services-main-heading {
  color: #8ba693;
  font-size: 45px;
  font-family: 'source_sans_proextralight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  text-align: center;
}

@media (max-width: 767px) {
  .services-main-heading {
    margin-left: -15px;
    margin-right: -15px;
    border-bottom: 1px solid #d1d1d1;
  }
}

.services-secondary-heading {
  color: #165528;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 22px;
}
/* services-list
 * -------------------------------------------------------------------------- */

.services-list {
  list-style-type: none;
}

.services-list-item {
  position: relative;
  float: left;
  width: 50%;
}

.services-item:before {
  content: '';
  position: absolute;
  top: 50%;
  margin-top: -1px;
  left: 0;
  width: 4px;
  height: 4px;
  background: #8ba693;
}
/* services-links
 * -------------------------------------------------------------------------- */

.services-links {
  list-style-type: none;
}

.services-links-redirect {
  position: relative;
  margin-left: 10px;
  text-decoration: none;
  font-size: 15px;
  color: #165528;
  border-bottom: 1px solid #165528;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.services-links-redirect:before {
  content: '';
  position: absolute;
  top: 50%;
  left: -10px;
  margin-top: -1.5px;
  width: 4px;
  height: 4px;
  background: #8ba693;
}

.services-links-redirect:focus,
.services-links-redirect:hover,
.services-links-redirect:active {
  text-decoration: none;
  color: #165528;
}
/* services-user-added-content
 * -------------------------------------------------------------------------- */

@media (max-width: 767px) {
  .services-user-added-content {
    height: 200px;
    margin-bottom: 10px;
    overflow: hidden;
  }
  .services-user-added-content.open {
    height: auto;
    overflow: visible;
  }
}
/* services-content-hider
 * -------------------------------------------------------------------------- */

@media (max-width: 767px) {
  .services-content-hider {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding-top: 20px;
    padding-bottom: 20px;
    text-align: center;
    background: rgba(255, 255, 255, 0.6);
  }

  .services-content-hider.open {
    position: relative;
    padding-bottom: 0;
    background: none;
  }

  .no-rgba .services-content-hider {
    background: #fff;
  }
}


/* SLIDER
** ========================================================================== */

/* slider
 * -------------------------------------------------------------------------- */

.slider-wrap {
  margin-bottom: 10px;
  height: 300px;
  background-position: center center;
  background-repeat: no-repeat;
  color: #b70004;
}

.slider {
  background-image: var(--desktop);
  text-decoration: none;
  background-position: center center;
  background-repeat: no-repeat;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
}
@media (max-width: 768px) {
  .slider {
    background-image: var(--mobile);
  }
}

.slider:focus,
.slider:hover,
.slider:active {
  text-decoration: none;
}

.slider-bg {
  position: relative;
  background-color: rgba(255, 255, 255, 0.7);
}

.no-rgba .slider-bg {
  background-color: rgb(255, 255, 255);
}

.slider-bg:after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: -47px;
  height: 47px;
  background: url('../img/slider_pattern.svg') bottom center repeat-x;
  background-size: auto 100%;
  z-index: -1;
}

.no-svg .slider-bg:after {
  background-image: url('../img/slider_pattern.png');
}

.slider-slogan-main {
  margin-top: 10px;
  margin-bottom: 5px;
  font-size: 30px;
  color: #b70004;
}

@media (min-width: 500px) {
  .slider-slogan-main {
    font-size: 45px;
  }
}

.slider-slogan-secondary {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 22px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #404040;
}

@media (min-width: 500px) {
  .slider-slogan-secondary {
    font-size: 26px;
  }
}

.slider-buttons {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  display: block;
  text-align: center;
  z-index: 5;
}

.slider-buttons-btn {
  display: inline-block;
  height: 10px;
  width: 10px;
  margin-right: 5px;
  background: #fff;
  -webkit-transition: background-color 1500ms linear;
  -moz-transition: background-color 1500ms linear;
  -o-transition: background-color 1500ms linear;
  -ms-transition: background-color 1500ms linear;
  transition: background-color 1500ms linear;
  cursor: pointer;
}

.slider-buttons-btn.active,
.slider-buttons-btn.disabled {
  cursor: default;
}

.slider-buttons-btn.active {
  background: #507f5d;
}

/* SIDEBAR
** ========================================================================== */

/* menu-side
 * -------------------------------------------------------------------------- */

.menu-side {
  padding-left: 0;
  display: block;
}

.menu-side-item {
  display: block;
}

.menu-side-link {
  display: block;
  padding: 10px 10px;
  font-size: 15px;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
  text-decoration: none;
}

.menu-side-submenu .menu-side-submenu .menu-side-link {
  padding: 4px 10px;
  font-size: 14px;
}

.menu-side-link:focus,
  .menu-side-link:hover,
  .menu-side-link:active,
  .current-menu-parent > .menu-side-link,
  /*.menu-item-has-children.current_page_item > .menu-side-link*/
  .current_page_item > .menu-side-link {
  background: #165528;
  color: #fff;
  text-decoration: none;
}
/* submenu
 * -------------------------------------------------------------------------- */

/*Remove unneeded menu items*/

.menu-childs-only > .menu-side-item,
.menu-childs-only > .menu-side-item > .menu-side-link {
  display: none;
}

.menu-childs-only > .current-menu-item,
.menu-childs-only > .current-menu-ancestor {
  display: block;
}

.menu-subitems > .menu-side-item > .menu-side-link {
  display: none;
}
/* menu-side-submenu
 * -------------------------------------------------------------------------- */

.menu-side-submenu .menu-side-submenu {
  display: none;
  padding-left: 15px;
}

.current_page_item > .menu-side-link + .menu-side-submenu,
.current-menu-parent > .menu-side-link + .menu-side-submenu,
.menu-side-submenu .menu-side-link.active + .menu-side-submenu {
  display: block;
}

.menu-side-submenu .menu-side-submenu .menu-side-link {
  position: relative;
  padding-left: 15px;
}

.menu-side-submenu .menu-side-submenu .menu-side-link:focus,
.menu-side-submenu .menu-side-submenu .menu-side-link:hover,
.menu-side-submenu .menu-side-submenu .menu-side-link:active,
.menu-side-submenu .current-menu-parent .menu-side-submenu > .current_page_item .menu-side-link {
  background: transparent;
  color: #ca0000;
}

.menu-side-submenu .menu-side-submenu .menu-side-link:focus:before,
.menu-side-submenu .menu-side-submenu .menu-side-link:hover:before,
.menu-side-submenu .menu-side-submenu .menu-side-link:active:before,
.menu-side-submenu
  .current-menu-parent
  .menu-side-submenu
  > .current_page_item
  .menu-side-link:before {
  content: '\f054';
  position: absolute;
  top: 13px;
  left: 0;
  margin-top: -4.5px;
  font: normal normal normal 8px/1 FontAwesome;
  font-size: 9px;
  color: #ca0000;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}
/* stats
 * -------------------------------------------------------------------------- */

.stats-date {
  display: block;
  font-size: 14px;
  color: #8ba693;
}

.sidebar-rates {
  margin-top: 65px;
}

.sidebar-rates .link-button-secondary {
  margin-bottom: 20px;
}
/* factor
 * -------------------------------------------------------------------------- */

.factor-label,
.factor-data {
  line-height: 1;
  color: #165528;
}

.factor {
  margin-bottom: 15px;
}

.factor-label {
  font-size: 18px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.factor-data {
  text-decoration: none;
  position: relative;
  display: inline-block;
  padding-right: 20px;
  font-size: 30px;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.factor-data.factor-data-link {
  border-bottom: 2px solid transparent;
  color: #165528;
  -webkit-transition: border 0.2s ease-in-out;
  -moz-transition: border 0.2s ease-in-out;
  -ms-transition: border 0.2s ease-in-out;
  -o-transition: border 0.2s ease-in-out;
  transition: border 0.2s ease-in-out;
}

.factor-data.factor-data-link:focus,
.factor-data.factor-data-link:hover,
.factor-data.factor-data-link:active {
  border-bottom: 2px solid #165528;
  text-decoration: none;
}

.factor-data.descending:before,
.factor-data.ascending:before {
  position: absolute;
  top: 50%;
  right: 0;
  display: inline-block;
  margin-top: -10.5px;
  font: normal normal normal 21px/1 FontAwesome;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #e3212e;
}

.factor-data.descending:before {
  content: '\f107';
}

.factor-data.ascending:before {
  content: '\f106';
}

.factor-data-lg {
  font-size: 40px;
}

.factor-details {
  color: #8ba693;
  font-size: 13px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

/* FILTER
** ========================================================================== */

/* filter
 * -------------------------------------------------------------------------- */

.filter {
  position: relative;
  width: 100%;
  padding: 10px 20px 10px 20px;
  margin-bottom: 30px;
  background: #d5ded8;
}

.filter-button {
  display: inline-block;
  margin-left: 2px;
}

.filter-button .main-button {
  height: 34px;
  padding: 0 10px;
  line-height: 34px;
  font-size: 17px;
  border: none;
}

.filter-dropdown-menu {
  width: 100%;
  max-width: 100%;
  max-height: 200px;
  border-radius: 0;
  overflow-y: auto;
  overflow-x: hidden;
}

@media (min-height: 660px) {
  .filter-dropdown-menu {
    max-height: 300px;
  }
}

@media (min-height: 900px) {
  .filter-dropdown-menu {
    max-height: 500px;
  }
}
/* filter-group
 * -------------------------------------------------------------------------- */

.filter-group,
.filter-icon,
.filter-group-label,
.filter-group-input {
  display: inline-block;
}

.filter-group {
  display: table-cell;
}

.filter-dropdown {
  display: block;
}

.filter-dropdown-option {
  padding: 4px 0 4px 10px;
  cursor: pointer;
}

.filter-dropdown-toggle {
  width: 100%;
  height: 34px;
  border: 1px solid #7b9f86;
  border-radius: 0;
}

@media (min-width: 768px) {
  .filter-empty {
    /*width: 20%;*/
    opacity: 0;
    white-space: normal !important;
  }
  .filter-empty-element {
    max-height: 10px;
  }
}
/* filter-label
 * -------------------------------------------------------------------------- */
.filter-label {
  position: relative;
  display: table-cell;
  padding-right: 6px;
  padding-left: 5px;
  text-align: right;
  font-size: 15px;
  color: #165528;
  white-space: nowrap;
}

.filter-radio-btn {
  position: absolute;
  left: 0;
  border-radius: 100%;
  background: #fff;
  width: 5px;
  height: 5px;
}

.filter-radio-input:checked + .filter-radio-btn:before {
  content: '';
  position: absolute;
  top: 50%;
  bottom: 0;
  left: 50%;
  right: 0;
  width: 8px;
  height: 8px;
  margin-top: -4px;
  margin-left: -4px;
  border-radius: 100%;
  background: #165528;
}

.filter-radio-input {
  position: absolute;
  left: 0;
  opacity: 0;
  width: 5px;
}

.filter-label-inline {
  display: inline-block;
}

.filter-divider {
  display: inline-block;
  width: 4px;
}
/* filter-mini
 * -------------------------------------------------------------------------- */

.filter-mini {
  display: table;
  padding-left: 45px;
}

.filter-mini:after {
  content: '';
  clear: both;
  display: table;
}

.filter-mini:before {
  content: none;
  position: absolute;
  top: 50%;
  left: 15px;
  height: 30px;
  width: 30px;
  margin-top: -15px;
  background: url('../img/search_ico.svg') no-repeat;
  -webkit-background-size: contain;
  -moz-background-size: contain;
  -o-background-size: contain;
  background-size: contain;
}

@media (min-width: 768px) {
  .filter-mini:before {
    content: '';
  }
}

.filter-mini-group {
  padding-left: 5px;
  display: table-cell;
  white-space: nowrap;
}

.filter-mini-label,
.filter-mini-dropdown,
.filter-mini-input {
  display: inline-block;
}

.filter-mini-label {
  height: 35px;
  line-height: 35px;
  padding-right: 5px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 15px;
  color: #165528;
}

.filter-mini-input {
  padding: 0 5px;
  height: 34px;
  line-height: 34px;
  border: 1px solid #7b9f86;
  background: #fff;
  text-align: left;
}

.filter-mini-btn {
  position: relative;
  top: -1px;
  padding: 8px;
}
/* filter-input
 * -------------------------------------------------------------------------- */

.lg-filter-input {
  width: 68%;
}

@media (min-width: 992px) {
  .lg-filter-input {
    width: 70%;
  }
}

.lg-filter-input:placeholder,
.lg-filter-input:-webkit-input-placeholder,
.lg-filter-input:-moz-placeholder,
.lg-filter-input:-ms-input-placeholder {
  color: #8f8f8f;
}
/* filter-input-dropdown
 * -------------------------------------------------------------------------- */

.filter-input-dropdown {
  position: relative;
}

.filter-input-dropdown:before {
  content: '\f0dc';
  position: absolute;
  top: 50%;
  right: 10px;
  display: inline-block;
  margin-top: -9px;
  font: normal normal normal 18px/1 FontAwesome;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #b6c7bb;
}
/* filter-table
 * -------------------------------------------------------------------------- */

.filter-table.table > tbody > tr > td {
  vertical-align: bottom;
  white-space: nowrap;
}

.filter-table .filter-table-top td {
  border-right: 1px dotted #7b9f86;
}

.filter-table-group {
  display: inline-block;
}

.filter-table-group-half {
  width: 50px;
}

.filter-table-top td {
  border-left: 1px dotted #165528;
}

.filter-table-top td:first-child {
  border-left: none;
}

.filter-table-hidden {
  margin-bottom: 0;
}
/* filter-table td widths
 * -------------------------------------------------------------------------- */

.filter-table-top {
  margin-bottom: 0;
}

.filter-table-top.filter-table > tbody > tr > td {
  width: 16.6666%;
  white-space: nowrap;
}

.filter-table-bottom {
  width: 100%;
}

.filter-table-bottom.filter-table > tbody > tr > td {
  width: 33.3333%;
  white-space: nowrap;
}

.filter-table-bottom.filter-table > tbody > tr > td:first-child {
  width: 100%;
}

.filter-table-bottom.filter-table > tbody > tr > td:last-child {
  width: 100%;
}

.filter-table-bottom {
  margin-bottom: 10px;
  border-bottom: 1px solid #fff;
}

.filter.not-detail .filter-table-bottom {
  border-bottom: none;
}
/* filter-table-input
 * -------------------------------------------------------------------------- */

.filter-table-input {
  height: 40px;
  padding: 0 6px;
  border: 1px solid #7b9f86;
  line-height: 35px;
  background: #fff;
  border-radius: 0;
  color: #8f8f8f;
}

@media (min-width: 768px) {
  .filter-table-input {
    height: 35px;
    width: 65px;
  }
  .filter-table-input-lg.filter-table-input {
    width: 100%;
  }
}

@media (min-width: 768px) {
  .filter-table-input {
    width: 80px;
  }
}

.filter-table-input-full {
  width: 100%;
}

.filter-table-input-lg.filter-table-input {
  width: 100%;
}
/* filter-buttons
  * -------------------------------------------------------------------------- */

/* .filter-detailed-btn {
    height: 30px;
    line-height: 30px;
    padding: 0 8px;
    text-transform: uppercase;
    background: #165528;
    font-size: 13px;
    color: #fff;
    border:none;
  }

  .filter-detailed-btn:focus,
  .filter-detailed-btn:hover,
  .filter-detailed-btn:active,
  .filter-submit-btn:focus,
  .filter-submit-btn:hover,
  .filter-submit-btn:active {
    outline: none;
  }*/

.filter-submit-btn {
  position: relative;
  padding: 10px 10px 10px 30px;
  font-size: 20px;
  background: #165528;
  color: #fff;
  border: none;
  text-transform: uppercase;
}

.filter-submit-btn.filter-submit-btn-sm {
  padding-left: 10px;
}

@media (min-width: 768px) {
  .filter-submit-btn {
    padding: 8px 8px 8px 30px;
    font-size: 15px;
  }
}

.filter-submit-btn:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 8px;
  height: 18px;
  width: 18px;
  margin-top: -8.5px;
  background: url('../img/search_ico_white.svg') no-repeat;
  -webkit-background-size: contain;
  -moz-background-size: contain;
  -o-background-size: contain;
  background-size: contain;
}

.no-svg .filter-submit-btn:before {
  background-image: url('../img/search_ico_white.png');
}

.filter-submit-btn-sm:before {
  content: none;
}
/* filter-radio-btn
  * -------------------------------------------------------------------------- */

.filter-label {
  position: relative;
}

.filter-label-radio {
  padding-left: 20px;
}

.filter-radio-btn,
.filter-radio-input {
  position: absolute;
  left: 0;
  top: 0;
  height: 14px;
  width: 14px;
}

/*.filter-radio-btn {
    top: 4px;
    background: #fff;
    border-radius: 100%;
  }

  .filter-radio-btn[type="radio"]:checked + .filter-radio-btn:after {
    background: red;
    height: 6.5px;
    width: 6.5px;
    border-radius: 100%;
  }

  .filter-radio-input {
    opacity: 0;
  }*/
/* mob-filter-group
 * -------------------------------------------------------------------------- */

.mob-filter {
  margin: 0 -15px;
}

.mob-filter-btn {
  width: 100%;
  margin: 5px 0;
  padding: 12px 10px;
  border: none;
  background: #8ba693;
  color: #fff;
  font-size: 21px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.mob-filter-btn:focus,
.mob-filter-btn:hover,
.mob-filter-btn:active {
  outline: none;
}

.mob-filter-btn-txt {
  display: inline-block;
  position: relative;
  padding-left: 30px;
}

.mob-filter-btn-txt:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  height: 25px;
  width: 25px;
  margin-top: -12.5px;
  background: url('../img/search_ico_white.svg') no-repeat;
  -webkit-background-size: contain;
  -moz-background-size: contain;
  -o-background-size: contain;
  background-size: contain;
}

.no-svg .mob-filter-btn-txt:before {
  background-image: url('../img/search_ico_white.png');
}

.mob-filter-dropdown {
  display: none;
}

.mob-filter-dropdown.active {
  display: block;
  z-index: 101;
}

.mob-filter-group {
  position: relative;
  margin-bottom: 10px;
}

.mob-filter-group:after {
  content: '';
  clear: both;
  display: table;
}
.mob-filter-group .filter-divider {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -2px;
  width: 4px;
  text-align: center;
}
.mob-filter-group .filter-table-group {
  display: inline-block;
  width: 41%;
}

.mob-filter-group .filter-table-group-right {
  float: right;
}

.mob-filter-group .filter-table-input {
  width: 100%;
}

.mob-filter-group-wrap {
  margin-top: 20px;
}

/* FILTER DROPDOWN
** ========================================================================== */

/* dropdown-filter
 * -------------------------------------------------------------------------- */
@media (min-width: 768px) {
  .dropdown-filter {
    max-width: 80px;
  }
}

@media (min-width: 992px) {
  .dropdown-filter {
    max-width: 160px;
  }
}
/* dropdown-filter-btn
 * -------------------------------------------------------------------------- */

.dropdown-filter-btn {
  position: relative;
  height: 40px;
  width: 100%;
  margin-bottom: 10px;
  padding: 0 10px 0 10px;
  overflow: hidden;
  border: 1px solid #7b9f86;
  line-height: 35px;
  background: #fff;
  color: #8f8f8f;
  border-radius: 0;
  text-align: left;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-weight: normal;
  font-style: normal;
}

@media (min-width: 768px) {
  .dropdown-filter-btn {
    height: 35px;
    margin-bottom: 0;
  }
}

/*.dropdown-filter-btn:before,
	.dropdown-filter-btn:after {
			position: absolute;
			top: 50%;
			right: 8px;
			display: inline-block;
			color: #b6c7bb;
		  font: normal normal normal 14px/1 FontAwesome;
		  text-rendering: auto;
		  -webkit-font-smoothing: antialiased;
		  -moz-osx-font-smoothing: grayscale;
	}*/

/* .dropdown-filter-btn:before {
  	content: "\f0de";
  	margin-top: -9px;
  }

	.dropdown-filter-btn:after {
			content: "\f0dd";
			margin-top: -5px;
	}*/

.dropdown-filter-btn-icon {
  position: absolute;
  right: 0;
  height: 100%;
  width: 20px;
  vertical-align: middle;
  background: #fff;
}

.dropdown-filter-btn-icon:before,
.dropdown-filter-btn-icon:after {
  position: absolute;
  top: 50%;
  right: 8px;
  display: inline-block;
  color: #b6c7bb;
  font: normal normal normal 14px/1 FontAwesome;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.dropdown-filter-btn-icon:before {
  content: '\f0de';
  margin-top: -9px;
}

.dropdown-filter-btn-icon:after {
  content: '\f0dd';
  margin-top: -5px;
}

.dropdown-filter-btn-inline {
  min-width: 100%;
}

.front-dropdown-filter {
  display: table-cell;
}

/* MAIN-FILTER
** ========================================================================== */

/* main-filter
 * -------------------------------------------------------------------------- */

.main-filter:before {
  content: none;
}

/* CLARIFY
** ========================================================================== */

/* clarify
 * -------------------------------------------------------------------------- */
.clarify {
  margin-bottom: 20px;
}
/* clarify-search
 * -------------------------------------------------------------------------- */

.clarify-search {
  display: block;
  position: relative;
}

@media (min-width: 768px) {
  .clarify-search {
    display: inline-block;
  }
}

.clarify-search-input {
  width: 100%;
  padding-right: 50px;
  padding-left: 15px;
  border: 1px solid #8ba693;
  outline: none;
  line-height: 45px;
  height: 45px;
  font-size: 16px;
  color: #8ba693;
}

@media (min-width: 768px) {
  .clarify-search-input {
    height: auto;
    width: 120px;
    border: none;
    padding-right: 30px;
    padding-left: 0;
    line-height: 1;
    font-size: 13px;
  }
}

.clarify-search-input[placeholder] {
  color: #8ba693;
}

@media (min-width: 768px) {
  .clarify-search-input[placeholder] {
    color: #a5a5a5;
  }
}

.clarify-search-submit {
  position: absolute;
  top: 0;
  right: 0;
  opacity: 0;
  width: 50px;
  height: 100%;
  z-index: 9999;
}

@media (min-width: 768px) {
  .clarify-search-submit {
    width: 30px;
  }
}

.clarify-search-icon {
  position: absolute;
  top: 50%;
  right: 0;
  margin-top: -14px;
  font-size: 24px;
  width: 50px;
  height: 100%;
  text-align: center;
  color: #8ba693;
}

@media (min-width: 768px) {
  .clarify-search-icon {
    margin-top: -8px;
    width: 30px;
    font-size: 16px;
    color: #a5a5a5;
  }
}
/* clarify-dropdown
 * -------------------------------------------------------------------------- */

.clarify-dropdown {
  display: block;
  margin-bottom: 15px;
}

@media (min-width: 768px) {
  .clarify-dropdown {
    display: inline-block;
    margin-right: 10px;
    margin-bottom: 0;
  }
}

.clarify-dropdown-trigger {
  position: relative;
  width: 100%;
  line-height: 45px;
  height: 45px;
  padding: 0 30px 0 15px;
  background: transparent;
  border: 1px solid #8ba693;
  border-radius: 0;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #8ba693;
  font-size: 16px;
  outline: none;
  text-align: left;
}

.clarify-dropdown-option {
  padding: 5px;
  cursor: pointer;
}

.clarify-dropdown-trigger:hover,
.clarify-dropdown-trigger:focus,
.clarify-dropdown-trigger:active {
  outline: none;
}

@media (min-width: 768px) {
  .clarify-dropdown-trigger {
    width: auto;
    height: auto;
    padding-left: 0;
    border: none;
    line-height: 1;
    font-size: 13px;
  }
}

.clarify-dropdown-trigger:after {
  content: '\f0dc';
  position: absolute;
  top: 50%;
  right: 17px;
  margin-top: -8px;
  display: inline-block;
  font: normal normal normal 16px/1 FontAwesome;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #b6c7bb;
}

@media (min-width: 768px) {
  .clarify-dropdown-trigger:after {
    right: 8px;
  }
}

.clarify-dropdown-menu {
  max-height: 500px;
  min-width: 100%;
  overflow-y: auto;
  border-radius: 0;
}

/* FOOTER
** ========================================================================== */

/* footer
 * -------------------------------------------------------------------------- */

.footer {
  position: relative;
  margin-top: 80px;
  border-top: 2px solid #8ba693;
  background: #fff;
}

.page-front .footer {
  margin-top: 20px;
}

@media (min-width: 768px) {
  .footer {
    background: #165528;
  }
}

.footer:before {
  content: '';
  position: absolute;
  top: -101px;
  left: 0;
  right: 0;
  height: 101px;
  background: url('../img/footer_pattern.svg') bottom center repeat-x;
  background-size: auto 100%;
  z-index: -1;
}

.no-svg .footer:before {
  background-image: url('../img/footer_pattern.png');
}
/* footer-menu
 * -------------------------------------------------------------------------- */

.footer-menu {
  padding-top: 20px;
  background: #ededed;
}
/* menu-footer
 * -------------------------------------------------------------------------- */
.menu-footer-heading h4 {
  margin-bottom: 10px;
  text-transform: uppercase;
  font-size: 12px;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #3c3c3c;
}

.menu-footer-list {
  padding-left: 0;
}

.menu-footer-item {
  display: block;
  list-style-type: none;
  padding: 5px 0;
}

.menu-footer-link {
  display: inline;
  font-size: 13px;
  text-decoration: none;
  color: #626262;
  border-bottom: 1px solid transparent;
  transition: 0.3s;
  -o-transition: 0.3s;
  -ms-transition: 0.3s;
  -moz-transition: 0.3s;
  -webkit-transition: 0.3s;
}

.menu-footer-link:focus,
.menu-footer-link:hover,
.menu-footer-link:active {
  border-bottom: 1px solid #165528;
  color: #165528;
  text-decoration: none;
}
/* footer-contacts
 * -------------------------------------------------------------------------- */

.footer-logos {
  padding-top: 20px;
  padding-bottom: 20px;
}
/* contacts-footer
 * -------------------------------------------------------------------------- */

.contacts-footer-list {
  padding-left: 0;
}

.contacts-footer-item {
  display: block;
  margin-left: 15px;
  text-align: center;
  list-style-type: none;
  color: #8ba693;
}

.contacts-footer-item:first-child {
  margin-left: 0;
}

@media (min-width: 768px) {
  .contacts-footer-item {
    display: inline-block;
    text-align: left;
  }
}

.contacts-footer-item,
.contacts-footer-label {
  color: #4e885f;
}

@media (min-width: 768px) {
  .contacts-footer-item,
  .contacts-footer-label {
    color: #8ba693;
  }
}

.contacts-footer-link {
  color: #4e885f;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

@media (min-width: 768px) {
  .contacts-footer-link {
    color: #b2cfba;
    font-size: 13px;
  }
}

.contacts-footer-link:focus,
.contacts-footer-link:hover,
.contacts-footer-link:active {
  text-decoration: none;
  color: #4e885f;
  border-bottom: 1px solid #4e885f;
}

.contacts-footer-heading h4 {
  margin-top: 0;
  text-transform: uppercase;
  font-size: 12px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
  text-align: center;
}

@media (min-width: 768px) {
  .contacts-footer-heading h4 {
    text-align: left;
    color: #8ba693;
  }
}

.contacts-footer-label {
  margin-right: 5px;
}
/* footer-logo
 * -------------------------------------------------------------------------- */

.footer-logo-image {
  display: inline-block;
  background: url('../img/pindi_kinnisvara_logo_footer.svg') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 32px;
}

.no-svg .footer-logo-image {
  background-image: url('../img/pindi_kinnisvara_logo_footer.png');
}
/* footer-rights
 * -------------------------------------------------------------------------- */

.footer-rights {
  text-align: center;
  font-size: 12px;
  color: #4e885f;
}

/* CONTACT
** ========================================================================== */

/* contact headings
 * -------------------------------------------------------------------------- */

.contact-heading h2 {
  /*margin-top: 20px;*/
  margin-top: 0;
  text-align: center;
  color: #165528;
  font-size: 21px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

@media (min-width: 768px) {
  .contact-heading h2 {
    text-align: left;
  }
}
/* contact-info
 * -------------------------------------------------------------------------- */

.contact-info {
  float: right;
  width: 48%;
  text-align: left;
}

@media (min-width: 768px) {
  .contact-info {
    width: 70%;
  }
}

@media (min-width: 800px) {
  .contact-info {
    width: 70%;
  }
}

@media (min-width: 1200px) {
  .contact-info {
    width: 75%;
  }
}

.halfed-contact-info {
  width: 48%;
}

@media (min-width: 768px) {
  .halfed-contact-info {
    width: 58%;
  }
}

@media (min-width: 1200px) {
  .halfed-contact-info {
    width: 68%;
  }
}

.contact-info.secondary-contact-info {
  width: 65%;
}

@media (min-width: 992px) {
  .contact-info.secondary-contact-info {
    width: 74%;
  }
}

@media (min-width: 1200px) {
  .contact-info.secondary-contact-info {
    width: 78%;
  }
}

.secondary-contact-info .contact-details-item {
  position: relative;
  display: inline-block;
  margin-right: -3px;
  padding: 0 5px;
}

.secondary-contact-info .contact-details-item:first-child {
  padding-left: 0;
}

.secondary-contact-info .contact-details-item-link {
  display: block;
}

@media (min-width: 768px) {
  .secondary-contact-info .contact-details-item:before {
    content: '';
    height: 100%;
    width: 1px;
    background: #ddd;
    position: absolute;
    left: 0;
  }
}

.secondary-contact-info .contact-details-item:first-child:before,
.mailto-content-contact .contact-details-item:before {
  content: none;
}
.mailto-content-contact .secondary-contact-info .contact-details-item {
  padding: 0;
}

.mailto-content-contact {
  display: none;
}

@media (min-width: 768px) {
  .mailto-content-contact {
    display: block;
  }
}

.contact-info.contact-info-block {
  width: 100%;
  float: none;
}

.contact:after {
  content: '';
  display: table;
  clear: both;
}

.contact-info-name {
  color: #165528;
  font-size: 16px;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.contact-info-title {
  margin-bottom: 10px;
  color: #8ba693;
  font-size: 13px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
}
.contact-info-link {
  display: inline-block;
  color: #165528;
  font-size: 14px;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  border-bottom: none;
  text-decoration: none;
  text-transform: uppercase;
  border-bottom: 1px solid transparent;
  -webkit-transition: 0.3s ease;
  -moz-transition: 0.3s ease;
  -ms-transition: 0.3s ease;
  -o-transition: 0.3s ease;
  transition: 0.3s ease;
}
.contact-info-link:focus,
.contact-info-link:hover,
.contact-info-link:active {
  text-decoration: none;
  color: #165528;
  border-bottom: 1px solid #165528;
}

@media (max-width: 992px) {
  .contact-info-btn {
    padding-left: 5px;
    padding-right: 5px;
  }
}
/* contact-links
 * -------------------------------------------------------------------------- */

.contact-links {
  margin-top: 10px;
}

.contact-links-button {
  padding: 8px 10px;
}

.contact-link-wrap {
  position: relative;
  top: 10px;
  float: right;
}

@media (min-width: 1200px) {
  .contact-link-wrap {
    float: none;
    margin-top: 25px;
  }
}
/* contact-details
 * -------------------------------------------------------------------------- */

.contact-details {
  list-style-type: none;
}

.contact-details-item,
.contact-details-item-link {
  overflow: hidden;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
  font-size: 14px;
  text-decoration: none;
  display: block;
  text-overflow: ellipsis;
}

.contact-details-item-inline {
  display: inline-block;
}

.contact-details-item-link {
  display: inline-block;
  border-bottom: 1px solid transparent;
  -webkit-transition: 0.3s ease;
  -moz-transition: 0.3s ease;
  -ms-transition: 0.3s ease;
  -o-transition: 0.3s ease;
  transition: 0.3s ease;
}

.contact-details-item-link:focus,
.contact-details-item-link:hover,
.contact-details-item-link:active {
  text-decoration: none;
  color: #165528;
  border-bottom: 1px solid #165528;
}
/* contact-thumbnail
 * -------------------------------------------------------------------------- */

.contact-thumbnail {
  width: 48%;
  display: inline-block;
  text-align: right;
}

@media (min-width: 768px) {
  .contact-thumbnail {
    width: 30%;
    text-align: left;
  }
}

@media (min-width: 800px) {
  .contact-thumbnail {
    width: 30%;
  }
}

@media (min-width: 992px) {
  .contact-thumbnail {
    width: 27%;
  }
}

@media (min-width: 1200px) {
  .contact-thumbnail {
    width: 25%;
  }
}

@media (min-width: 768px) {
  .contact-thumbnail.secondary-contact-thumbnail .contact-thumbnail-img {
    width: 120px;
    height: 120px;
  }
}

@media (min-width: 992px) {
  .contact-thumbnail.secondary-contact-thumbnail .contact-thumbnail-img {
    width: 130px;
    height: 130px;
  }
}

.contact-thumbnail.secondary-contact-thumbnail {
  width: 33%;
}

@media (min-width: 992px) {
  .contact-thumbnail.secondary-contact-thumbnail {
    width: 25%;
  }
}

@media (min-width: 1200px) {
  .contact-thumbnail.secondary-contact-thumbnail {
    width: 20%;
  }
}

.halfed-contact-thumbnail {
  position: relative;
  top: 10px;
  width: 48%;
}

@media (min-width: 768px) {
  .halfed-contact-thumbnail {
    width: 40%;
  }
}

@media (min-width: 1200px) {
  .halfed-contact-thumbnail {
    width: 30%;
  }
}

.contact-thumbnail.contact-thumbnail-block {
  display: block;
  width: 100%;
}

.contact-thumbnail-img {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  border: 1px solid #95a696;
}

@media (min-width: 992px) {
  .contact-thumbnail-img {
    width: 105px;
    height: 105px;
  }
}
/* make-contact
 * -------------------------------------------------------------------------- */

.make-contact {
  display: none;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 6;
}

@media (min-width: 768px) {
  .make-contact {
    position: fixed;
  }
}

.make-contact.active {
  display: block;
}

.make-contact-lightbox {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: none;
  z-index: 99999999;
  background-color: #fff;
  opacity: 0.7;
  z-index: 4;
}

@media (min-width: 768px) {
  .make-contact-lightbox {
    display: block;
  }
}

.make-contact-box {
  position: absolute;
  top: 100%;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: 100%;
  min-height: 500px;
  margin: auto;
  z-index: 5;
  background-color: #fff;
  border: 1px solid #d1d1d1;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

@media (min-width: 768px) {
  .make-contact-box {
    top: 50%;
    width: 750px;
    min-height: 400px;
    height: 400px;
  }
}
/* contact-front
 * -------------------------------------------------------------------------- */

@media (max-width: 767px) {
  .contact-front {
    position: relative;
    padding: 10px 0 20px;
    margin-right: -15px;
    margin-left: -15px;
    background: #ededed;
  }

  .contact-front .contact-links-button {
    position: absolute;
    bottom: -15px;
  }
}

/* CONTACTS
** ========================================================================== */

/* table-contacts
 * -------------------------------------------------------------------------- */

@media (min-width: 768px) {
  .table-contacts tr:nth-child(even) {
    background: #f1f1f1;
  }
}

.table-contacts tbody tr td {
  width: 50%;
  border-top: none;
}

@media (max-width: 767px) {
  .table-contacts tr,
  .table-contacts th,
  .table-contacts td {
    display: block;
  }

  .table-contacts tbody tr td:nth-child(odd) {
    background: #f1f1f1;
  }

  .table-contacts > thead > tr > th {
    border-bottom: none;
  }

  .table-contacts tbody tr td {
    width: 100%;
  }
}
/* contacts-tabs
 * -------------------------------------------------------------------------- */

.contacts-heading {
  position: relative;
  margin-bottom: 0;
  text-align: center;
  color: #165528;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

@media (min-width: 992px) {
  .contacts-heading {
    text-align: left;
  }
}

@media (max-width: 767px) {
  .contacts-heading:after {
    content: '';
    position: absolute;
    right: -23px;
    bottom: 0;
    left: -38px;
    background: #d1d1d1;
    height: 2px;
  }
}

.contacts-tabs {
  display: block;
  margin-bottom: 0;
  list-style-type: none;
}

@media (min-width: 768px) {
  .contacts-tabs {
    /* display: inline-block; */
    float: right;
  }
}

@media (min-width: 768px) {
  .contacts-tabs-item:first-child {
    padding-left: 0;
  }
}

.contacts-tabs-item {
  display: block;
  margin: 20px 0;
  padding-left: 10px;
  color: #a2b8a9;
  font-size: 15px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  text-transform: uppercase;
  text-align: center;
  white-space: nowrap;
  cursor: pointer;
}

@media (min-width: 768px) {
  .contacts-tabs-item {
    display: inline-block;
    text-align: right;
    margin: 0 0;
  }
}

.contacts-tabs-item:focus,
.contacts-tabs-item:hover,
.contacts-tabs-item:active {
  color: #165528;
}

.contacts-tabs-small {
  display: block;
  float: none;
  text-align: center;
}

/* MAILTO
** ========================================================================== */

/* mailto
 * -------------------------------------------------------------------------- */
.mailto {
  text-align: left;
}
/* mailto-heading
 * -------------------------------------------------------------------------- */

.mailto-heading {
  position: relative;
  padding: 15px 20px;
  background-color: #165528;
}

.mailto-heading-header {
  position: relative;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 30px;
  margin-top: 0;
  margin-bottom: 0;
  color: #fff;
}
/* mailto-content-contact
 * -------------------------------------------------------------------------- */

.mailto-content-contact {
  width: 30%;
  float: left;
  padding: 20px;
}
/* mailto-content-form
 * -------------------------------------------------------------------------- */

.mailto-content-form {
  position: relative;
  width: 100%;
  float: left;
  padding: 10px 20px;
}

@media (min-width: 768px) {
  .mailto-content-form {
    width: 70%;
  }
}

.mailto-content-form.full-width {
  width: 100%;
}

.mailto-content-form:before {
  content: '';
  position: absolute;
  top: 5%;
  left: 0;
  background: #e9e9e9;
  height: 90%;
  width: 1px;
}

.mailto-content-form .main-button {
  float: right;
}

.mailto-label {
  margin-bottom: 2px;
  color: #165528;
  font-size: 15px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.mailto-form-group {
  width: 100%;
  margin-right: 1%;
}

@media (min-width: 768px) {
  .mailto-form-group {
    width: 49.5%;
    float: left;
  }
}

.mailto-form-group-last {
  margin-right: 0;
}

.mailto-form-input {
  width: 100%;
  padding: 0 10px;
  height: 30px;
  line-height: 30px;
  border: 1px solid #7b9f86;
  transition: border-color 0.4s ease-in-out;
}

.mailto-form-input.has-error {
  border-color: #e3212e;
}

.mailto-form-input:first-child {
  float: left;
}

.mailto-form-textarea {
  height: 150px;
  width: 100%;
  border: 1px solid #7b9f86;
  resize: none;
}

.mailto-button {
  padding: 5px 15px;
}

.mailto-close-button {
  z-index: 9999;
}

.mailto-contact-thumbnail {
  margin-bottom: 10px;
}

.mailto-form-group .js-error,
.mailto-form-group-full .js-error {
  height: 20px;
  display: block;
}

.mailto-form-after-submit-message {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  text-align: center;
  bottom: 0;
  display: inline-block;
  vertical-align: middle;
}

.mailto-form-after-submit-message:before {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  width: 1px;
  margin-left: -1px;
}

.mailto-form-after-submit-message span {
  font-size: 20px;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
}

/* SUBSCRIBE
** ========================================================================== */

/* subscribe
 * -------------------------------------------------------------------------- */

.subscribe-title {
  display: block;
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 19px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
}

.subscribe-info {
  display: block;
  margin-bottom: 8px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 15px;
  color: #165528;
}

.subscribe-join {
  margin-bottom: 25px;
  text-align: right;
}

.subscribe-join-input {
  margin-bottom: 10px;
  padding: 0 10px;
  height: 33px;
  width: 100%;
  line-height: 33px;
  border: 1px solid #8ba693;
  text-align: left;
  font-size: 15px;
  color: #165528;
}

.subscribe-join-input[placeholder] {
  color: #8ba693;
}

.main-button-larger {
  padding: 10px 17px;
  font-size: 13px;
}

.subscribe-social-icons {
  text-align: center;
}

@media (min-width: 768px) {
  .subscribe-social-icons {
    text-align: right;
  }
}

.subscribe-privacy {
  display: flex;
  text-align: left;
}

.privacy-checkbox {
  margin-right: 5px !important;
}

.page-default .subscribe-join {
  margin-top: 25px;
  text-align: left;
}

.page-default .subscribe-join button {
  margin-top: 10px;
}

/* NEWS
** ========================================================================== */

/* news-list
 * -------------------------------------------------------------------------- */

.news-list {
  list-style-type: none;
}

.news-item {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #d1d1d1;
}

.news-item-mini {
  padding-bottom: 0;
  border-bottom: none;
}

.news-link {
  display: block;
  text-decoration: none;
}

.news-link:focus,
.news-link:hover,
.news-link:active {
  text-decoration: none;
}

.news-link:focus .message-heading,
.news-link:hover .message-heading,
.news-link:active .message-heading {
  border-bottom: 1px solid #165528;
}

.message-heading.message-heading-mini:focus,
.message-heading.message-heading-mini:hover,
.message-heading.message-heading-mini:active {
  border-bottom: 1px solid #434343;
}
/* message
 * -------------------------------------------------------------------------- */

.message-date {
  display: block;
  color: #898989;
  font-size: 13px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.message-heading {
  display: inline-block;
  margin-bottom: 10px;
  border-bottom: 1px solid transparent;
  color: #165528;
  font-size: 20px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.message-heading-mini {
  margin: 0;
  color: #434343;
  font-size: 17px;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.message-intro {
  margin-bottom: 18px;
  font-size: 15px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #434343;
}

.message-intro-mini {
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
}
/* article
 * -------------------------------------------------------------------------- */

.article {
  position: relative;
  margin-bottom: 20px;
}

@media (min-width: 768px) {
  .main-article-button {
    position: absolute;
    top: -50%;
    right: 0;
  }
}

.article:after {
  content: '';
  display: table;
  clear: both;
}

.article-date {
  display: inline-block;
  color: #898989;
  font-size: 13px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

/* FIRM
** ========================================================================== */

/* firm-contact-block
 * -------------------------------------------------------------------------- */

.firm-contact-block:after {
  content: '';
  display: table;
  clear: both;
}
/* firm-contact-heading
 * -------------------------------------------------------------------------- */

.firm-contact-heading {
  margin-top: 0;
  font-size: 36px;
  color: #165528;
  font-family: 'source_sans_proextralight', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.firm-contact-heading-sm {
  font-size: 20px;
}
/* firm-contact-label/item
  * -------------------------------------------------------------------------- */

.firm-contact-label {
  width: 40%;
  padding-right: 12px;
  text-align: right;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.firm-contact-item {
  width: 60%;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

@media (min-width: 768px) {
  .firm-contact-primary .firm-contact-label {
    width: 30%;
  }

  .firm-contact-primary .firm-contact-item {
    width: 70%;
  }
  .firm-contact-secondary .firm-contact-label {
    width: 46.75%;
  }

  .firm-contact-secondary .firm-contact-item {
    width: 53.25%;
  }
}

.firm-contact-label,
.firm-contact-item {
  float: left;
  color: #434343;
  font-size: 15px;
}

.firm-contact-label-inline {
  width: 40%;
}

.firm-contact-item-inline {
  width: 60%;
}

@media (min-width: 768px) {
  .firm-contact-label-inline {
    width: 46%;
  }

  .firm-contact-item-inline {
    width: 54%;
  }
}

.firm-contact-item-link {
  text-decoration: none;
  color: #434343;
  border-bottom: 1px solid transparent;
  -webkit-transition: 0.3s ease;
  -moz-transition: 0.3s ease;
  -ms-transition: 0.3s ease;
  -o-transition: 0.3s ease;
  transition: 0.3s ease;
}

.firm-contact-item-link:focus,
.firm-contact-item-link:hover,
.firm-contact-item-link:active {
  text-decoration: none;
  color: #165528;
  border-bottom: 1px solid #165528;
}
/* firm-contact-link
 * -------------------------------------------------------------------------- */

.firm-contact-link {
  border-bottom: 1px solid transparent;
  text-decoration: none;
  color: #165528;
}

.firm-contact-link:focus,
.firm-contact-link:hover,
.firm-contact-link:active {
  -webkit-transition: 0.3s ease;
  -moz-transition: 0.3s ease;
  -ms-transition: 0.3s ease;
  -o-transition: 0.3s ease;
  transition: 0.3s ease;
  text-decoration: none;
  color: #165528;
  border-bottom: 1px solid #165528;
}

/* MAIN CONTACT
** ========================================================================== */

/* main-contact-company/regional-contact-general
  * -------------------------------------------------------------------------- */

.regional-contact-general {
  margin-bottom: 35px;
}

.main-contact-company,
.regional-contact-general {
  position: relative;
}

.regional-contact-general:before {
  position: absolute;
  right: 50%;
  background: #ced6e5;
  width: 1px;
  height: 100%;
}

@media (min-width: 768px) {
  .regional-contact-general:before {
    content: '';
  }
}

.company-person .contact-heading h2 {
  margin-top: 15px;
}

/* LOCATION
** ========================================================================== */

/* location-contact
  * -------------------------------------------------------------------------- */

.location-contact {
  margin-bottom: 20px;
}

.location-contact-front-page .em-content {
  display: none;
}

.location-contact-front-page .em-content.active {
  display: block;
}

.location-contact-single {
  display: none;
}

.location-contact-single.active {
  display: block;
}
/* change-counties
  * -------------------------------------------------------------------------- */

.change-counties-list {
  display: inline-block;
  list-style-type: none;
}

.change-counties-item {
  display: inline-block;
}

.change-counties-btn {
  position: relative;
  width: 44px;
  height: 44px;
  background: transparent;
  border-radius: 50%;
  border: 2px solid #165528;
}

.change-counties-prev:before,
.change-counties-next:before {
  display: inline-block;
  position: absolute;
  top: 50%;
  right: 0;
  left: 0;
  margin-top: -13px;
  color: #165528;
  font: normal normal normal 26px/1 FontAwesome;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.change-counties-prev:before {
  content: '\f104';
}

.change-counties-next:before {
  content: '\f105';
}
/* location-links
  * -------------------------------------------------------------------------- */
.location-links-link {
  position: relative;
  top: 8px;
}

/* LOCALE
** ========================================================================== */

/* locale-map
  --------------------------------------------------------------------------- */

.map-canvas {
  width: 100%;
  height: 0;
  padding-bottom: 25%; /* 16:10 ratio */
}

@media (max-width: 479px) {
  .map-canvas {
    padding-bottom: 100%; /* 1:1 ratio */
  }
}

/* Img fix */
.map-canvas img {
  max-width: none;
  width: auto;
}

/* Fallback */
.no-js .regional-contact-map {
  display: none;
}

.regional-contact-address {
  display: none;
}

.no-js .regional-contact-address {
  display: block;
}
/* locale-button
  --------------------------------------------------------------------------- */

.locale-button {
  position: relative;
  top: -15px;
  display: inline-block;
  padding: 2px 10px;
  background: #a2b8a9;
  text-transform: uppercase;
  text-decoration: none;
  font-size: 12px;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #fff;
}

.locale-button:focus,
.locale-button:hover,
.locale-button:active {
  text-decoration: none;
  color: #fff;
}
/* object-locale
    --------------------------------------------------------------------------- */

.object-locale-misc {
  position: absolute;
  top: 0;
  right: 2px;
}

.object-locale-button,
.object-locale-close {
  padding: 4px 10px;
}

.object-locale-button {
  position: relative;
  display: inline-block;
  float: none;
  background-color: #95a696;
  padding: 10px 40px;
}

@media (min-width: 768px) {
  .object-locale-button {
    float: left;
    padding: 4px;
    background-color: #165528;
  }
}

.object-locale-icon {
  display: block;
  padding: 9px;
  background-image: url('../img/search_ico_white.svg');
  background-repeat: no-repeat;
  -webkit-background-size: contain;
  -moz-background-size: contain;
  -o-background-size: contain;
  background-size: contain;
}

.object-locale-close {
  display: inline-block;
  margin-left: 5px;
}

/* FLAGS
** ========================================================================== */

/* flags-list
 * -------------------------------------------------------------------------- */

.flags-list {
  margin: 0;
  list-style-type: none;
}

.flag-item {
  display: inline-block;
  margin-right: 8px;
  width: 16px;
  height: 11px;
}
/* flags-images
 * -------------------------------------------------------------------------- */

.flag-et {
  background-image: url('../img/et.png');
}
.flag-en {
  background-image: url('../img/en_GB.png');
}
.flag-fi {
  background-image: url('../img/fi.png');
}
.flag-ru {
  background-image: url('../img/ru_RU.png');
}
.flag-it {
  background-image: url('../img/it.png');
}

/* HIRE
** ========================================================================== */

/* hire-icon
  --------------------------------------------------------------------------- */

.hire-employee {
  display: inline-block;
  width: 50%;
  text-align: center;
}

.hire-employee-wrap {
  position: relative;
  display: inline-block;
  width: 90px;
  height: 90px;
  border-radius: 50%;
  border: 1px solid #95a696;
}

.hire-employee-wrap-icon {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  margin-top: -35px;
  color: #d1d1d1;
  font-size: 70px;
}
/* hire-info
  --------------------------------------------------------------------------- */

.hire-info {
  float: right;
  width: 50%;
}

/* BROKERS
** ========================================================================== */

.brokers-header-heading {
  display: inline-block;
  vertical-align: middle;
  margin: 0 15px 0 0;
}
.brokers-header-title {
  display: inline-block;
  vertical-align: middle;
  font-size: 14px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #8ba693;
}

.brokers-header-button {
  float: right;
}

.brokers-employee-info-link {
  text-decoration: none;
  color: #165528;
  border-bottom: 1px solid transparent;
}

.brokers-employee-info-link:focus,
.brokers-employee-info-link:hover,
.brokers-employee-info-link:active {
  border-bottom: 1px solid #165528;
  color: #165528;
  text-decoration: none;
}
/* brokers-employee-info
  --------------------------------------------------------------------------- */

.brokers-employee-info {
  display: block;
  list-style-type: none;
}
.brokers-employee-info-item {
  display: block;
  font-size: 15px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
}
/* tabs
  --------------------------------------------------------------------------- */

.tabs {
  list-style-type: none;
  padding: 0;
  border-bottom: 1px solid #afafaf;
}

.tabs-item {
  position: relative;
  float: left;
  bottom: -1px;
  /* display: inline-block; */
  padding: 6px 10px;
  font-size: 20px;
  border-bottom: 1px solid transparent;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #afafaf;
  cursor: pointer;
}
.tabs-item:before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: -1px;
  width: 2px;
  height: 5px;
  background-color: #fff;
}
.tabs-item:first-child:before {
  content: none;
}
.tabs-item:focus,
.tabs-item:hover,
.tabs-item:active {
  border-bottom: 1px solid #165528;
  color: #165528;
}

/* OFFER-HEADER
** ========================================================================== */

/* offer-header
  --------------------------------------------------------------------------- */

.offer-header {
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 1px solid #d1d1d1;
}

.offer-header:after {
  content: '';
  display: table;
  clear: both;
}
/* offer-header-button
  --------------------------------------------------------------------------- */
.offer-header-button {
  float: left;
  height: 35px;
  padding: 0 10px;
  line-height: 35px;
}
/* dev-header-button
  --------------------------------------------------------------------------- */
.dev-header-button {
  float: right;
}
/* paginate
  --------------------------------------------------------------------------- */

.paginate {
  float: left;
  height: 35px;
  line-height: 35px;
  margin-left: 20px;
  color: #898989;
  font-size: 15px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.paginate-number {
  display: inline-block;
  line-height: 28px;
}
/* mob-paginate
  --------------------------------------------------------------------------- */

.mob-paginate {
  display: block;
  position: relative;
  height: 42px;
  line-height: 42px;
  margin-bottom: 40px;
  font-size: 18px;
  text-align: center;
}
.mob-paginate-button {
  height: 42px;
  width: 50px;
  background: #b5c0b6;
  color: #fff;
  font-size: 28px;
}

.mob-paginate-button:focus,
.mob-paginate-button:hover,
.mob-paginate-button:active {
  background: #165528;
  color: #fff;
  text-decoration: none;
}
/* paginate-button
  --------------------------------------------------------------------------- */

.paginate-button {
  color: #898989;
  font-size: 20px;
}

.paginate-button:focus,
.paginate-button:hover,
.paginate-button:active {
  color: #165528;
}

.paginate-next {
  float: right;
  margin-left: 8px;
}

.paginate-prev {
  float: left;
  margin-right: 8px;
}
/* offer-header-misc
  --------------------------------------------------------------------------- */

.offer-header-misc {
  float: right;
}
/* misc
  --------------------------------------------------------------------------- */

.misc {
  height: 35px;
  line-height: 35px;
  margin: 0;
  list-style-type: none;
}

.misc-item {
  margin-left: 5px;
  display: inline-block;
}

.misc-icon {
  font-size: 18px;
  color: #a2b8a9;
}

.misc-icon:focus,
.misc-icon:hover,
.misc-icon:active {
  text-decoration: none;
  color: #165528;
}

/* MAP
** ========================================================================== */

/* map
  --------------------------------------------------------------------------- */
.location-map {
  /*height: 0;*/
  /*padding-top: 50%;*/
  width: 100%;
  margin-bottom: 40px;
  /*background: url('../img/map_estonia.svg') no-repeat;*/
}

/* OFFER
** ========================================================================== */

/* offer
 * -------------------------------------------------------------------------- */
.offer {
  position: relative;
  margin-bottom: 30px;
  z-index: 99;
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  transition: all 0.5s ease;
}

@media (min-width: 768px) {
  .offer {
    margin-bottom: 0;
  }
}

.offer-link {
  display: block;
}

.offer-link:focus,
.offer-link:hover,
.offer-link:active {
  text-decoration: none;
  border-bottom: none;
}
/* offer-name
 * -------------------------------------------------------------------------- */

.offer-name {
  display: block;
  margin-bottom: 10px;
  padding-bottom: 10px;
  min-height: 55px;
  overflow: hidden;
  font-size: 18px;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  border-bottom: 1px solid #d9d9d9;
  color: #165528;
}

.lg-offer-name {
  font-size: 25px;
}

.offer-name-extra {
  font-size: 17px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #8ba693;
}
/* offer-booked
 * -------------------------------------------------------------------------- */
.offer-booked {
  display: block;
  text-transform: uppercase;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 15px;
  color: #ff3c00;
}
/* block-offer-name
 * -------------------------------------------------------------------------- */

.block-offer-name-link {
  display: inline-block;
  margin-top: 10px;
  font-size: 18px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
  border-bottom: 1px solid transparent;
}

.block-offer-name-link:focus,
.block-offer-name-link:hover,
.block-offer-name-link:active {
  color: #165528;
  text-decoration: none;
  border-bottom: 1px solid #165528;
}
/* offer-details
 * -------------------------------------------------------------------------- */

.offer-details {
  list-style-type: none;
}

.offer-details:after {
  content: '';
  display: table;
  clear: both;
}

.offer-details-item {
  display: inline-block;
  margin-right: 10px;
  color: #8ba693;
  font-size: 15px;
}

.offer-details-block:after {
  content: '';
  display: table;
  clear: both;
}

.dev-offer-details-block {
  width: 100%;
  padding: 0 10px;
}

@media (min-width: 992px) {
  .dev-offer-details-block {
    display: inline-block;
    width: 49%;
  }
}

.offer-details-label {
  text-align: right;
  padding-right: 10px;
}

.offer-details-label,
.offer-details-content {
  display: inline-block;
  width: 49%;
  font-size: 16px;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #434343;
  vertical-align: top;
}

.offer-details-content {
  padding-left: 10px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
}
/* offer-price
  * -------------------------------------------------------------------------- */

.offer-price {
  display: block;
  margin-top: 5px;
  /*margin-bottom: 5px;*/
  padding: 10px 20px 10px 0;
  border-top: 1px solid #d9d9d9;
  border-bottom: 3px solid #d9d9d9;
}

.offer-price-object {
  margin-top: 10px;
  margin-bottom: 30px;
  padding: 0;
}

@media (min-width: 768px) {
  .offer-price-object {
    margin-bottom: 0;
  }
}

.offer-price-bg {
  background: #eee;
  padding: 0;
}

.offer-price-amount {
  display: block;
  font-size: 35px;
  font-family: 'source_sans_probold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
}

.offer-price-amount.list-offer-price-amount {
  position: relative;
  padding: 2px 10px;
  font-size: 25px;
  color: #434343;
}

.list-offer-price-amount {
  position: relative;
}

.offer-price-favorite:after {
  content: '\f006';
  position: absolute;
  top: 50%;
  right: 10px;
  margin-top: -11px;
  display: inline-block;
  font: normal normal normal 21px/1 FontAwesome;
  color: #898989;
  cursor: pointer;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.offer-price-favorite.active:after {
  content: '\f005';
  color: #8ba693;
}

.offer-price-amount-object {
  display: none;
  margin-bottom: 10px;
  margin-top: 10px;
}

.offer-price-amount-object.active {
  display: block;
}

.offer-price-amount-button-wrap {
  display: block;
}

@media (min-width: 992px) {
  .offer-price-amount-button-wrap {
    /* display: inline-block; */
    float: right;
  }
}

.offer-price-amount-button {
  display: inline-block;
  padding: 5px 20px;
  background: #8ba693;
  border-radius: 0;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 13px;
  text-transform: uppercase;
  color: #fff;
}

@media (min-width: 768px) and (max-width: 991px) {
  .offer-price-amount-button {
    float: none;
  }
}

.offer-price-details-extra {
  display: inline-block;
  font-size: 15px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #898989;
}
/* offer-price-field-wrap/error
  * -------------------------------------------------------------------------- */
.offer-price-field-wrap {
  position: relative;
  display: block;
  width: 100%;
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  .offer-price-field-wrap {
    display: table-cell;
    width: 30%;
    padding: 30px 8px 40px 0;
  }
}

.offer-price-field-wrap-lg {
  width: 100%;
}

.offer-price-field-error {
  position: absolute;
  right: 0;
  left: 0;
}

@media (min-width: 768px) {
  .offer-price-field-error {
    bottom: 0;
  }
}
/* offer-content-heading
  * -------------------------------------------------------------------------- */

.offer-content-heading {
  color: #165528;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 22px;
}
/* offer-more
  * -------------------------------------------------------------------------- */

.offer-more:after {
  content: '';
  display: table;
  clear: both;
}

.offer-more-pics,
.offer-more-map {
  position: relative;
  padding: 0 14px 0 35px;
  display: inline-block;
  width: 49.5%;
  height: 35px;
  color: #fff;
  font-size: 13px;
  line-height: 35px;
  text-transform: uppercase;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  cursor: pointer;
}

.offer-more-pics {
  padding-left: 45px;
  float: left;
  background: #165528;
}

.offer-more-map {
  float: right;
  background: #8ba693;
}

.offer-more-pics:before,
.offer-more-map:before {
  position: absolute;
  top: 50%;
  left: 15px;
  margin-top: -9px;
  display: inline-block;
  font: normal normal normal 18px/1 FontAwesome;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.offer-more-pics:before {
  content: '';
  height: 17px;
  width: 17px;
  margin-top: -7.5px;
  background: url('../img/picture_ico.svg') no-repeat;
  -webkit-background-size: contain;
  -moz-background-size: contain;
  -o-background-size: contain;
  background-size: contain;
}

.no-svg .offer-more-pics:before {
  background-image: url('../img/picture_ico.png');
}

.offer-more-map:before {
  content: '\f041';
}
/* offer-contact
  * -------------------------------------------------------------------------- */
.offer-contact {
  margin: 20px 0;
  padding-bottom: 20px;
  border-top: none;
  border-bottom: none;
}

@media (min-width: 768px) {
  .offer-contact {
    border-top: 1px solid #d9d9d9;
    border-bottom: 1px solid #d9d9d9;
  }
}
/* table-offer
  * -------------------------------------------------------------------------- */

.table-offer > tbody > tr > td {
  vertical-align: middle;
  padding: 10px 10px 10px 20px;
}

.table-offer > thead > tr > th {
  border-bottom: 3px double #939393;
}
/* offer-price-send
  * -------------------------------------------------------------------------- */

.offer-price-send {
  display: table-row;
}

.offer-price-send-input {
  height: 36px;
  width: 100%;
  padding: 0 10px;
  vertical-align: middle;
  line-height: 34px;
  border: 1px solid #7b9f86;
}

.offer-price-send-input-lg {
  width: 100%;
}

.offer-price-sent-button,
.offer-price-send-button {
  position: relative;
  padding: 5px 20px;
  background: #8ba693;
  border-radius: 0;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 13px;
  text-transform: uppercase;
  color: #fff;
}

.offer-price-send-button {
  display: table-cell;
}

.offer-price-sent-button {
  /* display: inline-block; */
  float: right;
}

.offer-price-sent-message {
  font-size: 18px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
}

.offer-price-sent,
.offer-price-send {
  display: none;
  margin-top: 25px;
  margin-bottom: 25px;
}

.offer-price-sent.active,
.offer-price-send.active {
  display: block;
}

@media (min-width: 768px) {
  .offer-price-send.active {
    display: table-row;
  }
}
/* offer-thumbnail
  * -------------------------------------------------------------------------- */

.offer-thumbnail {
  position: relative;
  display: inline-block;
  overflow: hidden;
  width: 100%;
  height: 0;
  padding-top: 57%;
}

.offer-thumbnail-wrap {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.offer-thumbnail-img-wrap {
  width: 100%;
  height: 100%;
}

.object-fit-img-wrap {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center center;
}

.offer-thumbnail-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/*.no-objectfit .offer-thumbnail-img {
    opacity: 0;
  }*/

.offer-thumbnail-booked {
  position: absolute;
  left: 0;
  right: 0;
  padding: 11px 3px;
  text-transform: uppercase;
  color: #fff;
  opacity: 0.8;
  font-size: 14px;
  line-height: 1.2;
  background-color: #ff3c00;
  text-align: center;
}

.offer-thumbnail-info {
  position: absolute;
  bottom: 5px;
  left: 7px;
  margin-right: 7px;
  padding: 2px 8px;
  color: #fff;
  background-color: rgba(21, 78, 31, 0.8);
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 12px;
  text-transform: uppercase;
}

.no-rgba .offer-thumbnail-info {
  background-color: #154e1f;
}
/* offer-content-text
  * -------------------------------------------------------------------------- */

/*.offer-content-text {
    text-transform: capitalize;
  }*/
/* block-offer
  * -------------------------------------------------------------------------- */

.block-offer-list tr:nth-child(6n + 4),
.block-offer-list tr:nth-child(6n + 5),
.block-offer-list tr:nth-child(6n + 6) {
  background: #f3f3f3;
}

.table-offer .block-offer-details-row {
  padding-top: 0;
  padding-bottom: 0;
}

.table-offer > tbody > .block-offer-details-row > td {
  padding-top: 0;
  padding-bottom: 0;
}

.block-offer-price-amount {
  color: #434343;
  font-size: 25px;
  font-family: 'source_sans_probold', 'Helvetica Neue', Helvetica, arial, sans-serif;
}
/* block-offer-thumbnail
  * -------------------------------------------------------------------------- */

.block-offer-img-td {
  width: 30%;
  padding-left: 0 !important;
}

.block-offer-thumbnail {
  position: relative;
  display: inline-block;
  overflow: hidden;
  width: 100%;
  height: 0;
  padding-top: 60%;
}

.block-offer-thumbnail-img-wrap {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
}

.block-offer-thumbnail-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/*.no-objectfit .block-offer-thumbnail-img {
    opacity: 0;
  }*/
/* block-offer-content-text
  * -------------------------------------------------------------------------- */

.block-offer-content-text {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 16px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #434343;
}
/* offer-no-found
 * -------------------------------------------------------------------------- */

.offer-no-found {
  margin-top: 40px;
  font-size: 21px;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
}

/* GALLERY
** ========================================================================== */

/* gallery
 * -------------------------------------------------------------------------- */

/*.offer-gallery {
    position: fixed;
    z-index: 999;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0%;
    background: #fff;
  }
  .offer-gallery-yolo {
    position: absolute;
    top: 50px;
    bottom: 50px;
    width: 500px;
    margin: auto;
    background: red;
  }*/

/*.gallery-title {
		background-color: #165528;
		vertical-align: middle;
	}

	.gallery-title-heading {
    margin-bottom: 2px;
    padding-left: 21px;
    padding-right: 52px;
    padding-top: 15px;
    padding-bottom: 15px;
    font-family: "source_sans_proextralight", "Helvetica Neue", Helvetica, arial, sans-serif;
    font-size: 36px;
    color: #fff;
	}

  .offer-images{
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }

  .offer-images.active{
    display: block;
  }

  .lightbox {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
    background-color: #fff;
    opacity: 0.7;
  }

  .gallery{
    position: absolute;
    top: 10%;
    bottom: 10%;
    left: 0;
    right: 0;
    width: 800px;
    margin: auto;
    z-index: 2;
  }

  .gallery-offer-name-extra {
  	display: inline-block;
  	vertical-align: middle;
  	margin-bottom: 0;
  }*/
/* gallery-images-img-wrap
 * -------------------------------------------------------------------------- */

/*.gallery-images {
		position: relative;
	}

	.gallery-images-bg {
		background:#d5e3d6;
		position:absolute;
		left: 0;
		right: 15%;
		top: 0;
		bottom: 0;
    overflow: hidden;
	}

	.gallery-images-img-wrap {
		position: relative;
		height: 0;
		padding-bottom: 56.528%;
	}

  .gallery-images-img-wrap-abs {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
  }

  .gallery-images-img {
    position: absolute;
    left: 0;
    right: 15%;
    top: 0;
    bottom: 0;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: 50% 50%;
  }*/
/* gallery-list
 * -------------------------------------------------------------------------- */

/*.gallery-images-list {
		margin-bottom: 0;
		padding-bottom: 0;
		list-style-type: none;
	}

	.gallery-images-list-wrap {
	 	position: absolute;
	 	left: 85%;
	 	right: 0;
	 	top: 0;
	 	bottom: 0;
	 	margin-left: 5px;
	}

	.gallery-images-item-wrap-abs {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
	}

	.gallery-images-item-wrap {
		position: relative;
		height: 0;
		padding-bottom: 76%;
		margin-bottom: 5px;
	}

	.gallery-images-item-wrap-abs {
	position: absolute;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
	}

	.gallery-images-item {
		width: auto;
		height: 100%;
		max-width: none;
		padding-top: 0;
		-webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
		background-position: 50% 50%;
		background-repeat: no-repeat;
	}

  .gallery-images-item-wrap {
    border:2px solid transparent;
  }

  .gallery-images-item-wrap.active {
    border:2px solid #165528;
  }

	.gallery-close-button {
		position: absolute;
		right: 0;
		top: 50%;
		height: 15px;
		width: 15px;
		margin-top: -10px;
		margin-right: 21px;
		color: #fff;
		cursor: pointer;
	}

	.gallery-close-button:before,
	.gallery-close-button:after {
		content: "";
		position: absolute;
		top: 50%;
		height: 3px;
		width: 100%;
		background: #fff;
	}

	.gallery-close-button:before {
		-webkit-transform: rotate(-45deg);
		-moz-transform: rotate(-45deg);
		transform: rotate(-45deg);
	}

	.gallery-close-button:after {
		-webkit-transform: rotate(45deg);
		-moz-transform: rotate(45deg);
		transform: rotate(45deg);
	}*/
/* gallery-title
 * -------------------------------------------------------------------------- */

/*.gallery-title {
  	position: relative;
  }*/
/* gallery-btn
 * -------------------------------------------------------------------------- */

/*.gallery-images-misc {
    position: absolute;
    display: block;
    left: 0;
    right: 0;
    bottom: 2%;
    z-index: 99999;
    text-align: center;
    margin-bottom: 0;
    color: #fff;
  }

  .gallery-images-misc-item {
    display: inline-block;
    position: relative;
    height: 50px;
    line-height: 50px;
    width: 80px;
    background: rgba(22, 85, 40, 0.8);
  }

  .no-rgba .gallery-images-misc-item {
    background: #165528;
  }

  .gallery-counter {
    position: absolute;
    text-align: center;
    left: 0;
    right: 0;
  }

  .gallery-btn {
    display: block;
    position: relative;
    height: 100%;
    cursor: pointer;
  }

  .gallery-btn:before,
  .gallery-btn:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 42%;
    width: 16px;
    height: 2px;
    background: #fff;
  }

  .gallery-btn.gallery-btn-prev:before {
    margin-top: 5px;
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    transform: rotate(45deg);
  }

  .gallery-btn.gallery-btn-prev:after {
    margin-top: -5px;
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    transform: rotate(-45deg);
  }

  .gallery-btn.gallery-btn-next:before {
    margin-top: 5px;
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    transform: rotate(-45deg);
  }

  .gallery-btn.gallery-btn-next:after {
    margin-top: -5px;
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    transform: rotate(45deg);
  }*/

/* searching
** ========================================================================== */

/* searching-input
 * -------------------------------------------------------------------------- */

.searching-input {
  height: 34px;
  line-height: 34px;
  border: 1px solid #7b9f86;
}

/* THUMBNAILS
** ========================================================================== */

/* thumbnails-main
 * -------------------------------------------------------------------------- */

/*.thumbnails-main {
 	position: relative;
  height: 300px;
  margin-bottom: 5px;
  border:3px solid #165528;
 }*/

/*@media(min-width: 992px) {
  .thumbnails-main {
    height: 365px;
  }
 }*/

.thumbnails-main {
  position: relative;
  width: 100%;
  padding-bottom: 76%;
  margin-bottom: 5px;
  height: 0;
  float: left;
  border: 3px solid #165528;
  overflow: hidden;
}

.thumbnails-main-abs {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
}

.thumbnails-main-img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

/*.no-objectfit .thumbnails-main-img {
    opacity: 0;
 }*/
/* thumbnails-block
 * -------------------------------------------------------------------------- */

.thumbnails-block {
  margin-bottom: 15px;
}

.thumbnails-block:after,
.thumbnails-block:before {
  content: '';
  display: table;
  clear: both;
}

.thumbnails-block-item {
  position: relative;
  width: 24.25%;
  padding-bottom: 23.33%;
  margin-left: 1%;
  height: 0;
  float: left;
  border: 1px solid #165528;
  overflow: hidden;
}

.thumbnails-block-item-wrap {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.thumbnails-block-item:first-child {
  margin-left: 0;
}

/* FEATURED
** ========================================================================== */

/* featured
 * -------------------------------------------------------------------------- */

.featured-link-button {
  font-size: 15px;
  color: #165528;
}
/* featured-residences
 * -------------------------------------------------------------------------- */
.featured-residences {
  display: none;
  opacity: 0;
  margin-bottom: 30px;
}

.featured-residences.active {
  display: block;
  opacity: 1;
}
/* featured-block
 * -------------------------------------------------------------------------- */

.featured-block {
  margin-bottom: 30px;
}

.featured-block:after {
  content: '';
  display: table;
  clear: both;
}
/* featured-trigger/featured-switch
 * -------------------------------------------------------------------------- */

.featured-switch {
  margin-bottom: 25px;
  padding-top: 10px;
}

.featured-switch:after {
  content: '';
  display: table;
  clear: both;
}

.featured-trigger {
  position: relative;
  width: 47.7%;
  float: right;
  color: #fff;
  font-size: 16px;
  padding: 8px 5px;
  background: #8ba693;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  text-transform: uppercase;
  cursor: pointer;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;
  overflow: hidden;
  -webkit-transition: all 1s ease;
  -moz-transition: all 1s ease;
  -o-transition: all 1s ease;
  -ms-transition: all 1s ease;
  transition: all 1s ease;
}

@media (min-width: 768px) {
  .featured-trigger {
    width: 49.7%;
    color: #8ca693;
    font-size: 14px;
    background: transparent;
    text-align: left;
  }
}

@media (min-width: 992px) {
  .featured-trigger {
    font-size: 18px;
  }
}

.featured-trigger:after {
  position: absolute;
  right: 0;
  left: 0;
  bottom: 0;
  background: #8ca693;
  height: 3px;
  -webkit-transition: background 1s ease;
  -moz-transition: background 1s ease;
  -o-transition: background 1s ease;
  -ms-transition: background 1s ease;
  transition: background 1s ease;
}

@media (min-width: 768px) {
  .featured-trigger:after {
    content: '';
  }
}

.featured-trigger.active {
  background: #165528;
}

@media (min-width: 768px) {
  .featured-trigger.active {
    color: #165528;
    background: transparent;
  }
}

.featured-trigger.active:after {
  background: #165528;
}

.featured-trigger:first-child {
  float: left;
}
/* featured-products
 * -------------------------------------------------------------------------- */

.featured-products {
  margin-top: 30px;
  padding-top: 20px;
  padding-bottom: 20px;
}

@media (min-width: 768px) {
  .bg-featured-products {
    background: #ededed;
  }
}
/* devprojects
 * -------------------------------------------------------------------------- */

.devprojects {
  position: relative;
  padding-bottom: 4px;
  margin-left: -15px;
  margin-right: -15px;
  margin-bottom: 20px;
  border-bottom: 1px solid #d1d1d1;
}

@media (min-width: 768px) {
  .devprojects {
    margin-left: 0;
    margin-right: 0;
  }
}

.devprojects:after {
  content: '';
  display: table;
  clear: both;
}

.devprojects-heading {
  display: inline-block;
  padding-right: 0;
  font-size: 25px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
  text-align: center;
}

a.devprojects-heading:hover {
  color: #165528;
  text-decoration: none;
}

@media (min-width: 768px) {
  .devprojects-heading {
    padding-right: 50px;
    margin: 0;
    text-align: left;
  }
}

.devprojects-link {
  position: relative;
  top: 9px;
  padding-right: 15px;
  border-bottom: 1px solid transparent;
  color: #165528;
  font-size: 13px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  text-decoration: none;
  -o-transition: 0.5s;
  -ms-transition: 0.5s;
  -moz-transition: 0.5s;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

.devprojects-link:focus,
.devprojects-link:hover,
.devprojects-link:active {
  color: #165528;
  text-decoration: none;
  border-bottom: 1px solid #165528;
}

@media (min-width: 992px) {
  .devprojects-link {
    /*top: -18px;*/ /*- height + margin-bottom*/
    float: right;
  }
}

.front-devprojects-link {
  top: 0;
}

.devprojects-link:after {
  content: '\f054';
  position: absolute;
  top: 50%;
  right: 0;
  border-bottom: 1px solid transparent;
  margin-top: -4.5px;
  font: normal normal normal 8px/1 FontAwesome;
  font-size: 9px;
  color: #cbd7cf;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}
/* project
 * -------------------------------------------------------------------------- */

.project {
  display: block;
  text-decoration: none;
}

.project:focus .project-name,
.project:hover .project-name,
.project:active .project-name {
  color: #434343;
  border-bottom: 1px solid #434343;
}

.project-thumbnail {
  width: 100%;
  height: 0;
  padding-bottom: 86.7%;
  overflow: hidden;
  display: block;
  position: relative;
  margin-bottom: 10px;
}

.project-thumbnail-abs {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.project-thumbnail-img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

/*.no-objectfit .project-thumbnail-img {
    opacity: 0;
  }*/

  .project-thumbnail-info {
  position: absolute;
  bottom: 5px;
  left: 7px;
  margin-right: 7px;
  padding: 2px 8px;
  color: #fff;
  text-transform: uppercase;
  background-color: rgba(21, 78, 31, 0.8);
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 12px;
}

.no-rgba .project-thumbnail-info {
  background-color: #154e1f;
}

.project-name {
  display: inline-block;
  color: #434343;
  border-bottom: 1px solid transparent;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 18px;
  -webkit-transition: 0.3s ease;
  -moz-transition: 0.3s ease;
  -ms-transition: 0.3s ease;
  -o-transition: 0.3s ease;
  transition: 0.3s ease;
}

.project-name-link {
  text-decoration: none;
  color: #434343;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 18px;
  border-bottom: 1px solid transparent;
  -webkit-transition: 0.5s ease;
  -moz-transition: 0.5s ease;
  -ms-transition: 0.5s ease;
  -o-transition: 0.5s ease;
  transition: 0.5s ease;
}

.project-name-link:hover,
.project-name-link:focus,
.project-name-link:active {
  text-decoration: none;
  color: #434343;
  border-bottom: 1px solid #434343;
}

/* PROPERTY
** ========================================================================== */

/* property
  * -------------------------------------------------------------------------- */

.property {
  position: relative;
}

.property:after {
  content: '';
  display: table;
  clear: both;
}
/* property-info
  * -------------------------------------------------------------------------- */

.property-info {
  float: right;
  width: 48.7%; /*Same as feature-trigger width*/
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.property-info-name {
  /*display: inline-block;
    height: 58px;*/
  overflow: hidden;
  font-size: 15px;
  color: #434343;
  white-space: nowrap;
  border-bottom: 1px solid transparent;
  -webkit-transition: 0.3s ease;
  -moz-transition: 0.3s ease;
  -ms-transition: 0.3s ease;
  -o-transition: 0.3s ease;
  transition: 0.3s ease;
}

.property-info-name:focus,
.property-info-name:hover,
.property-info-name:active {
  color: #434343;
  text-decoration: none;
  border-bottom: 1px solid #434343;
}

@media (min-width: 1170px) {
  .property-info-name {
    white-space: normal;
    text-overflow: clip;
  }
}

.property-info-price {
  font-size: 25px;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (min-width: 768px) {
  .property-info-price {
    white-space: normal;
    text-overflow: clip;
  }
}

@media (min-width: 992px) {
  .property-info-price {
    position: absolute;
    bottom: 5px;
  }
}

.property-info-extra {
  font-size: 12px;
  color: #898989;
}

@media (max-width: 1199px) {
  .property-info-extra {
    display: block;
  }
}
/* property-thumbnail
  * -------------------------------------------------------------------------- */

.property-thumbnail-link {
  position: relative;
  display: inline-block;
  overflow: hidden;
  width: 48.7%;
  height: 0;
  padding-top: 38%;
}

.property-thumbnail {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
}

.property-thumbnail-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-imagefit .property-thumbnail-img {
  opacity: 0;
}
/* property-thumbnail-text
  * -------------------------------------------------------------------------- */

.property-thumbnail-text {
  position: absolute;
  bottom: 5px;
  left: 7px;
  padding: 2px 8px;
  color: #fff;
  background-color: rgba(21, 78, 31, 0.8);
  text-transform: uppercase;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 12px;
}

.no-rgba .property-thumbnail-text {
  background-color: #154e1f;
}
/* property-button
  * -------------------------------------------------------------------------- */

@media (max-width: 767px) {
  .property-button-wrap {
    position: absolute;
    bottom: 3px;
  }
}

.property-button {
  padding: 5px 10px 3px 10px;
  color: #fff;
  /*border-bottom: 2px solid transparent;*/
  white-space: nowrap;
}

.property-button:hover,
.property-button:focus,
.property-button:active {
  /*border-bottom: 2px solid #fff;*/
  color: #d5ded8;
  text-decoration: none;
}

/* EMPLOY
** ========================================================================== */

/* employ
 * -------------------------------------------------------------------------- */

.employ {
  margin-top: 15px;
}
/* employ-heading
 * -------------------------------------------------------------------------- */

.employ-heading {
  display: block;
  text-align: center;
  color: #165528;
  font-family: 'source_sans_proextralight', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.employ-secondary-heading {
  margin: 0;
}

.employ-secondary-heading-link {
  color: #165528;
  font-size: 20px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  text-decoration: none;
}
.employ-secondary-heading-link:focus,
.employ-secondary-heading-link:hover,
.employ-secondary-heading-link:active {
  border-bottom: 1px solid #165528;
  text-decoration: none;
  color: #165528;
}
/* employ-date
 * -------------------------------------------------------------------------- */

.employ-date {
  margin-bottom: 12px;
  text-align: center;
}

.employ-date-list {
  list-style-type: none;
}

.employ-date-item {
  position: relative;
  display: inline-block;
  padding: 0 10px;
  color: #898989;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 15px;
}

.employ-date-item:first-child {
  padding-left: 0;
}

.employ-date-item:before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 1px;
  background: #898989;
}

.employ-date-item:first-child:before {
  content: none;
}
/* employ-thumbnail
  * -------------------------------------------------------------------------- */

.employ-thumbnail {
  width: 100%;
  height: 150px;
  overflow: hidden;
}

.employ-thumbnail-img {
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  margin-top: 75px;
}

/* employ-fb-share
  * -------------------------------------------------------------------------- */
.employ-fb-share {
  margin-top: 10px;
}

/* JOBS
** ========================================================================== */

/* jobs
 * -------------------------------------------------------------------------- */

.jobs {
  margin-top: 20px;
  list-style-type: none;
}

.jobs-item {
  margin-bottom: 20px;
  padding-bottom: 5px;
  border-bottom: 1px solid #d1d1d1;
}

/* FAVOURITE
** ========================================================================== */

/* favourited-items-btn
 * -------------------------------------------------------------------------- */

.favourited-items-btn {
  display: none;
  position: fixed;
  top: 50%;
  right: 0;
  height: 80px;
  width: 80px;
  border-radius: 100%;
  border: 1px solid #fff;
  z-index: 100;
  background-color: #165528;
  color: #fff;
  cursor: pointer;
  -webkit-box-shadow: 0px 0px 11px 0px rgba(0, 0, 0, 0.29);
  -moz-box-shadow: 0px 0px 11px 0px rgba(0, 0, 0, 0.29);
  box-shadow: 0px 0px 11px 0px rgba(0, 0, 0, 0.29);
}

@media (min-width: 1200px) {
  .favourited-items-btn {
    right: 10%;
  }
}

.favourited-items-btn:before,
.favourited-items-btn:after {
  content: '\f005';
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  display: inline-block;
  font: normal normal normal 30px/1 FontAwesome;
  line-height: 1;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.favourited-items-btn:before {
  top: -16px;
  color: #fff;
  font-size: 32px;
}

.favourited-items-btn:after {
  top: -12px;
  color: #165528;
  font-size: 25px;
}

.favourited-items-btn.active {
  display: block;
}

.favourited-count,
.favourited-text {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  text-align: center;
}

.favourited-count {
  margin-top: -20px;
}

.favourited-text {
  margin-top: -5px;
}

.favorited-icon {
  content: '\f006';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* RESULT
** ========================================================================== */

/* result
 * -------------------------------------------------------------------------- */

.result-list {
  list-style-type: none;
}

.result-item {
  border-top: 1px solid #d1d1d1;
}

.result-item:first-child {
  border-top: none;
}

.result-item-link {
  border-bottom: 1px solid transparent;
  text-decoration: none;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
}

.result-item-link:hover,
.result-item-link:focus,
.result-item-link:active {
  color: #165528;
  border-bottom: 1px solid #165528;
}

.result-header-heading {
  display: inline-block;
  margin-bottom: 10px;
  border-bottom: 1px solid transparent;
  color: #165528;
  font-size: 20px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.result-item-content {
  color: #434343;
}

.result-header-found {
  float: right;
}

/* PRODUCTS
** ========================================================================== */

/* products-content
 * -------------------------------------------------------------------------- */
.products-content {
  padding-bottom: 20px;
}
/* products-filter
 * -------------------------------------------------------------------------- */

.products-filter {
  position: relative;
  margin-top: 15px;
  margin-bottom: 15px;
  padding-top: 15px;
  text-align: center;
  border-bottom: none;
}

.products-filter-bottom {
  border-bottom: 1px solid #a5a5a5;
}

.products-filter-top {
  border-top: 1px solid #a5a5a5;
}

@media (min-width: 768px) {
  .products-filter {
    text-align: left;
  }
}

.products-filter:after {
  display: table;
  clear: both;
}

.products-filter-more {
  text-align: center;
  cursor: pointer;
}

.products-filter-more-button {
  display: inline-block;
  padding: 8px 12px;
  text-transform: uppercase;
  text-decoration: none;
  font-size: 16px;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #fff;
  background: #165528;
}

.products-filter-more-button:focus,
.products-filter-more-button:hover,
.products-filter-more-button:active {
  text-decoration: none;
  color: #fff;
}

.products-filter-pages {
  float: left;
  margin-top: 0;
  font-size: 20px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
}

.products-filter-pages h1 {
  display: inline-block;
  padding-right: 4px;
  margin-top: 0;
  margin-bottom: 0;
  font-size: 20px;
  line-height: 1.4;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
}

.products-filter-pages-top {
  margin-top: 0;
  margin-bottom: 10px;
  position: static;
  display: inline-block;
}

@media (min-width: 768px) {
  .products-filter-pages-top {
    margin-bottom: 0;
  }
}

.products-secondary-button {
  float: right;
  padding: 6px 10px;
  text-transform: uppercase;
  text-decoration: none;
  font-size: 12px;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  background: #8ba693;
  color: #fff;
}

.products-secondary-button:focus,
.products-secondary-button:hover,
.products-secondary-button:active {
  text-decoration: none;
  color: #fff;
}
/* products-filter-misc
 * -------------------------------------------------------------------------- */

.products-filter-misc {
  float: none;
}

@media (min-width: 768px) {
  .products-filter-misc {
    float: right;
  }
}

.dev-products-filter-misc-sort {
  vertical-align: top;
  color: #a5a5a5;
}

.dev-products-filter-misc-sort.dropdown-filter-btn:before,
.dev-products-filter-misc-sort.dropdown-filter-btn:after {
  color: #b6c7bb;
}

.products-filter-misc-sort {
  height: 15px;
  padding-right: 25px;
  line-height: 15px;
  border: none;
  outline: none;
}

@media (max-width: 767px) {
  .products-filter-misc-sort {
    width: 100%;
    margin-bottom: 0;
    padding-right: 50px;
    padding-left: 15px;
    border: 1px solid #8ba693;
    outline: none;
    line-height: 45px;
    height: 45px;
    font-size: 16px;
    color: #8ba693;
  }
}

.offers-products-filter-misc-sort {
  vertical-align: bottom;
}

.btn.products-filter-misc-sort:focus,
.btn.products-filter-misc-sort:hover,
.btn.products-filter-misc-sort:active,
.btn.products-filter-misc-sort:active.focus {
  outline: none;
  box-shadow: none;
}
/* display-type
 * -------------------------------------------------------------------------- */

.display-type {
  margin-bottom: 0;
  /* display: inline-block; */
  float: right;
  list-style-type: none;
  color: #8ba693;
}

.display-type-btn {
  vertical-align: middle;
  display: inline-block;
  font-size: 21px;
  margin-left: 10px;
  cursor: pointer;
}

.display-type-btn:focus .display-icon,
.display-type-btn:hover .display-icon,
.display-type-btn:active .display-icon,
.display-type-btn.active .display-icon {
  background: #165528;
}
/* boxes-icon
 * -------------------------------------------------------------------------- */

.boxes-icon {
  display: block;
  position: relative;
  background: #8ba693;
  height: 16px;
  width: 16px;
}

.boxes-icon:before,
.boxes-icon:after {
  position: absolute;
  content: '';
  background: #fff;
}

.boxes-icon:before {
  right: 50%;
  margin-right: -1px;
  height: 100%;
  width: 2px;
}

.boxes-icon:after {
  top: 50%;
  width: 100%;
  height: 2px;
  margin-top: -1px;
}
/* bars-icon
 * -------------------------------------------------------------------------- */

.bars-icon {
  display: block;
  position: relative;
  height: 16px;
  width: 16px;
  background: #8ba693;
}

.bars-icon:before,
.bars-icon:after {
  content: '';
  width: 100%;
  height: 2px;
  position: absolute;
  background: #fff;
}

.bars-icon:before {
  top: 4px;
}

.bars-icon:after {
  bottom: 4px;
}

/* SUBSCRIBE
** ========================================================================== */

/* subscribe-button
 * -------------------------------------------------------------------------- */

.subscribe-button {
  margin-bottom: 10px;
}

/* ESTATE
** ========================================================================== */

/* estate-details
 * -------------------------------------------------------------------------- */

.estate:after {
  content: '';
  display: table;
  clear: both;
}

.estate-details {
  width: 55%;
  float: left;
  padding-left: 20px;
}

.estate-details-name {
  display: block;
  margin-bottom: 10px;
  font-size: 20px;
  text-decoration: none;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
  line-height: 1;
}

.estate-details-name:focus,
.estate-details-name:hover,
.estate-details-name:active {
  text-decoration: none;
  color: #165528;
}

.estate-details-link {
  display: inline-block;
  border-bottom: 1px solid transparent;
  font-size: 16px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #165528;
}

.estate-details-link:focus,
.estate-details-link:hover,
.estate-details-link:active {
  color: #165528;
  border-bottom: 1px solid #165528;
  text-decoration: none;
}

.estate-details-info {
  max-height: 80px;
  overflow: hidden;
}

@media (min-width: 992px) {
  .estate-details-info {
    max-height: 140px;
  }
}

@media (min-width: 1200px) {
  .estate-details-info {
    max-height: 180px;
  }
}

.estate-details-info-label {
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
}
/* estate-thumbnail
  * -------------------------------------------------------------------------- */

.estate-thumbnail {
  position: relative;
  /* display: inline-block; */
  float: left;
  height: 0;
  width: 43%;
  padding-top: 24.3%;
  overflow: hidden;
}

.estate-thumbnail-link {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
}

.estate-thumbnail-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/*.no-objectfit .estate-thumbnail-img {
    opacity: 0;
  }*/

/* DEVELOPMENTS
** ========================================================================== */

/* developments-block
 * -------------------------------------------------------------------------- */

.developments-block {
  padding-bottom: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #d1d1d1;
}

/* MOB-SLIDER
** ========================================================================== */

/* mob-slider-item
 * -------------------------------------------------------------------------- */

.mob-slider {
  margin-left: -15px;
  margin-right: -15px;
}

.mob-slider-item-wrap {
  margin-right: 6px;
  margin-left: 6px;
  outline: none;
}

.mob-slider-item-wrap:focus,
.mob-slider-item-wrap:hover,
.mob-slider-item-wrap:active {
  outline: none;
}

/*.mob-slider-item-wrap.slick-active {
    position: relative;
  }

  .mob-slider-item-wrap.slick-active:before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: #165528;
  }*/

.mob-slider-item {
  position: relative;
  display: inline-block;
  overflow: hidden;
  width: 100%;
  height: 0;
  padding-top: 100%;
}

.mob-slider-img-wrap {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.mob-slider-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/*.no-objectfit .mob-slider-img {
  opacity: 0;
 }*/
/* mob-slider-buttons
 * -------------------------------------------------------------------------- */
.mob-slider-buttons {
  position: relative;
  text-align: center;
}

.mob-slider-buttons .slider-buttons-btn {
  height: 15px;
  width: 20px;
  background: #8ba693;
}

.mob-slider-buttons .slider-buttons-btn.active {
  background: #165528;
}
/* mob-slider-counter
  * -------------------------------------------------------------------------- */

.mob-counter {
  margin-bottom: 20px;
  text-align: center;
  text-transform: uppercase;
  color: #165528;
  font-size: 15px;
  font-family: 'source_sans_probold', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

/* MAP
** ========================================================================== */

/* map pins locations
 * -------------------------------------------------------------------------- */

.tags-tallinn {
  right: 54.9%;
  top: 13%;
}

.tags-haapsalu {
  left: 26.9%;
  top: 35%;
}

.tags-jogevamaa {
  right: 28.5%;
  top: 41.8%;
}

.tags-johvi {
  right: 12%;
  top: 14.8%;
}

.tags-kuressaare {
  left: 10.5%;
  top: 65.5%;
}

.tags-hiiumaa {
  left: 14.8%;
  top: 33.5%;
}

.tags-narva {
  right: 0%;
  top: 15%;
}

.tags-paide {
  right: 41.9%;
  top: 37%;
}

.tags-polvamaa {
  right: 19.8%;
  top: 74.3%;
}

.tags-parnu {
  left: 43.6%;
  top: 62.4%;
}

.tags-rakvere {
  right: 28.7%;
  top: 15%;
}

.tags-rapla {
  left: 48.1%;
  top: 30.1%;
}

.tags-tapa {
  left: 64.6%;
  top: 20.1%;
}

.tags-tartu {
  right: 21%;
  bottom: 39.1%;
}

.tags-turi {
  right: 44%;
  top: 40.4%;
}

.tags-valga {
  bottom: 12%;
  left: 66.8%;
}

.tags-viljandi {
  bottom: 40.6%;
  left: 58%;
}

.tags-voru {
  bottom: 15.3%;
  right: 18.7%;
}
/* map map buttons
 * -------------------------------------------------------------------------- */

.mob-map-button {
  display: block;
  position: relative;
  border: 1px solid #165528;
  height: 40px;
  width: 40px;
  border-radius: 100%;
  font-size: 25px;
  text-align: center;
  z-index: 99;
  color: #165528;
  cursor: pointer;
}

@media (min-width: 500px) {
  .mob-map-button {
    height: 45px;
    width: 45px;
    font-size: 35px;
  }
}

.mob-map-button-next:before,
.mob-map-button-prev:before {
  position: absolute;
  top: 50%;
  margin-top: -10px;
  left: 0;
  right: 0;
  text-align: center;
  display: inline-block;
  font: normal normal normal 20px/1 FontAwesome;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@media (min-width: 500px) {
  .mob-map-button-next:before,
  .mob-map-button-prev:before {
    margin-top: -12.5px;
    font-size: 25px;
  }
}

.mob-map-button-next {
  float: right;
}

.mob-map-button-next:before {
  content: '\f105';
}

.mob-map-button-prev:before {
  content: '\f104';
}

.mob-map-button-prev {
  float: left;
}

.mob-map-controls-list {
  list-style: none;
  position: absolute;
  top: 50%;
  right: 0;
  bottom: 0;
  left: 0;
  margin-top: -23px;
  margin-left: 40px;
  margin-right: 40px;
  margin-bottom: 0;
  font-size: 20px;
  overflow: hidden;
  text-transform: uppercase;
}

@media (min-width: 500px) {
  .mob-map-controls-list {
    margin-left: 45px;
    margin-right: 45px;
    margin-top: -30px;
    font-size: 25px;
  }
}

/* tmp
** ========================================================================== */

/* contacts page toggleable blocks */
.table-contacts tbody {
  display: none;
}
.table-contacts tbody.active {
  display: table-row-group;
}

/* price per square meter, small green text */
.offer-price-per-area {
  vertical-align: middle;
  font-size: 14px;
  font-family: 'source_sans_prolight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  color: #8ba693;
}

/* broker page broker achievements points over 9000 */
.tabs-item.active {
  color: #165528;
  border-bottom-color: #165528;
}
.brokers-portfolio-tab {
  display: none;
}
.brokers-portfolio-tab.active {
  display: block;
}

.regional-contact-map.object-regional-contact-map {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 0;
  z-index: -1;
}

.regional-contact-map.object-regional-contact-map.active {
  opacity: 1;
  z-index: 1;
}

.locale-map.object-locale-map {
  position: relative;
  height: 100%;
  width: 100%;
}

.map-canvas.object-map-canvas {
  height: 100%;
  width: 100%;
  padding-bottom: 0;
}

.locale-button.object-locale-button {
  position: absolute;
  top: 0;
  right: 0;
}

/* products list and grid view */
@media (min-width: 768px) {
  .products-content {
    display: none;
  }
  .products-content.active {
    display: block;
  }
}

/* estonian map*/

.estonia-map-lg {
  width: 100%;
  height: 320px;
}

@media (min-width: 768px) {
  .estonia-map-lg {
    height: 380px;
  }
}

@media (min-width: 992px) {
  .estonia-map-lg {
    height: 620px;
  }
}

.estonia-map {
  width: 100%;
  height: 320px;
}

@media (min-width: 768px) {
  .estonia-map {
    height: 420px;
  }
}

.tags {
  position: absolute;
  width: 16px;
  height: 16px;
  margin-left: -8px;
  margin-top: -8px;
  background: #fff;
  border-radius: 50%;
  cursor: pointer;
}

.tags:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 14px 8px 0 8px;
  border-color: #fff transparent transparent transparent;
}

.tags-inner {
  top: 50%;
  left: 50%;
  margin-top: -6px;
  margin-left: -6px;
  width: 12px;
  height: 12px;
  background: #165528;
}

.tags.active .tags-inner {
  background: red;
}

.tags-inner:before {
  border-width: 11px 6px 0 6px;
  border-color: #165528 transparent transparent transparent;
}

.tags.active .tags-inner:before {
  border-color: red transparent transparent transparent;
}

.tags-inner:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 6px;
  margin-top: -3px;
  margin-left: -3px;
  background: #fff;
  border-radius: 50%;
}

.offer-thumbnail-wrap {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.search-highlight {
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

/* take contact form */
.mailto-form-after-submit-message {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: white;
  display: none;
}
.mailto-form-after-submit-message.active {
  display: block;
}

.em-tooltip {
  display: none;
}

@media (min-width: 768px) {
  .em-tooltip.active {
    display: block;
    position: absolute;
    top: -50%;
    right: 50%;
    margin-top: -25.5px;
    background: #b70004;
    border: 1px solid #fff;
    font-size: 12px;
    border-radius: 7px;
    text-transform: uppercase;
    transform: translateX(50%);
    -webkit-transform: translateX(50%);
    -moz-transform: translateX(50%);
    padding: 2px 8px;
    color: #fff;
  }
}

.em-tooltip:before,
.em-tooltip:after {
  content: '';
  position: absolute;
  left: 50%;
  margin-left: -8px;
  bottom: -8px;
  border-style: solid;
  border-width: 11px 8px 0;
  border-color: #b70004 transparent transparent transparent;
}

.em-tooltip:before {
  bottom: -11px;
  border-color: #fff transparent transparent transparent;
}

/*.em-tags-mob{
  position: relative;
  overflow: hidden;
}
.em-tag-mob{
  position: absolute;
  top: 50%;
  opacity: 0;
  left: 100%;
  width: 100%;
  margin-top: -10px;
  text-align: center;
  color: transparent;
  -moz-transition-property: left;
  -o-transition-property: left;
  -webkit-transition-property: left;
  transition-property: left;
  -moz-transition-duration: 1s;
  -o-transition-duration: 1s;
  -webkit-transition-duration: 1s;
  transition-duration: 1s;
  -moz-transition-timing-function: ease;
  -o-transition-timing-function: ease;
  -webkit-transition-timing-function: ease;
  transition-timing-function: ease;
}
.em-tag-mob.active{
  opacity: 1;
  left: 0;
  color: #165528;
}
.em-tag-mob.active.to-left{
  opacity: 1;
  left: -100%;
}
.em-tag-mob.active.to-right{
  opacity: 1;
  left: 100%;
}
.em-tag-mob.prev{
  opacity: 1;
  left: -100%;
}
.em-tag-mob.prev.to-active{
  opacity: 1;
  left: 0;
}
.em-tag-mob.next{
  opacity: 1;
  left: -100%;
}
.em-tag-mob.next.to-active{
  opacity: 1;
  left: 0;
}*/

.em-tag-mob {
  height: 45px;
  line-height: 45px;
  text-transform: uppercase;
  font-size: 18px;
  vertical-align: middle;
  cursor: pointer;
  color: #165528;
}

.em-tag-mob:focus,
.em-tag-mob:hover,
.em-tag-mob:active {
  outline: none;
}

.em-mob-map-btn {
  position: absolute;
  top: 0;
  bottom: 0;
  border-radius: 100%;
  width: 45px;
  height: 45px;
  background: #fff;
  border: 2px solid #165528;
  z-index: 1;
}

.em-mob-map-btn:focus,
.em-mob-map-btn:hover,
.em-mob-map-btn:active {
  outline: none;
}

.em-mob-map-next {
  right: 0;
}

.em-mob-map-next:before {
  content: '';
  position: absolute;
  top: 50%;
  right: 40%;
  height: 14px;
  width: 14px;
  margin-top: -7px;
  margin-left: -10px;
  border-style: solid;
  border-width: 2px;
  border-color: #165528 #165528 transparent transparent;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.em-mob-map-prev {
  left: 0;
}

.em-mob-map-prev:before {
  content: '';
  position: absolute;
  top: 50%;
  right: 25%;
  height: 14px;
  width: 14px;
  margin-top: -7px;
  margin-left: -3px;
  border-style: solid;
  border-width: 2px;
  border-color: transparent transparent #165528 #165528;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.object-seperator {
  display: none;
}

.object-seperator:nth-child(6n) {
  display: block;
  float: left;
  width: 100%;
  margin-top: 20px;
  margin-bottom: 20px;
  height: 1px;
  background: #d9d9d9;
}

.object-seperator:last-child {
  display: none;
}

.js-slider-single {
  -webkit-transition: opacity 2s ease-in-out;
  transition: opacity 2s ease-in-out;
  z-index: 0;
  opacity: 0;
}

.js-slider-single.active {
  z-index: 1;
  opacity: 1;
}

/*.offer-image {z-index: 999999;}
.gallery-images-misc {
  display: none!important;
}

.mob-slider,
.gallery-title-heading {
  display: none!important;
}*/

/* css that came with acf, edit as you like */

/* Gallery v2
* -------------------------------------------------------------------------- */

/* gallery
 * -------------------------------------------------------------------------- */

/*.offer-gallery {
    position: fixed;
    z-index: 999;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0%;
    background: #fff;
  }
  .offer-gallery-yolo {
    position: absolute;
    top: 50px;
    bottom: 50px;
    width: 500px;
    margin: auto;
    background: red;
  }*/

.offer-images {
  display: none;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.offer-images.active {
  display: block;
}

.lightbox {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
  background-color: #fff;
  opacity: 0.95;
}

.gallery {
  position: absolute;
  top: 90px;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 2;
}

.gallery-offer-name-extra {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 0;
}

.gallery-images-img {
  -webkit-transition: background-image 0.2s ease-in-out;
  transition: background-image 0.2s ease-in-out;
}
/* gallery-images-img-wrap
 * -------------------------------------------------------------------------- */

.gallery-images {
  position: relative;
  margin: 0 auto;
}

.gallery-images-bg {
  background: #d5e3d6;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.gallery-images-img-wrap {
  position: relative;
  height: 0;
  padding-bottom: 30.528%;
}

@media (min-height: 640px) {
  .gallery-images-img-wrap {
    padding-bottom: 40.528%;
  }
}

@media (min-height: 820px) {
  .gallery-images-img-wrap {
    padding-bottom: 51.528%;
  }
}

@media (min-height: 865px) {
  .gallery-images-img-wrap {
    padding-bottom: 35.528%;
  }
}

@media (min-height: 935px) {
  .gallery-images-img-wrap {
    padding-bottom: 60.528%;
  }
}

@media (min-height: 1000px) {
  .gallery-images-img-wrap {
    padding-bottom: 65.528%;
  }
}

.gallery-images-img-wrap-abs {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
}

.gallery-images-img {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  max-height: 400px;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: 50% 50%;
}

@media (min-height: 820px) {
  .gallery-images-img {
    max-height: 500px;
  }
}

@media (min-height: 920px) {
  .gallery-images-img {
    max-height: 700px;
  }
}
/* gallery-list
 * -------------------------------------------------------------------------- */

.gallery-container {
  margin: 0 auto;
  overflow-x: auto;
}

.gallery-images-list {
  position: relative;
  left: 0;
  margin-bottom: 0;
  padding-bottom: 16px;
  list-style-type: none;
  white-space: nowrap;
  text-align: center;
}

.gallery-images-list-wrap {
  position: absolute;
  left: 0;
  right: 0;
  bottom: -15px;
  margin-left: 5px;
}

.gallery-images-item-wrap-abs {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.gallery-images-item-wrap {
  position: relative;
  height: 0;
  padding-bottom: 10%;
  margin-bottom: 5px;
  width: 20%;
  display: inline-block;
}

.gallery-images-item-wrap-abs {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
}

.gallery-images-item {
  width: auto;
  height: 100%;
  max-width: none;
  padding-top: 0;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}

.gallery-images-item-wrap {
  border: 2px solid transparent;
}

.gallery-images-item-wrap.active {
  border: 2px solid #165528;
}

.gallery-close-button {
  position: absolute;
  right: 0;
  top: 50%;
  height: 15px;
  width: 15px;
  margin-top: -10px;
  margin-right: 21px;
  color: #fff;
  cursor: pointer;
}

.gallery-close-button:before,
.gallery-close-button:after {
  content: '';
  position: absolute;
  top: 50%;
  height: 3px;
  width: 100%;
  background: #fff;
}

.gallery-close-button:before {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.gallery-close-button:after {
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  transform: rotate(45deg);
}
/* gallery-title
 * -------------------------------------------------------------------------- */

.gallery-title {
  position: relative;
  background-color: #165528;
  vertical-align: middle;
  z-index: 999;
}

.gallery-title-heading {
  margin-top: 0;
  padding-left: 21px;
  padding-right: 52px;
  padding-top: 15px;
  padding-bottom: 15px;
  font-family: 'source_sans_proextralight', 'Helvetica Neue', Helvetica, arial, sans-serif;
  font-size: 36px;
  color: #fff;
}
/* gallery-btn
 * -------------------------------------------------------------------------- */

.gallery-images-misc {
  position: absolute;
  display: block;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99999;
  text-align: center;
  margin-bottom: 0;
  color: #fff;
}

.gallery-images-misc-item {
  display: inline-block;
  position: relative;
  height: 50px;
  line-height: 50px;
  width: 80px;
  background: rgba(22, 85, 40, 0.8);
}

.no-rgba .gallery-images-misc-item {
  background: #165528;
}

.gallery-counter {
  position: absolute;
  text-align: center;
  left: 0;
  right: 0;
}

.gallery-btn {
  display: block;
  position: relative;
  height: 100%;
  cursor: pointer;
}

.gallery-btn:focus,
.gallery-btn:hover,
.gallery-btn:active,
.gallery-btn.active {
  background: #8ba693;
}

.gallery-btn:before,
.gallery-btn:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 42%;
  width: 16px;
  height: 2px;
  background: #fff;
}

.gallery-btn.gallery-btn-prev:before {
  margin-top: 5px;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  transform: rotate(45deg);
}

.gallery-btn.gallery-btn-prev:after {
  margin-top: -7px;
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.gallery-btn.gallery-btn-next:before {
  margin-top: 5px;
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.gallery-btn.gallery-btn-next:after {
  margin-top: -5px;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  transform: rotate(45deg);
}

.gallery-thumbs-warp {
  position: absolute;
  height: 100%;
  /*margin-top: -20px;*/
}

.gallery-thumbs-scroll {
  position: absolute;
  bottom: 15px;
  height: 100%;
  margin-top: -10px;
  padding: 0 15px;
  font-size: 50px;
  border: none;
  z-index: 99;
  opacity: 0.5;
  background-color: rgba(255, 255, 255, 0.4);
  filter: alpha(opacity=10);
  -webkit-transition: opacity 0.15s ease-in-out;
  -moz-transition: opacity 0.15s ease-in-out;
  -ms-transition: opacity 0.15s ease-in-out;
  -o-transition: opacity 0.15s ease-in-out;
  transition: opacity 0.15s ease-in-out;
  color: #fff;
}

.no-rgba .gallery-thumbs-scroll {
  background-color: transparent;
}

.gallery-thumbs-scroll:focus,
.gallery-thumbs-scroll:hover,
.gallery-thumbs-scroll:active {
  opacity: 1;
  outline: none;
}

.gallery-thumbs-scroll-right {
  right: 0;
}

.gallery-thumbs-scroll-left {
  left: 0;
}
/* gallery-main-img-counter
 * -------------------------------------------------------------------------- */

/* layout-lines
* -------------------------------------------------------------------------- */

/* For quick layouting purpose ONLY */

/*
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font: 12px/1 sans-serif;
  }

  body > div {
    margin-bottom: 16px;
    border-color: #666;
  }

  div {
    border: solid 1px #ccc;
    padding: 8px !important;
  }

  .container:hover { background: #fff9da; }
  [class*="col-"] { border-color: #e7acac; }

  .row {
    border-style: dashed;
    margin: 0;
  }*/

.no-gutter {
  padding-left: 0;
  padding-right: 0;
}

.slider-wrap {
  position: relative;
}
.slider-wrap .slider {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
}
.slider-wrap .slider.active {
  z-index: 3;
}

.loading {
  position: relative;
}

.loading:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: block;
  margin: 0 auto;
  height: 50px;
  width: 50px;
  background: url('../img/pindi_loader.gif') no-repeat center center;
}

.dl-submenu li {
  display: block;
}

.subscribe-join {
  position: relative;
}

.subscribe-privacy-error,
.subscribe-email-error {
  display: none;
  text-align: left;
}

.has-privacy-error .subscribe-privacy-error,
.has-email-error .subscribe-email-error {
  display: block;
}

.subscribe-error-text {
  text-align: left;
  color: #e3212e !important;
}

.subscribe-success {
  display: none;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  text-align: center;
  white-space: nowrap;
  background-color: rgba(255, 255, 255, 0.95);
}

.subscribe-success:before {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: -2px;
}

.success .subscribe-success {
  display: block;
}

.subscribe-success-text {
  position: absolute;
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  left: 0;
  right: 0;
  top: 50%;
  margin-top: -14px;
  color: #165528;
  font-size: 20px;
}

/* to be sorted */
.dropdown-seperator {
  display: block;
  height: 1px;
  margin: 5px 2px;
  background-color: #165528;
}

.footer-logo-wrap {
  position: relative;
  width: 100%;
  height: 0;
  display: inline-block;
  width: 100%;
  padding-bottom: 35%;
  margin-bottom: 15px;
}

.footer-logo-wrap-bkm {
  padding-bottom: 44%;
}

@media (min-width: 500px) {
  .footer-logo-wrap {
    padding-bottom: 15%;
  }

  .footer-logo-wrap-bkm {
    padding-bottom: 22%;
  }
}

@media (min-width: 768px) {
  .footer-logo-wrap {
    padding-bottom: 6%;
    margin-bottom: 0;
  }

  .footer-logo-wrap-bkm {
    padding-bottom: 33%;
  }
}

.footer-logo-abs {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.footer-logo-row {
  background: url('../img/logorida.svg') no-repeat center center;
}

.no-svg .footer-logo-row {
  background: url('../img/logorida.png?1') no-repeat center center;
}

.footer-logo-1 {
  background: url('../img/eri.svg') no-repeat center center;
}

.no-svg .footer-logo-1 {
  background-image: url('../img/eri.png');
}

.footer-logo-2 {
  background: url('../img/Realia.svg') no-repeat center center;
}

.no-svg .footer-logo-2 {
  background-image: url('../img/Realia.png');
}

.footer-logo-3 {
  background: url('../img/Realia_lux.svg') no-repeat center center;
}

.no-svg .footer-logo-3 {
  background-image: url('../img/Realia_lux.png');
}

.footer-logo-4 {
  background: url('../img/BKM.svg') no-repeat center center;
}

.no-svg .footer-logo-4 {
  background-image: url('../img/BKM.png');
}

.footer-logo-5 {
  background: url('../img/pindi_kinnisvara_logo_footer.svg') no-repeat center center;
}

.no-svg .footer-logo-5 {
  background-image: url('../img/pindi_kinnisvara_logo_footer.png');
}

.footer-logo {
  height: 100%;
  width: 100%;
  -webkit-background-size: contain;
  -moz-background-size: contain;
  -o-background-size: contain;
  background-size: contain;
}

.flex-row {
  display: flex;
  flex-wrap: wrap;
}

.col-flex {
  padding-bottom: 15px;
}

.col-flex-bordered:before {
  position: absolute;
  right: 0;
  background: #ced6e5;
  width: 1px;
  height: 100%;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-center .contact-wrap {
  width: 100%;
  max-width: 380px;
}

@media (min-width: 992px) {
  .col-flex-bordered:before {
    content: '';
  }
}

.col-flex-order-first {
  order: -1;
}

.col-flex-order-1 {
  order: 1;
}

.col-flex-order-2 {
  order: 2;
}

.col-flex-order-3 {
  order: 3;
}

@media (min-width: 992) {
  .col-md-flex-order-first {
    order: -1;
  }

  .col-md-flex-order-1 {
    order: 1;
  }

  .col-md-flex-order-2 {
    order: 2;
  }

  .col-md-flex-order-3 {
    order: 3;
  }
}

.cookie-header {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding-top: 15px;
  padding-bottom: 15px;
  border-top: #165528;
  background-color: #ededed;
  text-align: center;
  -webkit-box-shadow: 0px -4px 23px -7px rgba(120, 120, 120, 1);
  -moz-box-shadow: 0px -4px 23px -7px rgba(120, 120, 120, 1);
  box-shadow: 0px -4px 23px -7px rgba(120, 120, 120, 1);
  -webkit-transition: bottom 1s ease;
  -moz-transition: bottom 1s ease;
  -o-transition: bottom 1s ease;
  -ms-transition: bottom 1s ease;
  transition: bottom 1s ease;
  z-index: 10000;
}

.cookie-header-content {
  padding-bottom: 10px;
  font-size: 16px;
  color: #165528;
  font-family: 'source_sans_proregular', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.cookie-header-hidden {
  bottom: -100%;
}

.cookie-header-settings {
  display: none;
}
.cookie-header-settings.show {
  display: flex !important;
  justify-content: center;
  align-items: center;
}
@media (max-width: 767.98px) {
  .cookie-header-settings {
    flex-wrap: wrap;
  }
}

.cookie-bar__check-area {
  display: grid !important;
  grid-template-columns: repeat(2, 1fr);
  padding: 1.6rem 3rem;
  align-items: center;
  gap: 1.6rem 3rem;
  border-radius: 1rem;
}
@media (max-width: 767.98px) {
  .cookie-bar__check-area {
    padding: 1.6rem 2rem;
    width: 100%;
  }
}

.cookie-bar__check-group {
  display: flex;
  align-items: center;
}

.cookie-bar__check-group label {
  margin: 0 8px;
}

.cookie-bar__check-group .cookie-checkbox {
  position: relative;
  width: 2.4rem;
  height: 2.4rem;
  padding: 0;
  margin: 0;
  border: 2px solid #3c3c3c;
  border-radius: 0.3;
  cursor: pointer;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.cookie-bar__check-group .cookie-checkbox::after {
  position: absolute;
  display: none;
  content: "";
}
.cookie-bar__check-group .cookie-checkbox:checked::after {
  top: -0.4rem;
  right: 0;
  bottom: 0;
  left: 0;
  display: block;
  width: 0.8rem;
  height: 1.2rem;
  margin: auto;
  border: solid #3c3c3c;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
.cookie-bar__check-group .cookie-checkbox:disabled {
  opacity: 0.5;
}
.cookie-bar__check-group .cookie-checkbox:disabled + label {
  opacity: 0.5;
}

.tippy-toggle {
  display: block;
  height: 1.8rem;
  width: 1.8rem;
  background-image: url("../img/info.svg");
  background-repeat: no-repeat;
  background-size: contain;
}
.tippy-toggle.disabled {
  opacity: 0.5;
}

.cookiebar-tooltip {
  display: none;
  color: #165528;
  font-size: 1.2rem;
  font-weight: 500;
  line-height: 1.44rem;
  text-align: left;
  background-color: #fff;
  border-radius: 1rem;
  padding: 1rem;
  max-width: 300px;
}
.cookiebar-tooltip[data-show] {
  display: block;
}

.absolute--top-left {
  position: absolute;
  top: 0;
  left: 0;
}

.ad-banner {
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 5;

  -webkit-transform: translate(0, 100%);
  -ms-transform: translate(0, 100%);
  -o-transform: translate(0, 100%);
  transform: translate(0, 100%);

  -webkit-transition: transform 0.5s ease-in-out;
  -o-transition: transform 0.5s ease-in-out;
  transition: transform 0.5s ease-in-out;
}

.ad-banner.ad-banner--visible {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  transform: translate(0, 0);
}

.icon {
  position: relative;
  display: inline-block;
  z-index: 1;
}

.icon--button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 1px solid #8ba693;
  background-color: #fff;
  cursor: pointer;
}

.icon--button-close::before,
.icon--button-close::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  display: inline-block;
  width: 75%;
  height: 10%;
  background-color: #8ba693;
}

.icon--button-close::before {
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}

.icon--button-close::after {
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  -ms-transform: translate(-50%, -50%) rotate(-45deg);
  -o-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}

.icon--ad-banner {
  position: absolute;
  top: 0;
  left: 0;

  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  transform: translate(0, 0);
}

.ad-banner--visible .icon--ad-banner {
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.container-narrow {
  width: 800px;
  max-width: 100%;
}

.container-medium {
  width: 900px;
  max-width: 100%;
}

@media (max-width: 600px) {
  .container-narrow {
    padding-left: 35px;
    padding-right: 35px;
  }
}

.heart-icon {
  width: 30px;
  height: 30px;
  margin-bottom: 8px;
}


.pb-20 {
  padding-bottom: 20px;
}
.pb-30 {
  padding-bottom: 30px;
}
.pb-40 {
  padding-bottom: 40px;
}
.pb-50 {
  padding-bottom: 50px;
}

.pt-20 {
  padding-top: 20px;
}
.pt-30 {
  padding-top: 30px;
}

.mb-10 {
  margin-bottom: 10px;
}
.mb-20 {
  margin-bottom: 20px;
}
.mb-30 {
  margin-bottom: 30px;
}
.mb-40 {
  margin-bottom: 40px;
}
.mb-50 {
  margin-bottom: 50px;
}

.mt-20 {
  margin-top: 20px;
}

.video-content video {
  max-width: 100%;
}

.green-box {
  background-color: #f1f5f2;
  padding: 15px 20px;
}

.page-management .green-box strong span {
  font-family: 'source_sans_probold', 'Helvetica Neue', Helvetica, arial, sans-serif;
}
.page-management .green-box a span {
  color: #155528;
}
.page-management .green-box .main-button span {
  color: #fff;
  text-transform: none;
  padding-left: 7px;
  padding-right: 7px;
}

.page-vahendus .green-box {
  padding-left: 50px;
  padding-right: 50px;
}
.page-vahendus .green-box ul li p {
  color: #155528;
}
.page-vahendus .broker-contact-card .contact-thumbnail .contact-thumbnail-img {
  object-fit: cover;
}
.page-vahendus .broker-contact-card .contact-thumbnail {
  width: 105px;
}
.page-vahendus .broker-contact-card .contact-info {
  width: calc(100% - 130px);
}

.page-hindamine-ru .contact-buttons a {
  margin: 0 7px;
}

.page-hindamine-ru .broker-contact-card {
  display: inline-flex;
}
.page-hindamine-ru .broker-contact-card .contact-thumbnail {
  display: block;
}
.page-hindamine-ru .broker-contact-card .contact-info {
  display: block;
  width: auto;
  flex-grow: 1;
  padding-left: 15px;
}
@media screen and (max-width: 1400px) {
  .table.filter-table.filter-table-top tr {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
  }

  .filter-table-top.filter-table > tbody > tr > td {
    width: 100%;
  }

  .filter-table-group-half {
    width: 50% !important;
  }

  .dropdown-filter {
    max-width: 100% !important;
  }
  .filter-table-top td {
    border-left: 0px;
  }
}

@media screen and (max-width: 768px) {
  .subscribe-join {
    text-align: center;
  }

  .mob-filter-group .filter-divider {
    position: relative;
    margin-left: 6vw;
    left: 0;
  }

  .slider-wrap .slider {
    background-size: contain;
  }
}

.form-template-page .primary-heading, .pricing-template-page .primary-heading {
  text-align: center;
  margin: 40px 0 45px 0;
}

@media screen and (min-width: 700px) {
  .form-template-page .user-added-content .user-added-content__left form div.phone-margin-minus {
    margin-bottom: -5.1%;
  }
}

@media screen and (min-width: 1200px) {
  .form-template-page .primary-heading, .pricing-template-page .primary-heading {
    font-size: 28px;
  }
}

@media screen and (max-width: 700px) {
  .form-template-page .primary-heading, .pricing-template-page .primary-heading {
    font-size: 22px;
  }
}
.form-template-page .user-added-content.main__content::before, .pricing-template-page .user-added-content.main__content::before {
  content: none;
}
.form-template-page .user-added-content.main__content::after, .pricing-template-page .user-added-content.main__content::after {
  content: none;
}
.form-template-page .user-added-content.main__content {
  display: grid;
  grid-template-columns: 1.5fr 1fr;
  grid-gap: 20px;
}
.pricing-template-page .user-added-content.main__content {
  display: grid;
  grid-template-columns: 1.3fr 1.16fr;
  grid-gap: 20px;
}

@media screen and (max-width: 1200px) {
  .form-template-page .user-added-content.main__content, .pricing-template-page .user-added-content.main__content {
    display: flex;
    flex-direction: column;
  }
}

.form-template-page .user-added-content .user-added-content__right div:nth-of-type(2) img, .pricing-template-page .user-added-content .user-added-content__right div:nth-of-type(2) img {
  margin: 5px 0;
}
.form-template-page .user-added-content .user-added-content__left p,
.form-template-page .user-added-content .user-added-content__left ul li, .pricing-template-page .user-added-content .user-added-content__left p,
.pricing-template-page .user-added-content .user-added-content__left ul li {
  color: #165527;
}

.form-template-page .user-added-content .user-added-content__left ul li::before, .pricing-template-page .user-added-content .user-added-content__left ul li::before {
  content: none;
}

.form-template-page .user-added-content .user-added-content__left ul li, .pricing-template-page .user-added-content .user-added-content__left ul li {
  font-size: 18px;
  list-style-type: disc;
  /* margin-left: 30px; */
  margin-left: 15px;;
  color: #165527;
  padding: 0;
}

.form-template-page .user-added-content .user-added-content__left h3, .pricing-template-page .user-added-content .user-added-content__left h3 {
  font-size: 16px;
  color: #165527;
  font-weight: bold;
  font-family: 'source_sans_probold', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.form-template-page .user-added-content .user-added-content__left form input,
.form-template-page .user-added-content .user-added-content__left form textarea, .pricing-template-page .user-added-content .user-added-content__left form input,
.pricing-template-page .user-added-content .user-added-content__left form textarea {
  width: 100%;
  border-radius: 5px;
  border: 1px solid #165527;
  border-color: #165527;
  color: #165527;
  padding: 10px;
}

.form-template-page .user-added-content .user-added-content__left form select, .pricing-template-page .user-added-content .user-added-content__left form select  {
  width: 30%;
  border-radius: 5px;
  border: 1px solid #165527;
  border-color: #165527;
  color: #165527;
  padding: 10px;
  background: #fff;
}

.form-template-page .user-added-content .user-added-content__left form div, .pricing-template-page .user-added-content .user-added-content__left form div {
  margin: 5px 0;
}

#page-contact-form > div:nth-child(7) > div.form__buttons__container > a {
  border-bottom: 1px solid #165527;
}

.form-template-page .user-added-content .user-added-content__left form div:last-of-type, .pricing-template-page .user-added-content .user-added-content__left form div:last-of-type {
  color: #165227;
  text-transform: uppercase;
  font-weight: 600;
  font-size: 12px;
}

.form-template-page .user-added-content .user-added-content__left form div:last-of-type div a, .pricing-template-page .user-added-content .user-added-content__left form div:last-of-type div a {
  font-size: 12px;
  text-decoration: underline;
}

.form-template-page .user-added-content .user-added-content__left p:first-of-type, .pricing-template-page .user-added-content .user-added-content__left p:first-of-type {
  font-size: 28px;
}

.form-template-page .user-added-content .user-added-content__left form input::placeholder,
.form-template-page .user-added-content .user-added-content__left form select::placeholder,
.form-template-page .user-added-content .user-added-content__left form textarea::placeholder,
.pricing-template-page .user-added-content .user-added-content__left form input::placeholder,
.pricing-template-page .user-added-content .user-added-content__left form select::placeholder,
.pricing-template-page .user-added-content .user-added-content__left form textarea::placeholder {
  color: #165527;
  background: #fff;
  font-weight: 500;
}

.form-template-page .user-added-content .user-added-content__left form button, .pricing-template-page .user-added-content .user-added-content__left form button {
  font-size: 12px;
  padding: 10px 16px;
  text-transform: uppercase;
  background: #165527;
  border-radius: 5px;
  color: #fff;
  border: 0;
  font-weight: normal;
}

@media screen and (max-width: 700px) {
  .form-template-page .icons__container, .pricing-template-page .icons__container{
    display: inline-grid !important;
    align-items: center;
    justify-content: space-evenly;
    justify-items: center;
    min-width: -webkit-fill-available;
  }
  .form-template-page .icons__container a, .pricing-template-page .icons__container a{
    display: inline-block;
    width: 33%;
    min-width: max-content;

  }
  .form-template-page .icons__container img, .pricing-template-page .icons__container img{
    min-width: -webkit-fill-available;
  }
  .js-mobile-gallery {
    display: block;
  }
}

.form-template-page .icons__container, .pricing-template-page .icons__container{
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

@media screen and (max-width: 700px) {
  .form-template-page .icons__container, .pricing-template-page .icons__container{
    width:100%;
  }
}

.form-template-page .icons__container a div, .pricing-template-page .icons__container a div{
  font-size: 16px;
  text-align: center;
  margin-top: 10px;
}

.form-template-page .icons__container a, .pricing-template-page .icons__container a{
  width: 33%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin: 10px 0;
}

.form-template-page .icons__container a:hover, .pricing-template-page .icons__container a:hover {
  text-decoration: none;
  border: none;
}

.form-template-page .icons__container img, .pricing-template-page .icons__container img{
  width: 46px;
  height: 46px;
  object-fit: contain;
  object-position: center;
}
.form-template-page .brokers__container, .pricing-template-page .brokers__container{
  margin: 30px 0;
}

.form-template-page .brokers__container h3, .pricing-template-page .brokers__container h3{
  font-size: 28px;
  text-align: center;
  margin: 20px 0 50px;
  font-weight: 500;
  font-family: var(--font-family);
}

.form-template-page .brokers__container .js-broker-gallery, .pricing-template-page .brokers__container .js-broker-gallery{
  display: flex;
  align-items: flex-start;
  justify-content: space-evenly;
  overflow: hidden;
}

@media screen and (max-width: 700px) {
  .form-template-page .brokers__container .js-broker-gallery, .pricing-template-page .brokers__container .js-broker-gallery{
    display: inherit;
  }
}

.form-template-page .brokers__container .js-broker-gallery a, .pricing-template-page .brokers__container .js-broker-gallery a{
  display: flex;
  flex-direction: column;
  justify-content: center;
  border: 0 !important;
}

.form-template-page .brokers__container .js-broker-gallery a:hover, .pricing-template-page .brokers__container .js-broker-gallery a:hover{
  text-decoration: none;
  border: none;
}

.form-template-page .brokers__container .js-broker-gallery a img, .pricing-template-page .brokers__container .js-broker-gallery a img{
  width: 70px;
  height: 70px;
  border-radius: 50%;
  object-fit: contain;
  object-position: center;
  align-self: center;
}

.form-template-page .brokers__container .js-broker-gallery p, .pricing-template-page .brokers__container .js-broker-gallery p{
  font-size: 1.5rem;
  color: #000;
  margin-top: 15px;
  font-family: 'source_sans_prolight';
  margin-left:5px;
  margin-right:5px;
}

.form-template-page .brokers__container .js-broker-gallery span, .pricing-template-page .brokers__container .js-broker-gallery span{
  font-size: 12px;
  color: #000;
  font-style: italic;
}

.form-template-page .brokers__container .js-broker-gallery p:before, .pricing-template-page .brokers__container .js-broker-gallery p:before{
  content: '\201C';
  font-size: 18px;
  line-height: 12px;
  color: #165527;
  font-family: 'source_sans_probold', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.form-template-page .brokers__container .js-broker-gallery p:after, .pricing-template-page .brokers__container .js-broker-gallery p:after {
  content: '\201E';
  font-size: 18px;
  line-height: 12px;
  color: #165527;
  font-family: 'source_sans_probold', 'Helvetica Neue', Helvetica, arial, sans-serif;
}

.form-template-page .links__container a, .pricing-template-page .links__container a{
  color: #165527;
  padding: 9px 30px;
  border: 0.1rem solid #165527;
  border-radius: 5px;
  font-size: 12px;
}

body > div.container > div.content-divider > div > div.col-sm-8.col-md-9.print-col-12.padding-left > div.user-added-content.links__container > div > a {
  border-bottom: 0.1rem solid #165527;
}

.form-template-page .links__container h3, .pricing-template-page .links__container h3{
  text-align: center;
  font-size: 22px;
  margin: 30px 0;
}

.form-template-page .links__container div, .pricing-template-page .links__container div{
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}

.js-mobile-gallery {
  display: none;
}

@media screen and (max-width: 1199px) {
  .type-haldus .js-gallery {
    display: none;
  }

  .type-haldus .js-mobile-gallery {
    display: block;
  }

  .form-template-page .user-added-content .user-added-content__left, .pricing-template-page .user-added-content .user-added-content__left{
    margin: 0 20px;
  }
}

.form-tmp-mobile-banner {
  display: block;
}
.form-tmp-banner {
  display: none;
}

@media screen and (min-width: 768px) {
  .form-tmp-mobile-banner {
    display: none;
  }
  .form-tmp-banner {
    display: block;
  }
}
@media screen and (max-width: 1200px) {
  .form-template-page .links__container div, .pricing-template-page .links__container div{
    flex-direction: column;
  }

  .form-template-page .links__container a, .pricing-template-page .links__container a{
    margin: 10px 0;
    width: 100%;
    text-align: center;
  }
  body > div.container > div.content-divider > div > div.col-sm-8.col-md-9.print-col-12.padding-left > div.user-added-content.links__container > div > a {
    border-bottom: 0.1rem solid #165527;
  }

  .form-template-page .brokers__container .js-broker-gallery p, .pricing-template-page .brokers__container .js-broker-gallery p,
  .form-template-page .brokers__container .js-broker-gallery span, .pricing-template-page .brokers__container .js-broker-gallery span{
      width: 50vw;
      margin-right: auto;
      margin-left: auto;
  }
}

.form-template-page .emergency__container, .pricing-template-page .emergency__container{
  color: #dc0c24;
  font-size: 26px;
  text-align: center;
  font-weight: bold;
  margin: 30px 0;
}

.form-template-page .emergency__container a, .pricing-template-page .emergency__container a{
  color: #dc0c24;
  font-size: 26px;
  font-weight: bold;
  text-decoration: underline;
  border: 0 !important;
}

@media screen and (max-width: 700px) {
  .form-template-page .emergency__container, .pricing-template-page .emergency__container{
    font-size: 23px;
  }
  .form-template-page .emergency__container a, .pricing-template-page .emergency__container a {
    font-size: 23px;
  }
}

.form-template-page .emergency__container a:hover, .pricing-template-page .emergency__container a:hover {
  border: 0;
}

.form-template-page .form__buttons__container, .pricing-template-page .form__buttons__container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #165227;
  font-size: 12px;
}

.form-template-page .form-link, .pricing-template-page .form-link {
  font-size: 12px;
  padding: 10px 16px;
  text-transform: uppercase;
  border-radius: 5px;
  color: #165527;
  border: 1px solid #165227;
  font-weight: normal;
  text-decoration: none !important;
}

.form-template-page .footer__text, .pricing-template-page .footer__text {
  padding-top: 5px;
  text-align: center;
  margin-left: 25%;
}

.form-template-page .footer__text p, .pricing-template-page .footer__text p{
  font-size: 16px;
  color: #165527;
  text-align: center;
}

.form-template-page .content-divider, .pricing-template-page .content-divider {
  margin-bottom: 60px;
}

.form-template-page .footer__text::before, .pricing-template-page .footer__text::before {
  content: '';
  background-image: url('../img/footer-text-line.svg');
  width: 620px;
  height: 6px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  margin: 20px auto;
  display: block;
}

@media screen and (max-width: 1200px) {
  .form-template-page .footer__text::before, .pricing-template-page .footer__text::before {
    width: 400px;
  }
}

@media screen and (max-width: 760px) {
  .form-template-page .footer__text, .pricing-template-page .footer__text {
    margin-left: 0;
  }
}

@media screen and (max-width: 700px) {
  .form-template-page .footer__text::before, .pricing-template-page .footer__text::before  {
    width: 350px;
  }
}

.fa-heart:before {
  content: '\f004';
  color: red;
  font-family: 'FontAwesome';
}

.type-haldus.form-template-page
  .user-added-content
  .user-added-content__left
  form
  div:last-of-type
  div
  a,
.type-vahendamine.form-template-page
  .user-added-content
  .user-added-content__left
  form
  div:last-of-type
  div
  a {
  font-size: 16px;
}

@media screen and (max-width: 700px) {
  .type-haldus.form-template-page .form__buttons__container,
  .type-vahendamine.form-template-page .form__buttons__container {
    flex-direction: column;
  }
  .form-template-page .primary-heading, .pricing-template-page .primary-heading {
    font-size: min(max(30px, 4vw), 44px);
    word-break: break-word;
    margin: 16px 0 0;
  }
}

.ws-mobile-image {
  display: none;
}
@media screen and (max-width: 768px) {
  .ws-mobile-image {
    display: inherit;
  }
  .ws-desktop-image {
    display: none;
  }
}

/* Contact form 7 styles */
.wpcf7 .wpcf7-form-control.wpcf7-text,
.wpcf7 .wpcf7-form-control.wpcf7-select {
  width: 100%;
  height: 42px;
  padding: 10px;
  border: 1px solid #165528;
  border-radius: 5px;
  font-size: 14px;
}

.wpcf7 .wpcf7-form-control.wpcf7-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg width='17' height='10' viewBox='0 0 17 10' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15.5 1.50006L8.5 8.50006L1.5 1.50006' stroke='%234F4F4F' stroke-width='1.8' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
}

.wpcf7 .wpcf7-form-control.wpcf7-text::placeholder {
  opacity: 0.5;
	color: #165528;
}

.wpcf7 .wpcf7-not-valid-tip {
  display: block;
  margin-top: 4px;
  color: #dc3232;
  font-size: 12px;
  font-weight: normal;
}

.wpcf7 label {
  width: 100%;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.2;
  color: #165528;
}

.wpcf7 .wpcf7-list-item {
  margin: 0;
}

.wpcf7 .wpcf7-list-item .wpcf7-list-item-label,
.wpcf7 .wpcf7-list-item .wpcf7-list-item-label a {
	font-size: 16px;
	line-height: 1.2;
	color: #337AB7;
}

.wpcf7 .wpcf7-list-item label {
  display: flex;
  align-items: center;
	gap: 6px;
}

.wpcf7 .wpcf7-list-item label input {
	margin: 0;
}

.wpcf7 .wpcf7-submit {
  background: #165528;
  border-radius: 5px;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
	padding: 10px;
	border: none;
	text-transform: uppercase;
}


/* price table styles */

:root {
  --main-bg-color: #ffffff;
  --main-text-color: #000000;
  --highlight-color: #aacf55;
  --highlight-text-color: rgba(255, 255, 255, 0);
  --checkmark-color: #20552e;
  --header-bg-color: #227346;
  --header-text-color: #FFFFFF;
  --price-color: #f43f54;
  --button-bg-color: #006136;
  --button-text-color: #FFFFFF;
  --popular-plan-color: #3db25d;
  --blank-bg-color: #fafafa;
  --icon-color: #3db25d;
  --font-family: "Roboto", sans-serif;
}

body.price-template-page {
  background-color: var(--main-bg-color);
  color: var(--main-text-color);
  font-family: var(--font-family);
  line-height: 1.2;
}


body.price-template-page h1,
body.price-template-page h2,
body.price-template-page h3 {
  margin-bottom: 20px;
}

body.price-template-page.form-template-page .brokers__container .js-broker-gallery p {
  font-family: var(--font-family);
  font-weight: 400;
  color: #434343;
  font-size: 16px;
}

body.price-template-page.form-template-page .brokers__container .js-broker-gallery span {
  font-size: 16px;
  color: #434343;
  font-style: italic;
  font-weight: bold;
  font-family: var(--font-family);
}


body.price-template-page .user-added-content b,
body.price-template-page .user-added-content strong,
body.price-template-page b,
body.price-template-page strong{
  font-family: var(--font-family);
  font-weight: 500;
}

body.price-template-page .user-added-content p,
body.price-template-page .user-added-content span{
  font-size: 18px;
  font-family: var(--font-family);
}

@media (min-width: 1440px) {
  .container {
    width: 1400px;
  }
}


body.price-template-page .user-added-content.main__content {
  grid-template-columns: none;
}

body.price-template-page .user-added-content .user-added-content__left{
  margin: 0;
}


.price-table {
  width: 100%;
  border-collapse: collapse;
  border: none;
}

.price-table td {
  border-left: 5px solid var(--main-bg-color);
  border-right: 5px solid var(--main-bg-color);
  font-weight: 400;
}

.price-table h1 {
  font-size: min(max(20px, 4vw), 40px);
  color: var(--button-bg-color);
  word-wrap: break-word;
}

.price-table p {
  font-size: min(max(13px, 2vw), 18px);
  color: #000000;
}

.price-table p b {
  color: var(--button-bg-color);
  font-weight: 700;
  font-family: var(--font-family);
}

.price-table img {
  max-width: 100%;
  max-height: 80px;
  width: auto;
  margin-top: 10px;
}

.price-table text {
  font-size: 20px;
}

.price-table tr td {
  padding: 8px;
  font-size: 14px;
}

.price-table tr td:first-child {
  border-left: none;
}

.price-table tr td:not(:first-child) {
  text-align: center;
}

.price-table .price-row {
  background-color: #F2F1F0;
}

.price-table .price-row:nth-child(4n+1) {
  background-color: white;
}

.price-table .fa-check {
  color: var(--checkmark-color);
}

.price-table .fa-times {
  color: #D8D6E3;
}

.price-table tr:nth-child(2n) td:nth-child(3) {
  background-color: var(--highlight-text-color);
}

.price-table tr.price-table-head {
  background-color: var(--header-bg-color);
  color: var(--header-text-color);
}

.price-table tr.price-table-head td {
  font-size: 16px;
  font-weight: 400;
  font-family: var(--font-family);
}

.price-table-head td:nth-last-child(3) {
  background-color: #227346;
}

.price-table tr:nth-child(2n) td:nth-child(3) {
  background-color: #006136;
}

.price-table-head td:nth-last-child(1) {
  background-color: #024B29;
}

.price-table tr.price-details {
  background-color: #e3eccf;
}

.price-table td.price {
  color: var(--price-color);
  padding: 7px 0 0;
  font-size: 20px;
  font-weight: 400;
  font-family: var(--font-family);
}

.price-table td.price a {
  background-color: var(--button-bg-color);
  color: var(--button-text-color);
  padding: 12px 0;
  font-size: 18px;
  font-weight: 400;
  font-family: var(--font-family);
  text-transform: uppercase;
  display: inline-block;
  text-decoration: none;
  width: 100%;
}

.price-table td.price-table-popular {
  font-family: var(--font-family);
  border-top: 3px solid var(--popular-plan-color);
  color: var(--popular-plan-color);
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 700;
}

.price-table .price-blank {
  background-color: var(--blank-bg-color);
  border: none;
}

.price-table svg {
  width: 90px;
  fill: var(--icon-color);
}

@media screen and (min-width: 1200px) {
  body.price-template-page .primary-heading {
    font-size: 45px;
    text-align: left;
  }
}

@media screen and (max-width: 991px) {
  .price-table tr td {
    font-size: 14px;
  }

  .price-table td.price {
    font-size: 14px;
  }

  .price-table td.price a {
    font-size: 10px;
  }

  .price-table td.price-table-popular {
    font-size: 10px;
  }

  .price-table text {
    font-size: 12px;
  }
  
  .price-table h1 {
    margin-top: 0;
  }


  body.price-template-page.form-template-page .brokers__container .js-broker-gallery p {
text-align: center;
  }
  
  body.price-template-page.form-template-page .brokers__container .js-broker-gallery span {
    text-align: center;
    margin-bottom: 40px;
  }

}

.price-table .price-details {
  background-color: var(--highlight-text-color);
  color: var(--main-text-color);
  font-size: 14px;
  padding: 0;
  height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  opacity: 0;
  visibility: collapse;
}

.price-table .price-details.collapsed {
  display: none;
}

.price-table .price-details.expanded {
  height: 100%;
  /* Adjust the maximum height as needed */
  padding: 8px 24px;
  transition: max-height 0.6s ease, opacity 0.6s ease;
  /* Adjust the transition durations as desired */
  opacity: 1;
  visibility: visible;
}

.price-table .price-row.hover {
  background-color: var(--highlight-color);
  cursor: pointer;
}

.price-table .price-row.active {
  background-color: var(--highlight-color);
}

.hide-first-three-cells {
  background-color: #ffffff;
}

body.price-template-page .user-added-content__right .js-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-gap: 20px;
}

.gform_footer.top_label input.gform_button.button {
  font-size: 14px;
  border: none;
  border-radius: 5px;
  padding: 10px 15px;
  background-color: #165527;
  color: #fff;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: background-color 0.3s;
  text-transform: uppercase;
  display: inline-block;
  width: auto;
  font-weight: 400;
}

body .ginput_container textarea:focus-visible,
body .ginput_container input:focus-visible {
  outline: 2px solid #0d9d36;
}


/* Changing background and border of error div under field */
body .gform_wrapper .gfield_validation_message, 
body .gform_wrapper .validation_message {
    background: #ddecf5;
    border: 1px solid #65a9cb;
	border-radius: 6px;
}

body .gform_wrapper .gform_validation_errors{
	   background: #ddecf5;
    /* border: 1px solid #ffac00; */
	
}

body .gform_wrapper .gform_validation_errors>h2{
	    color: #165227;
    text-transform: uppercase;
		font-weight: bold;
	font-family: var(--font-family);
}

/* "/hindamine" page gform style */
.main__content.pricing .gform_wrapper.gravity-theme .gform_fields {
  grid-row-gap: 0px;
}
.main__content.pricing .gform_fields .gfield {
  margin: 2px 0;
}
.main__content.pricing .gfield .ginput_container {
  margin: 0;
}
.main__content.pricing .gform_wrapper.gravity-theme .gfield_label {
  display: none;
}
.main__content.pricing .gform_wrapper.gravity-theme .gform_footer {
  padding: 0;
}
.main__content.pricing .gform_ajax_spinner {
  width: 30px;
}

/* Gallery grid vahendus */
.new-gallery-list {
  overflow: hidden;
  clear: both;
  margin: -15px;
}

.new-gallery-list.popup-new-gallery {
  display: block;
}

.new-gallery-list .new-items {
  float: left;
  width: 40%;
  padding: 15px;
}

.new-gallery-list .new-items:nth-child(3n+1) {
  width: 60%;
  float: right;
}

.new-gallery-list .new-items:nth-child(6n+1) {
  float: left;
}

.new-gallery-list .new-items:nth-child(3n+1) .new-gall-imagebox {
  height: 516px;
}

.new-gall-imagebox {
  display: block;
  width: 100%;
  height: 243px;
  overflow: hidden;
  position: relative;
}

.new-gall-imagebox img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 5px;
}

@media (min-width:1201px) {
  body.price-template-page .user-added-content__right .js-gallery.new-vahendus-page {
    display: none;
  }
}

@media (max-width:1200px) {
  .new-gallery-list.popup-new-gallery {
    display: none;
  }
}


/* Scroll to contact form position fixed button */
.button-jumper {
  position: fixed;
  bottom: 20px;
  right: 20px;
  border: none;
  border-radius: 5px;
  padding: 12px 15px;
  background-color: #CF2535;
  color: #fff;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: background-color 0.3s;
  animation: jump 1.3s infinite alternate;
  text-transform: uppercase;
}

.button-jumper:hover {
  background-color: #236b38;
  animation-play-state: paused;
}

.hidden {
  opacity: 0;
  pointer-events: none;
}

@keyframes jump {
  0% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-4px);
  }

  100% {
    transform: translateY(0);
  }
}


/* slide content in on the scroll and slide top element in on page load styles */
.fade-up-slide {
  opacity: 0;
  transform: translateY(50px);
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
}

.fade-up-slide.fade-up-slide-show {
  opacity: 1;
  transform: translateY(0);
}


.top-element {
  opacity: 0;
  transform: translateY(50px);
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
}

.top-element.top-element-show {
  opacity: 1;
  transform: translateY(0);
}

@media (min-wdidth: 575px){
  .desktop-not{
    display:none;
  }
}

.inquiry-page__content .wp-block-button__link {
  display: inline-block;
  padding: 8px;
  color: #fff;
  background: #165528;
  font-size: 13px;
  text-transform: uppercase;
  text-decoration: none;
  border-radius: 0;
  font-family: 'source_sans_prosemibold', 'Helvetica Neue', Helvetica, arial, sans-serif;
  border: none;
}

.inquiry-page__content .wp-block-button__link:hover {
  text-decoration: none;
  color: #d5ded8;
}
