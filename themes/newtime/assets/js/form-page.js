/*
	Functions for 'Form contact page' template page
*/
(function () {
  if (!document.body.classList.contains('form-template-page') && !document.body.classList.contains('pricing-template-page')) {
    return;
  }

  // Check `haldus` mobile gallery first
  let imageGalleryEl = $('.js-mobile-gallery');
  if (!imageGalleryEl.length) imageGalleryEl = $('.js-gallery');

  const brokerGalleryEl = $('.js-broker-gallery');

  const sliderOptions = {
    arrows: false,
    mobileFirst: true,
    autoplay: true,
    responsive: [
      {
        breakpoint: 1200,
        settings: 'unslick',
      },
    ],
  };

  // Reinitilize slider if going from destop to mobile
  $(window).on('resize', function() {
 
    /* If we are above mobile breakpoint unslick the slider */
    if ($(window).width() >= 1200) 
    {
       /* Do this only if slider is initialized */
       if (imageGalleryEl.hasClass('slick-initialized') && brokerGalleryEl.hasClass('slick-initialized')) {
        imageGalleryEl.slick('unslick');
        brokerGalleryEl.slick('unslick');
       }
       return;
    }
    /* We are below mobile breakpoint, initialize the slider
       if it is not already initialized */
    else if (!imageGalleryEl.hasClass('slick-initialized')) 
    {
       return imageGalleryEl.slick(sliderOptions);
       
    }
    else if (!brokerGalleryEl.hasClass('slick-initialized')) {
      return brokerGalleryEl.slick(sliderOptions);
    }
 });
 
 $(window).trigger('resize');
})();
