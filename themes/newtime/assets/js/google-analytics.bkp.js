(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
})(window,document,'script','//www.google-analytics.com/analytics.js','ga');

ga('create', 'UA-73457234-1', 'auto'); // pindi
// ga('create', 'UA-73585733-1', 'auto'); // test.pindi
// ga('send', 'event', 'category', 'action', 'label');

ga('send', 'pageview');

$(function(){
	$('body').on('click', '.ga-banner', function(){
		let label = $(this).find('.ga-label').text();
		if ( ! label) label = 'Nimetu';
		ga('send', 'event', 'banner', 'klikk', label);

	}).on('click copy', 'a[href^="mailto:"]', function(event){
		let email = this.href.substr("mailto:".length);
		let type = (event.type == 'click') ? 'klikk' : 'kopeerimine';
		ga('send', 'event', 'meiliaadress', type, email);

	}).on('click copy', 'a[href^="tel:"]', function(event){
		let nr = this.href.substr("tel:".length);
		let type = (event.type == 'click') ? 'klikk' : 'kopeerimine';
		ga('send', 'event', 'telnr', type, nr);
	});

	document.querySelectorAll('.js-form-analytics')?.forEach((el) => {
		el.addEventListener('submit', (e) => {
			let form = e.currentTarget;
			if (form.dataset.gaCategory && form.dataset.gaIdentifier) {
				ga('send', 'event', form.dataset.gaCategory, 'kontakt', form.dataset.gaIdentifier);
			}
		});
	});

    document.addEventListener( 'wpcf7submit', function( event ) {
	
//        if ( '737069' == event.detail.contactFormId ) {
            // do something productive
            const e_type = 'müügihinnangu päring';
	    ga(
		'send', 
		'event', 
		'Form', 
		'Submit', 
		e_type,
		event.detail.contactFormId
	    );
//        }
    }, false );
});

function gaJoinNewsletter() {
	ga('send', 'event', 'uudiskiri', 'liitumine', 'pindi uudiskiri');
}

function gaTakeContact(item){
	var category, label;
	category = item.attr('ga-category');
	label = item.attr('ga-identifier');
	ga('send', 'event', category, 'kontakt', label);
}
