<?php
/**
 * Search results page
 */
?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>
    

    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-broker">
    
    <?php get_template_part('blocks/b_header'); ?>

    <div class="container">
        <div class="content-divider">
            <div class="row">
                <div class="col-sm-4 col-md-3 hidden-xs">

                    <?php get_template_part('blocks/b_sidebar_primary'); ?>
                    
                </div>
                <div class="col-sm-8 col-md-9 print-col-12 padding-left">

                    <?php get_template_part('blocks/b_breadcrumbs'); ?>
                    
                    <div class="result">
                        <?php if (have_posts()) : ?>
                            <div class="result-header margin-none">
                                <div class="result-header-heading">
                                    <h1><?php pll_e('Search results'); ?></h1>
                                </div>
                                <div class="result-header-found">
                                    <?php echo pll__('Found') . ' ' . $wp_query->found_posts . ' ' . ($wp_query->found_posts == 1 ? pll__('result') : pll__('results')); ?>
                                </div>
                            </div>
                            <ul class="result-list">
                                <?php while (have_posts()) : the_post(); ?>
                                    <li class="result-item">
                                        <a class="result-item-link" href="<?php echo get_the_permalink(); ?>">
                                            <h3 class="result-item-heading">
                                                <?php the_title(); ?>
                                            </h3>
                                        </a>
                                        <div class="result-item-content">
                                            <?php                                            
                                                if (function_exists('relevanssi_the_excerpt')) {
                                                    relevanssi_the_excerpt();
                                                };
                                            ?>
                                        </div>
                                    </li>
                                <?php endwhile; ?>
                            </ul>
                        <?php else: ?>
                            <ul class="result-list">
                                <li class="result-item">
                                    <h3 class="result-item-heading">
                                        <?php pll_e('No results found!'); ?>
                                    </h3>
                                </li>
                            </ul>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    

    <?php get_template_part('blocks/b_mob_social_icons'); ?>

    <?php get_template_part('blocks/b_footer'); ?> 

    <?php get_template_part('blocks/b_javascripts'); ?> 

</body>
</html>
