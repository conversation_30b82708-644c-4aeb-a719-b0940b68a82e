<?php
/**
 * The Template for displaying all single job offer
 */

$bottom_button = get_field('bottom_button');

?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>
    

    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-employ">

    <?php get_template_part('blocks/b_header'); ?>

    <div class="container">
        <div class="content-divider">
            <div class="row">
                <div class="col-sm-4 col-md-3 hidden-xs">
                    <?php get_template_part('blocks/b_sidebar_primary'); ?>
                </div>
                <div class="col-sm-8 col-md-9 print-col-12 padding-left">

                    <?php get_template_part('blocks/b_breadcrumbs'); ?>

                    <?php
                        //Get id for info page
                        $jobOffersArgs = [
                            'post_type' => 'page',
                            'fields' => 'ids',
                            'nopaging' => true,
                            'meta_key' => '_wp_page_template',
                            'meta_value' => 'page-templates/job_offers.php'
                        ];
                        $jobOffersId = array_values(get_posts($jobOffersArgs))[0];
                    ?>
                    <div class="text-right">
                        <a href="<?php echo get_the_permalink($jobOffersId); ?>" class="main-button"><?php pll_e('Back'); ?></a>
                    </div>
                    
                    <div class="employ">
                        <?php 
                            $fields = get_fields();
                            if ($fields['banner_image']) {
                            ?>
                            <div class="employ-thumbnail">
                                <img src="<?php echo $fields['banner_image']['sizes']['custom-banner-image']; ?>" alt="<?php echo $fields['banner_image']['alt']; ?>" class="employ-thumbnail-img">
                            </div>
                        <?php } ?>
                        <div class="employ-heading">
                            <h1>
                                <?php echo get_the_title(); ?>
                            </h1>
                        </div>
                        <div class="employ-date">
                            <ul class="employ-date-list">
                                <li class="employ-date-item"><?php echo pll__('Added at') . ': ' . get_the_date('d.m.Y'); ?></li>
                                <?php if ($fields['due_date']){ ?>
                                    <li class="employ-date-item"><?php echo pll__('Due date') . ': ' . $fields['due_date']; ?></li>
                                <?php } ?>

                                <?php $locations = wp_get_post_terms( get_the_ID(), 'job_location'); ?>
                                <?php if ($locations){
                                    $locationsList = '';
                                    // add label
                                    $locationsList .= pll__('Location') . ': ';
                                    
                                    foreach ($locations as $key => $val) {
                                        if ($key == 0) {
                                            // add just name for first item
                                            $locationsList .= $val->name;
                                        } else{
                                            // prepend coma for rest
                                            $locationsList .= ', ' . $val->name;
                                        }
                                    }
                                    echo '<li class="employ-date-item">' . $locationsList . '</li>';
                                } ?>
                            </ul>
                        </div>
                        <div class="user-added-content">
                            <?php
                                while ( have_posts() ) : the_post();
                                the_content();
                                endwhile;
                            ?>
                        </div>

                        <?php if ($bottom_button == 'email'): ?>
                            <a class="btn main-button" href="mailto:<EMAIL>">
                                <?php pll_e('Contact us') ?>
                            </a>
                        <?php elseif ($bottom_button == 'link' && ($link = get_field('button_link'))): ?>
                            <a class="btn main-button" href="<?php echo $link['url'] ?>">
                                <?php echo $link['title'] ?>
                            </a>
                        <?php else: ?>
                            <span class="btn main-button employ-fb-share js-fb-share"><?php pll_e('Share on facebook') ?></span>
                        <?php endif ?>
                    </div>

                </div>
            </div>
        </div>
    </div>
    

    <?php get_template_part('blocks/b_mob_social_icons'); ?>

    <?php get_template_part('blocks/b_footer'); ?> 

    <?php get_template_part('blocks/b_javascripts'); ?> 

</body>
</html>
