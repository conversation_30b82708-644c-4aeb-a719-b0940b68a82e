<?php
/**
 * The Template for displaying all market survey posts
 */
?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>
    

    <?php get_template_part('blocks/b_head'); ?>
</head>
<body class="page-article">

    <?php get_template_part('blocks/b_header'); ?>

    <div class="container">
        <div class="col-sm-4 hidden-xs hidden-print">

            <?php get_template_part('blocks/b_sidebar_primary'); ?>

        </div>
        <div class="col-sm-8">

            <?php get_template_part('blocks/b_breadcrumbs'); ?>
            
            <?php
            $pages = get_pages(array(
                'post_type' => 'page',
                'fields' => 'ids',
                'nopaging' => true,
                'meta_key' => '_wp_page_template',
                'meta_value' => 'page-templates/market_surveys.php'
            ));
            ?>
            <div class="visible-xs text-center">
                <a href="<?php echo get_permalink(array_values($pages)[0]->ID); ?>" class="btn main-button main-article-button hidden-print"><?php pll_e('Back'); ?></a>
            </div>
            <h1 class="primary-heading"><?php echo get_the_title(); ?></h1>

            <div class="article">
                <div class="article-date">
                    <?php echo get_the_date('d.m.Y'); ?>
                </div>
                <a href="<?php echo get_permalink(array_values($pages)[0]->ID); ?>" class="btn main-button main-article-button hidden-print hidden-xs"><?php pll_e('Back'); ?></a>
            </div>
            <div class="user-added-content">
                <?php 
                $attr = array(
                    'class' => 'featured-image',
                );
                the_post_thumbnail('medium', $attr); 
                ?>
                <?php
                    while ( have_posts() ) : the_post();
                    the_content();
                    endwhile;
                ?>
            </div>
            
        </div>
    </div>

    <?php get_template_part('blocks/b_mob_social_icons'); ?>

    <?php get_template_part('blocks/b_footer'); ?> 

    <?php get_template_part('blocks/b_javascripts'); ?> 

</body>
</html>
