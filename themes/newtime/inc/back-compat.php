<?php

function newtime_switch_theme() {
	switch_theme( WP_DEFAULT_THEME, WP_DEFAULT_THEME );
	unset( $_GET['activated'] );
	add_action( 'admin_notices', 'newtime_upgrade_notice' );
}
add_action( 'after_switch_theme', 'newtime_switch_theme' );

function newtime_upgrade_notice() {
	$message = sprintf( __( 'Newtime theme requires at least WordPress version 3.6. You are running version %s. Please upgrade and try again.', 'newtime' ), $GLOBALS['wp_version'] );
	printf( '<div class="error"><p>%s</p></div>', $message );
}


function newtime_customize() {
	wp_die( sprintf( __( 'Newtime theme requires at least WordPress version 3.6. You are running version %s. Please upgrade and try again.', 'newtime' ), $GLOBALS['wp_version'] ), '', array(
		'back_link' => true,
	) );
}
add_action( 'load-customize.php', 'newtime_customize' );


function newtime_preview() {
	if ( isset( $_GET['preview'] ) ) {
		wp_die( sprintf( __( 'Newtime theme requires at least WordPress version 3.6. You are running version %s. Please upgrade and try again.', 'newtime' ), $GLOBALS['wp_version'] ) );
	}
}
add_action( 'template_redirect', 'newtime_preview' );
