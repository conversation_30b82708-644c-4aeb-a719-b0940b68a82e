<?php 

function pindi_contact_form( $atts = array() ) {
	// if (!is_user_logged_in()) return;

	extract(shortcode_atts(array(
		'title' => null,
		'recipient' => null,
		'id' => null,
		'toggle' => null,
		'subject' => null,
	), $atts));

	ob_start();
	?>
	<div class="margin-bottom <?php echo ($toggle)? 'hidden' : ''; ?> <?php echo ($toggle)? 'toggle-'.$toggle : '' ?>">
		<?php if ($title): ?>
		<h2 class="secondary-heading-w-border"><?php echo $title ?></h2>
		<?php endif; ?>

		<form class="js-wp-ajax-form" action="" method="POST">
			<input type="hidden" name="action" value="consultation_form_submit">
			
			<?php if ($recipient): ?>
				<input type="hidden" name="recipient" value="<?php echo $recipient ?>" />
			<?php endif; ?>
			<?php if ($subject): ?>
				<input type="hidden" name="subject" value="<?php echo $subject ?>" />
			<?php endif; ?>

			<div class="nna-dropdown dropdown-filter dropdown">
				<input type="hidden" name="asset_type" value="<?php pll_e('House'); ?>">
				<label class="filter-label"><?php pll_e('Asset Type'); ?>:</label>

				<button
					id="asset-type-dropdown"
					class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle gray"
					type="button"
					aria-expanded="false"
					data-toggle="dropdown"
				>
					<?php pll_e('Apartment'); ?>
					<i class="dropdown-filter-btn-icon"></i>
				</button>

				<ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="asset-type-dropdown">
					<li class="nna-filter-option filter-dropdown-option"
						filter-value="<?php pll_e('Apartment'); ?>"><?php pll_e('Apartment'); ?></li>
					<li class="nna-filter-option filter-dropdown-option"
						filter-value="<?php pll_e('House'); ?>"><?php pll_e('House'); ?></li>
					<li class="nna-filter-option filter-dropdown-option"
						filter-value="<?php pll_e('Land'); ?>"><?php pll_e('Land'); ?></li>
					<li class="nna-filter-option filter-dropdown-option"
						filter-value="<?php pll_e('Commercial Place'); ?>"><?php pll_e('Commercial Place'); ?></li>
				</ul>
			</div>

			<div>
				<label class="filter-label"><?php pll_e('Asset Address'); ?>:</label>
				<input class="js-input js-required mailto-form-input" type="text" name="asset_address" />
			</div>

			<div>
				<label class="filter-label text-capitalize"><?php pll_e('Name'); ?>:</label>
				<input class="js-input js-required mailto-form-input" type="text" name="customer_name" />
			</div>

			<div>
				<label class="filter-label"><?php pll_e('Email Field'); ?>:</label>
				<input class="js-input js-required mailto-form-input" type="text" name="customer_email" />
			</div>

			<div>
				<label class="filter-label text-capitalize"><?php pll_e('Phone'); ?>:</label>
				<input class="js-input js-required mailto-form-input" type="text" name="customer_phone" />
			</div>

			<div>
				<label class="filter-label text-capitalize"><?php pll_e('Notes'); ?>:</label>
				<textarea class="js-input mailto-form-textarea" name="customer_message"></textarea>
			</div>

			<button class="js-wp-ajax-form-submit btn main-button mailto-button" type="submit"><?php pll_e('Send'); ?></button>
			<div class="js-response"></div>
		</form>
	</div>

	<?php
	$output = ob_get_contents();
	ob_end_clean();

	return $output;
}
add_shortcode('pindi-form', 'pindi_contact_form');

function enqueue_shortcode_scripts(){
	global $post;
	if(has_shortcode( $post->post_content, 'pindi-form') && ( is_single() || is_page() ) ){
		// wp_enqueue_style('my-custom-css', get_template_directory_uri().'/css/custom.css');
		wp_enqueue_script('pindi-form-js', get_template_directory_uri().'/assets/js/pindi-form.js');
	}
}
add_action( 'wp_enqueue_scripts', 'enqueue_shortcode_scripts' );

add_shortcode('pindi-kontakt', function($atts = []) {
	global $infoPageId;
	$contact = [
		'contactID' => $infoPageId,
		'mobile' => get_field('phone_number', pll_get_post($infoPageId)),
		'email' => get_field('email', pll_get_post($infoPageId)),
		'name' => false,
		'link' => false,
		'title' => false,
		'occupation' => false,
		'phone' => false,
		'thumbnail' => false,
		'languages' => false,
	];

	$args = shortcode_atts([
		'gacategory' => 'Lühikood',
		'buttonsClass' => '',
	], $atts);

	ob_start();
	?>
	<div class="js-contact-links contact-links">
		<div class="make-contact js-make-contact">
			<div class="make-contact-lightbox js-close-make-contact">
			</div>
			<div class="make-contact-box mailto">
				<div class="mailto-heading">
					<div class="gallery-close-button mailto-close-button js-close-make-contact"></div>
					<div class="mailto-heading-header"><?php pll_e('Contact us') ?></div>
				</div>
				<div class="mailto-content clearfix">
					<div class="mailto-content-contact">
						<div class="contact">
							<div class="contact-info contact-info-block">
								<?php
									if ($contact['name']) {
										echo '<div class="contact-info-name">' . $contact['name'] . '</div>';
									}

									if ($contact['occupation']){
										echo '<div class="contact-info-title">' . $contact['occupation'] . '</div>';
									}

									if ($contact['languages']){
										echo '<div class="contact-languages">';
										echo '<div class="flags">';
										echo '<ul class="flags-list">';
										foreach ($contact['languages'] as $lang) {
											echo '<li class="flag-item flag-' . $lang . '"></li>';
										}
										echo '</ul>';
										echo '</div>';
										echo '</div>';
									}
								?>
								<ul class="contact-details">
									<?php if ($contact['phone']): ?>
										<li class="contact-details-item"><a class="contact-details-item-link" href="tel:<?php echo $contact['phone']; ?>"> <!-- <?php pll_e('Phone'); ?>: --><?php echo $contact['phone'] ?><a></li>
									<?php endif; ?>
									<?php if ($contact['mobile']): ?>
										<li class="contact-details-item"><a class="contact-details-item-link" href="tel:<?php echo $contact['mobile']; ?>"><!-- <?php pll_e('Mobile'); ?>: --><?php echo $contact['mobile'] ?></a></li>
									<?php endif; ?>
									<?php if ($contact['email']): ?>
										<li class="contact-details-item"><!-- <?php pll_e('Email'); ?>: --><a class="contact-details-item-link" href="mailto:<?php echo $contact['email']; ?>"><?php echo $contact['email'] ?></a></li>
									<?php endif; ?>
								</ul>
							</div>
						</div>
					</div>

					<?php
						$gaIdentifier = '';
						$curUrl = get_the_permalink();
						$siteUrl = site_url();
						$gaIdentifier = str_replace($siteUrl, '', $curUrl);
					?>
					<form class="ga-contact-form js-contact-person-form mailto-content-form" identifier-action="sendMailTakeContact" ga-identifier="<?php echo $gaIdentifier; ?>" ga-category="<?php echo $args['gacategory']; ?>">
						<div class="js-form-submitted-message mailto-form-after-submit-message">
						</div>
						<div class="mailto-form-group js-field">
							<input type="hidden" name="mailForm[url]" value="<?php echo $curUrl;?>">
							<input name="mailForm[id]" type="text" value="<?php echo $contact['contactID']; ?>" hidden>
							<label class="mailto-label">
								<?php pll_e('Name'); ?>:
							</label>
							<input name="mailForm[name]" type="text" class="js-input js-required mailto-form-input">
							<span class='js-error'></span>
						</div>
						<div class="mailto-form-group mailto-form-group-last js-field">
							<label class="mailto-label">
								<?php pll_e('Email'); ?>:
							</label>
							<input name="mailForm[email]" type="text" class="js-input js-required js-email mailto-form-input">
							<span class='js-error'></span>
						</div>
						<div class="clearfix"></div>

						<div class="mailto-form-group-full js-field">
							<label class="mailto-label">
								<?php pll_e('Message content'); ?>:
							</label>
							<textarea name="mailForm[message]" class="js-input js-required mailto-form-textarea"></textarea>
							<span class='js-error'></span>
						</div>
						<button class="js-contact-person-form-submit btn main-button mailto-button" type="button"><?php pll_e('Send message'); ?></button>
					</form>
				</div>
			</div>
		</div>

		<button class="btn main-button contact-links-button js-make-contact-button <?php echo $args['buttonsClass']; ?>">
			<?php pll_e('Take contact') ?>
		</button>
	</div>
	<?php
	$output = ob_get_contents();
	ob_end_clean();

	return $output;
});

add_shortcode('pindi-kampaania', function($args = []) {
	// Add shortcode default arguments if missing
	$args = shortcode_atts([
		// Google analytic info
		'gacategory' => 'Lühikood',
		'gaidentifier' => str_replace(site_url(), '', get_the_permalink()),
		// Page id where to get email from
		// Default to info page
		'pageid' => 544,
	], $args);

	ob_start();
	include TEMPLATEPATH . '/page-templates/contact_pindimaakler.php';
	$output = ob_get_contents();
	ob_end_clean();

	return $output;
});
