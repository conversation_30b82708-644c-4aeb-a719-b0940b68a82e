<?php

class WS_Gravity_Forms {

    const ROW_NR = 'gform_row_nr';
    const ROW_NR_ID = 1000;

    function __construct() {
        add_filter('gform_submit_button', [$this, 'modify_footer'], 10, 2);
        add_filter('gform_pre_process', [$this, 'pre_process'], 10, 2);

        // Checking if row nr exists in options
        // If not then creating it
        $gform_row_nr = get_option( WS_Gravity_Forms::ROW_NR );
        if ($gform_row_nr === false) {
            add_option( WS_Gravity_Forms::ROW_NR, 1000 );
        }
    }

    function modify_footer($html, $form) {
        if ($form['title'] !== 'Hindamise vorm') {
            return $html;
        }
    
        $button_link = get_field('form_link');
        $button_text = pll__('<PERSON><PERSON> hinna<PERSON><PERSON>');
        $phone_nrs = get_field('form_phone_numbers');
        $phone_html = '';
        if ($phone_nrs) {
            $nrs = [];
            foreach (explode(';', $phone_nrs) as $number) {
                $trimed = str_replace(' ', '', $number);
                $nrs[] = "<a href='tel:{$trimed}'>{$number}</a>";
            }
    
            $phone_html = sprintf('%s: %s', pll__('Või helistage meile'), implode('; &nbsp;', $nrs));
        }
    
        if ($button_link) {
            $html = "<div class='form__buttons__container'>
                {$html}
                <a target='_blank' class='form-link' href='{$button_link}'>
                    {$button_text}
                </a>
            </div>
            <div>$phone_html</div>";
        } else {
            $html = "<div class='form__buttons__container'>
                {$html}
                <a target='_blank' class='form-link' href='{$button_link}'>
                    {$button_text}
                </a>
            </div>
                <div class='phone-margin-minus'>{$phone_html}</div>
            </div>";
        }
    
        return $html;
    }

    function pre_process($submitted_values, $form) {
        // Adding custom field and setting it's value in POST
        if (isset($submitted_values['fields'])) {
            // Creating field and adding to list
            $field = GF_Fields::create( [
                'type' => 'hidden',
                'label' => 'Nr.',
                'id' => WS_Gravity_Forms::ROW_NR_ID,
            ] );
            $submitted_values['fields'][] = $field;

            // Setting value in POST before form is used by plugins
            $row_nr = WS_Gravity_Forms::newRowNr();
            $_POST['input_' . $field->id] = $row_nr;
        }
        return $submitted_values;
    }

    /**
     * Creating new row nr
     */
    public static function newRowNr() {
        $gform_row_nr = get_option( 'gform_row_nr' );
        $gform_row_nr += 1;

        update_option( WS_Gravity_Forms::ROW_NR, $gform_row_nr );

        return $gform_row_nr;
    }
}
