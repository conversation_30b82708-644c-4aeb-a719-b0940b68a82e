<?php

/**
 * Broker post type settings
 */
class ws_broker_settings
{
	function __construct()
	{
		add_filter('manage_broker_posts_columns', [$this, 'add_description_column']);
		add_action('manage_broker_posts_custom_column', [$this, 'add_description_column_value'], 20, 2);
	}

	function add_description_column($columns) {
		$columns['description'] = 'Kirjeldus';
		return $columns;
	}

	function add_description_column_value($column_name, $id) {
		if ('description' == $column_name) {
			echo get_field('occupation', $id);
		}
	}
}
