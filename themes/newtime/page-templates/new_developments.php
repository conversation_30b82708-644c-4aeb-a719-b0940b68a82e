<?php /* Template Name: New developments */ ?>


<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>


    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-new-developments">

    <?php get_template_part('blocks/b_header'); ?>
    <div class="container">
        <div class="content-divider">
            <div class="row">
                <div class="col-sm-4 col-md-3 hidden-xs">

                    <?php get_template_part('blocks/b_sidebar_primary'); ?>

                </div>
                <div class="col-sm-8 col-md-9 print-col-12 padding-left">

                    <?php get_template_part('blocks/b_breadcrumbs'); ?>

                    <h1 class="primary-heading developments-heading">
                        <?php the_title(); ?>
                    </h1>

                    <form class="clarify">
                        <?php
                            $argsTerms = array();
                            $argsObjectType = array();

                            $taxonomy = 'new-developments-property-type';
                            if (isset($_GET['filters'])) {
                                $query = $_GET['filters'];
                                if (isset($query['term']) && !empty($query['term'])) {
                                    $term = explode('|', $query['term']);
                                    if ($term[0] == 'term') {
                                        // category sort
                                        $argsTerms = array(
                                            'tax_query' => array(
                                                array(
                                                    'taxonomy' => $taxonomy,
                                                    'field' => 'term_id',
                                                    'terms' => $term[1],
                                                ),
                                            ),
                                        );
                                    } elseif($term[0] == 'objectTypeMeta'){
                                        // acf custom fields sort
                                        $argsTerms = array(
                                            'meta_query' => array(
                                                array(
                                                    'key' => 'object_type',
                                                    'value' => $term[1],
                                               ),
                                            ),
                                        );
                                    }
                                }

                            }
                            $terms = get_terms($taxonomy);

                            //Get id for info page
                            $singleNewDevelopmentArgs = [
                                'post_type' => 'new-developments',
                                'fields' => 'ids',
                                'nopaging' => true,
                                'posts_per_page'   => 1,
                            ];
                            $singleNewDevelopmentId = array_values(get_posts($singleNewDevelopmentArgs))[0];
                            $objectTypesObject = get_field_object('object_type', $singleNewDevelopmentId);
                            $objectTypes = $objectTypesObject['choices'];
                            unset($objectTypes['none']);
                        ?>
                        <input name="filters[term]" type="text" value="<?php echo (isset($query['term']) && !empty($query['term']) ? $query['term'] : ''); ?>" hidden>

                        <div class="clarify-dropdown dropdown">
                            <button class="btn dropdown-filter-btn dropdown-toggle products-filter-misc-sort dev-products-filter-misc-sort" type="button" id="dropdownMenu" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <?php
                                    if (isset($query['term']) && !empty($query['term'])) {
                                        $term = explode('|', $query['term']);
                                        if ($term[0] == 'term') {
                                            $term = get_term_by('id', $query['term'], $taxonomy);
                                            echo $term->name;
                                        } elseif($term[0] == 'objectTypeMeta'){
                                            echo NnaTranslations::getInstance()->__($term[1] . ' (new dev)');
                                        }
                                    } else{
                                        pll_e('All object types');
                                    }
                                ?>
                                <i class="dropdown-filter-btn-icon"></i>
                            </button>
                            <ul class="dropdown-menu clarify-dropdown-menu" aria-labelledby="dropdownMenu">
                                <?php
                                    echo '<li class="js-filter-dropdown-item clarify-dropdown-option" identifier="">' . pll__('All object types') . '</li>';
                                    foreach ($objectTypes as $key => $objectType) {
                                        echo '<li class="js-filter-dropdown-item clarify-dropdown-option" identifier="objectTypeMeta|' . $key . '">' . NnaTranslations::getInstance()->__($key . ' (new dev)') . '</li>';
                                    }
                                    echo '<li class="dropdown-border"></li>';
                                    foreach ($terms as $term) {
                                        echo '<li class="js-filter-dropdown-item clarify-dropdown-option" identifier="term|' . $term->term_id . '">' . $term->name . '</li>';
                                    }
                                ?>
                            </ul>
                        </div>
                        <div class="clarify-search">
                            <input name="filters[searchQuery]" type="text" class="js-search-new-devs clarify-search-input" placeholder="otsi nime järgi...">
                            <span class="fa fa-search clarify-search-icon"></span>
                        </div>
                    </form>

                    <?php
                        $argsDefault = array(
                            'post_type' => 'new-developments',
                            'post_status' => 'publish',
                            'posts_per_page' => '-1',
                            'orderby' => 'date',
                            'order' => 'DESC',
                        );
                        $args = array_merge_recursive($argsDefault, $argsTerms);
                        $posts_array = query_posts($args);
                    ?>

                    <div class="js-search-results developments">
                        <?php
                            if ($posts_array) {
                                foreach ($posts_array as $single_post) {
                                    $link = get_field('new_development_link', $single_post->ID);
                                    $linkText = get_field('new_development_display_link', $single_post->ID);
                                    ?>
                                    <div class="developments-block">
                                        <div class="estate">
                                            <div class="estate-thumbnail">
                                                <a <?php echo ($link ? 'target="_BLANK"' : ''); ?> href="<?php echo ($link ? $link : get_the_permalink($single_post->ID)); ?>" class="estate-thumbnail-link">
                                                    <div class="offer-thumbnail-img-wrap">
                                                        <?php
                                                            $images = get_field('product_images', $single_post->ID);
                                                            if ($images) {
                                                                $firstImage = $images[0]['image']; ?>
                                                                <img src="<?php echo $firstImage['sizes']['medium']; ?>" alt="<?php echo $firstImage['alt']; ?>" class="estate-thumbnail-img">
                                                            <?php
                                                            } else{ ?>
                                                                <img src="<?php echo get_template_directory_uri(); ?>/assets/img/pindi_pakkumise_placeholder.jpg" alt="No image" class="estate-thumbnail-img">
                                                            <?php
                                                            }
                                                        ?>
                                                    </div>
                                                </a>
                                            </div>
                                            <div class="estate-details">
                                                <a <?php echo ($link ? 'target="_BLANK"' : ''); ?> href="<?php echo ($link ? $link : get_the_permalink($single_post->ID)); ?>" class="estate-details-name"><?php echo $single_post->post_title; ?></a>
                                                <div class="estate-details-info">
                                                    <?php
                                                        $finishedBy = get_field('finished_by', $single_post->ID);
                                                        if ($finishedBy) { ?>
                                                            <span class="estate-details-info-label"><?php pll_e('Finished by'); ?>: </span><span class="estate-details-info-data"><?php echo $finishedBy; ?></span>
                                                            <br>
                                                        <?php
                                                        }
                                                        echo getContentByIdNoTags($single_post->ID);
                                                    ?>
                                                </div>
                                                <?php
                                                    if ($link && $linkText) {
                                                        echo '<a target="_BLANK" href="' . $link . '" class="estate-details-link">' . $linkText . ' </a>';
                                                    }
                                                ?>
                                            </div>
                                        </div>
                                    </div>

                                <?php
                                }
                            }
                        ?>

                    </div>

                </div>
            </div>
        </div>
    </div>

    <script type="text/html" id="newDevs">
        {{#newDevs}}
            <div class="developments-block">
                <div class="estate">
                    <div class="estate-thumbnail">
                        <a {{#linkExternal}}target="_BLANK"{{/linkExternal}} href="{{#linkExternal}}{{linkExternal}}{{/linkExternal}}{{^linkExternal}}{{link}}{{/linkExternal}}" class="estate-thumbnail-link">
                            <div class="offer-thumbnail-img-wrap">
                                {{#image}}
                                    <img src="{{image.sizes.large}}" alt="{{image.alt}}" class="estate-thumbnail-img">
                                {{/image}}
                                {{^image}}
                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/img/pindi_pakkumise_placeholder.jpg" alt="No image" class="estate-thumbnail-img">
                                {{/image}}
                            </div>
                        </a>
                    </div>
                    <div class="estate-details">
                        <a {{#linkExternal}}target="_BLANK"{{/linkExternal}} href="{{#linkExternal}}{{linkExternal}}{{/linkExternal}}{{^linkExternal}}{{link}}{{/linkExternal}}" class="estate-details-name">{{title}}</a>
                        <div class="estate-details-info">
                            {{#finishedBy}}
                                    <span class="estate-details-info-label"><?php pll_e('Finished by'); ?>: </span><span class="estate-details-info-data">{{finishedBy}}</span>
                                    <br>
                            {{/finishedBy}}
                            {{content}}
                        </div>
                        {{#link}}
                            {{#linkText}}
                                <a target="_BLANK" href="{{linkExternal}}" class="estate-details-link">{{linkText}}</a>
                            {{/linkText}}
                        {{/link}}
                    </div>
                </div>
            </div>
        {{/newDevs}}
    </script>

    <?php get_template_part('blocks/b_mob_social_icons'); ?>

    <?php get_template_part('blocks/b_footer'); ?>

    <?php get_template_part('blocks/b_javascripts'); ?>

</body>
</html>
