<?php
/* Template Name: Contact with gallery */

$contact = getContact([
    'contactsType' => 'field',
    'customFieldsName' => 'related_contact',
]);
$gallery = get_field('product_images');

?>
<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>


    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-default">
    <?php get_template_part('blocks/b_header'); ?>
    <div class="container">
        <div class="content-divider">
            <div class="row">
                <div class="col-sm-4 col-md-3 hidden-xs">
                    <?php get_template_part('blocks/b_sidebar_primary'); ?>
                </div>
                <div class="col-sm-8 col-md-9 print-col-12 padding-left">

                    <?php get_template_part('blocks/b_breadcrumbs'); ?>

                    <h1 class="primary-heading"><?php echo get_the_title(); ?></h1>

                    <div class="added-content-contact hidden-xs">
                        <?php if ($contact) :
                            renderContact($contact, [
                                'gaCategory' => 'Üksikleht',
                                'thumbnailClass' => 'halfed-contact-thumbnail',
                                'infoClass' => 'halfed-contact-info',
                                'singleButtonClass' => 'hidden',
                            ]);
                        endif ?>
                        <div class="">
                            <?php if ($gallery) :
                                foreach ($gallery as $image_url) : ?>
                                    <div style="margin-top: 15px;">
                                        <img src="<?= current($image_url)['url'] ?>" loading="lazy">
                                    </div>
                                <?php endforeach;
                            endif; ?>
                        </div>
                    </div>

                    <div class="user-added-content">
                        <?php
                            while ( have_posts() ) : the_post();
                            the_content();
                            endwhile;
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php get_template_part('blocks/b_mob_social_icons'); ?>

    <?php get_template_part('blocks/b_footer'); ?>

    <?php get_template_part('blocks/b_javascripts'); ?>

</body>
</html>
