<?php
/* Template Name: Form contact page */

$custom_fields = get_fields();
$url = "https://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

?>
<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-js" lang="et">
<!--<![endif]-->

<head>
    <title><?php echo wp_title('', true, 'left'); ?></title>

    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-evaluation form-template-page type-<?= $custom_fields['form_type'] ?>">

    <?php get_template_part('blocks/b_header'); ?>

    <div class="container">
        <div class="content-divider">
            <div class="row">
                <div class="col-sm-4 col-md-3 hidden-xs">
                <?php if (strpos($url, 'teenused-erakliendile') == true || strpos($url, 'kinnisvarateenused-arikliendile') == true) {
                        get_template_part('blocks/b_sidebar_children_pages'); 
                        } else {
                         get_template_part('blocks/b_sidebar_primary');
                    }?>
                </div>
                <div class="col-sm-8 col-md-9 print-col-12 padding-left">
                    <?php get_template_part('blocks/b_breadcrumbs'); ?>

                    <div>
                        <?php if ($custom_fields["header_image"]) : ?>
                            <img src="<?= $custom_fields["header_image"]; ?>" alt="Bänneri pilt" loading="lazy" class="form-tmp-banner">
                        <?php endif ?>

                        <?php if ($custom_fields["header_mobile_image"]) : ?>
                            <img src="<?= $custom_fields["header_mobile_image"]; ?>" alt="Mobiili bänneri pilt" loading="lazy" class="form-tmp-mobile-banner">
                        <?php endif ?>
                    </div>

                    <h1 class="primary-heading"><?= $custom_fields["header_title"]; ?></h1>

                    <div class="user-added-content row main__content">
                        <div class="user-added-content__left">
                            <?= get_the_content(); ?>

                            <?php include 'form-page/form.php'; ?>
                        </div>
                        <div class="user-added-content__right">
                            <div class="js-gallery">
                                <?php if ($custom_fields["gallery"]) :
                                    foreach ($custom_fields["gallery"] as $image_url) : ?>
                                        <div>
                                            <img src="<?= $image_url ?>" loading="lazy">
                                        </div>
                                <?php endforeach;
                                endif; ?>
                            </div>
                        </div>
                    </div>

                    <?php if ($custom_fields["under_form_textarea"]) : ?>
                        <div class="user-added-content pricing__text">
                            <?php echo $custom_fields["under_form_textarea"] ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($custom_fields["important_links"]) : ?>
                        <div class="user-added-content links__container">
                            <h3><?= pll__('Olulised lingid') ?></h3>
                            <div>
                                <?php foreach ($custom_fields["important_links"] as $data) : ?>
                                    <a href="<?= $data['page']['url'] ?>" target="<?= $data['page']['target'] ?>">
                                        <?= $data['page']['title'] ?>
                                    </a>
                                <?php endforeach ?>
                            </div>
                        </div>
                    <?php endif ?>

                    <?php if ($custom_fields["emergency_phone"]) : ?>
                        <div class="user-added-content emergency__container">
                            <?= pll__('24h avariitelefon') ?>:
                            <?php
                            $nr = str_replace(' ', '', $custom_fields["emergency_phone"]);
                            printf("<a href='tel:{$nr}'>{$custom_fields['emergency_phone']}</a>")
                            ?>
                        </div>
                    <?php endif ?>

                    <?php if ($custom_fields['form_type'] == 'haldus' && $custom_fields["gallery"]) : ?>
                        <div class="js-mobile-gallery">
                            <?php foreach ($custom_fields["gallery"] as $image_url) : ?>
                                <div>
                                    <img src="<?= $image_url ?>" loading="lazy">
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($custom_fields["brokers"]) : ?>
                        <div class="user-added-content brokers__container">
                            <h3><?= pll__('Kliendid usaldavad meid') ?></h3>
                            <div class="js-broker-gallery">
                                <?php foreach ($custom_fields["brokers"] as $data) :
                                    $img_url = get_field('contact_thumbnail_image', $data["broker"]->ID);
                                    $img_url = $img_url ? $img_url['url'] : ''; ?>
                                    <a href="<?= $data['link']['url'] ?>" target="<?= $data['link']['target'] ?>">
                                        <img src="<?= $img_url ?>" alt="" loading="lazy">
                                        <p><?= $data['quote'] ?></p>
                                        <span><?= $data["name"] ?></span>
                                    </a>
                                <?php endforeach ?>
                            </div>
                        </div>
                    <?php endif ?>

                    <?php if ($custom_fields["icons"]) : ?>
                        <div class="user-added-content icons__container">
                            <?php foreach ($custom_fields["icons"] as $data) : ?>
                                <a href="<?= $data['link']['url'] ?>" target="<?= $data['link']['target'] ?>">
                                    <img src="<?= $data['image'] ?>" alt="" loading="lazy">
                                    <div><?= $data['link']['title'] ?></div>
                                </a>
                            <?php endforeach ?>
                        </div>
                    <?php endif ?>



                </div>
            </div>
        </div>

        <div class="user-added-content footer__text">
            <?= $custom_fields["footer_textarea"] ?>
        </div>
    </div>

    <?php
    get_template_part('blocks/b_mob_social_icons');
    get_template_part('blocks/b_footer');
    get_template_part('blocks/b_javascripts');
    ?>

</body>

</html>