<?php
/*
	Part of 'Form contact page' template
*/

if ($custom_fields['gravityform_shortcode'] ?? '') {
	echo do_shortcode($custom_fields['gravityform_shortcode']);
	return;
}

$type = $custom_fields['form_type'] ?? '';
switch ($type) {
	case 'hindamine':
		$heading = pll__('Hindamine tellige siit');
		$confirm = pll__('Tellige hindamine');
		break;

	case 'vahendamine':
		$heading = pll__('Sobiva maakleri leidmiseks võtke meiega kohe ühendust!');
		$confirm = pll__('Võtke ühendust');
		break;

	case 'haldus':
		$heading = pll__('Kirjutage meile, teeme Teile hea pakkumise!');
		$confirm = pll__('Küsige pakkumist');
		break;

	default:
		return;
}

$phone_html = '';
if ($custom_fields['form_phone_numbers']) {
	$nrs = [];
	foreach (explode(';', $custom_fields['form_phone_numbers']) as $number) {
		$trimed = str_replace(' ', '', $number);
		$nrs[] = "<a href='tel:{$trimed}'>{$number}</a>";
	}

	$phone_html = sprintf('%s: %s', pll__('Või helistage meile'), implode('; &nbsp;', $nrs));
}

$response = form_page_template_processing($type, $custom_fields['form_email_recipient']);

?>
<h3><?= $heading ?></h3>
<form id="page-contact-form" class="js-form-analytics" action="#page-contact-form" method="post" data-ga-identifier="<?= get_the_permalink(); ?>" data-ga-category="<?= $type ?>">

	<?php if ($type == 'hindamine') : ?>
		<div>
			<select name="liik" id="liik" required="">
				<option value=""><?= pll__('Vara liik') ?></option>
				<option value="korter"><?= pll__('Korter') ?></option>
				<option value="maja"><?= pll__('Maja') ?></option>
				<option value="maatükk"><?= pll__('Maatükk') ?></option>
				<option value="äripind"><?= pll__('Äripind') ?></option>
			</select>
		</div>

		<div>
			<input type="text" name="address" id="address" maxlength="100" required="" placeholder="<?= pll__('Vara aadress') ?>">
		</div>

		<div>
			<select name="maakond" class="" required="" aria-required="true">
				<option value=""><?= pll__('Maakond'); ?></option>
				<option value="Harju maakond"><?= pll__('Harju maakond'); ?></option>
				<option value="Hiiu maakond"><?= pll__('Hiiu maakond'); ?></option>
				<option value="Ida-Viru"><?= pll__('Ida-Viru'); ?></option>
				<option value="Jõgeva maakond"><?= pll__('Jõgeva maakond'); ?></option>
				<option value="Järva maakond"><?= pll__('Järva maakond'); ?></option>
				<option value="Lääne maakond"><?= pll__('Lääne maakond'); ?></option>
				<option value="Lääne-Viru"><?= pll__('Lääne-Viru'); ?></option>
				<option value="Põlva maakond"><?= pll__('Põlva maakond'); ?></option>
				<option value="Pärnu maakond"><?= pll__('Pärnu maakond'); ?></option>
				<option value="Rapla maakond"><?= pll__('Rapla maakond'); ?></option>
				<option value="Saare maakond"><?= pll__('Saare maakond'); ?></option>
				<option value="Tartu maakond"><?= pll__('Tartu maakond'); ?></option>
				<option value="Valga maakond"><?= pll__('Valga maakond'); ?></option>
				<option value="Viljandi maakond"><?= pll__('Viljandi maakond'); ?></option>
				<option value="Võru maakond"><?= pll__('Võru maakond'); ?></option>
			</select>
		</div>
		<div>
			<input type="text" name="nimi" id="nimi" maxlength="100" required="" placeholder="<?= pll__('Nimi') ?>">
		</div>
	<?php endif ?>

	<div>
		<input type="email" name="email" id="email" required="" placeholder="<?= pll__('E-mail') ?>">
	</div>
	<div>
		<input type="text" name="telefon" id="telefon" maxlength="100" required="" pattern="[+]?[0-9 ]+" title="+372 56 123 456" placeholder="<?= pll__('Telefon') ?>">
	</div>
	<div>
		<textarea style="resize: none;" name="lisainfo" id="lisainfo" cols="30" rows="5" maxlength="350" placeholder="<?= pll__('Lisainfo') ?>"></textarea>
	</div>

	<div>

		<?php if ($custom_fields['form_link']) : ?>
			<div class="form__buttons__container">
				<button type="submit"><?= $confirm ?></button>
				<a target="_blank" class="form-link" href="<?= $custom_fields['form_link'] ?>">
					<?= pll__('Meie hinnakiri') ?>
				</a>
			</div>
			<div><?= $phone_html ?></div>
		<?php else : ?>
			<div class="form__buttons__container">
				<button type="submit"><?= $confirm ?></button>
				<div class="phone-margin-minus"><?= $phone_html ?></div>
			</div>
		<?php endif ?>


	</div>

	<?php if ($response) : ?>
		<div>
			<?= $response ?>
		</div>
	<?php endif ?>

</form>
