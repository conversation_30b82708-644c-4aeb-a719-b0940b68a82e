<?php /* Template Name: Investor page template */ ?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>


    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-default">
    <?php get_template_part('blocks/b_header'); ?>
    <div class="container">
        <div class="content-divider">
            <div class="row">
                <div class="col-sm-4 col-md-3 hidden-xs">
                    <?php
                    $postIdByTemplateArgs = [
                        'post_type' => 'page',
                        'fields' => 'ids',
                        'nopaging' => true,
                        'meta_key' => '_wp_page_template',
                        'meta_value' => 'page-templates/business_client.php'
                    ];
                    $postIdByTemplate = array_values(get_posts($postIdByTemplateArgs))[0];

                    global $wp_query;
                    $save_query = $wp_query->queried_object_id;
                    $wp_query->queried_object_id = $postIdByTemplate;

                    get_template_part('blocks/b_sidebar_investor_page');

                    $wp_query->queried_object_id = $save_query;
                    ?>
                </div>
                <div class="col-sm-8 col-md-9 print-col-12 padding-left">

                    <?php get_template_part('blocks/b_breadcrumbs'); ?>

                    <?php
                        global $infoPageId;
                        $image = get_field('default_banner_image', pll_get_post($infoPageId));
                        $text = get_field('default_banner_text', pll_get_post($infoPageId));
                    ?>
                    <?php if ($image && $text): ?>
                        <div class="top-intro hidden-xs">
                            <div class="top-intro-thumbnail">
                                <?php echo '<img src="' . $image["sizes"]["custom-banner-image"] . '" alt="' . $image["alt"] . '" class="top-intro-img">'; ?>
                            </div>
                            <h2 class="top-intro-heading">
                                <?php echo $text; ?>
                            </h2>
                        </div>
                    <?php endif ?>

                    <h1 class="primary-heading"><?php echo get_the_title(); ?></h1>
                    <div class="user-added-content">
                        <?php
                            while ( have_posts() ) : the_post();
                            the_content();
                            endwhile;
                        ?>
                    </div>
                    <?php
                    $fields = get_fields();
                    ?>
                    <div class="customboxes">
                        <div class="row">
                            <?php
                            if ($fields['left_value'] && $fields['left_label']) { ?>
                                <div class="col-sm-6">
                                    <div class="customboxes-content customboxes-content-left">
                                        <h2 class="customboxes-content-heading">
                                            <?php echo $fields['left_value']; ?>
                                        </h2>
                                        <div class="customboxes-content-info">
                                            <?php echo $fields['left_label']; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php
                            }

                            if ($fields['right_value'] && $fields['right_label']) { ?>
                                <div class="col-sm-6">
                                    <div class="customboxes-content customboxes-content-right">
                                        <h2 class="customboxes-content-heading">
                                            <?php echo $fields['right_value']; ?>
                                        </h2>
                                        <div class="customboxes-content-info">
                                            <?php echo $fields['right_label']; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php get_template_part('blocks/b_mob_social_icons'); ?>

    <?php get_template_part('blocks/b_footer'); ?>

    <?php get_template_part('blocks/b_javascripts'); ?>

</body>
</html>
