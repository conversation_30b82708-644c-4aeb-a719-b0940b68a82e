<?php /* Template Name: Contacts group */ ?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>


    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-contact-groups">

    <?php get_template_part('blocks/b_header'); ?>

    <div class="container">
        <div class="content-divider">
            <div class="row">
                <div class="col-sm-4 col-md-3 hidden-xs">

                    <?php get_template_part('blocks/b_sidebar_children_pages'); ?>

                </div>
                <div class="col-sm-8 col-md-9 print-col-12 padding-left">

                    <?php get_template_part('blocks/b_breadcrumbs'); ?>

                    <h1 class="primary-heading groups-primary-heading">
                        <?php echo the_title(); ?>
                    </h1>

                    <div class="user-added-content">
                        <?php
                            while ( have_posts() ) : the_post();
                                the_content();
                            endwhile;
                        ?>
                    </div>

                    <form class="clarify">
                        <?php
                            $argsTerms = array();
                            $taxonomy = 'active_location';
                            if (isset($_GET['filters'])) {
                                $query = $_GET['filters'];
                                if (isset($query['term']) && !empty($query['term'])) {
                                    $argsTerms = array(
                                        'tax_query' => array(
                                            array(
                                                'taxonomy' => $taxonomy,
                                                'field' => 'term_id',
                                                'terms' => $query['term'],
                                            ),
                                        ),
                                    );
                                }
                            }
                            $terms = get_terms($taxonomy);
                            $cat_id = get_field('group_select');
                        ?>
                        <input name="filters[term]" type="text" value="<?php echo (isset($query['term']) && !empty($query['term']) ? $query['term'] : ''); ?>" hidden>
                        <input name="filters[catID]" type="text" value="<?php echo ($cat_id ? $cat_id : ''); ?>" hidden>

                        <div class="clarify-dropdown dropdown">
                            <button class="btn dropdown-filter-btn dropdown-toggle products-filter-misc-sort dev-products-filter-misc-sort" type="button" id="dropdownMenu" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <?php
                                    if (isset($query['term']) && !empty($query['term'])) {
                                        $term = get_term_by('id', $query['term'], $taxonomy);
                                        pll_e($term->name . ' (filter office name)');
                                    } else{
                                        pll_e('All counties');
                                    }
                                ?>
                                <i class="dropdown-filter-btn-icon"></i>
                            </button>
                            <ul class="dropdown-menu clarify-dropdown-menu" aria-labelledby="dropdownMenu">
                                <?php
                                    echo '<li class="js-filter-dropdown-item clarify-dropdown-option" identifier="">' . pll__('All counties') . '</li>';
                                    foreach ($terms as $term) {
                                        echo '<li class="js-filter-dropdown-item clarify-dropdown-option" identifier="' . $term->term_id . '">' . pll__($term->name . ' (filter office name)') . '</li>';
                                    }
                                ?>
                            </ul>
                        </div>


                        <div class="clarify-search">
                            <input name="filters[searchQuery]" type="text" class="js-search-broker clarify-search-input" placeholder="otsi nime järgi...">
                            <span class="fa fa-search clarify-search-icon"></span>
                        </div>
                    </form>

                    <table class="js-contacts-results table table-contacts contacts">
                        <?php
                        ?>


                        <?php
                            $cat_id = get_field('group_select');

                            $argsDefault = array(
                                'post_type' => 'broker',
                                'post_status' => 'publish',
                                'posts_per_page' => '-1',
                                'tax_query' => array(
                                    'relation' => 'AND',
                                    array(
                                        'taxonomy' => 'team-group',
                                        'field' => 'term_id',
                                        'terms' => $cat_id,
                                    )
                                ),
                                'orderby' => 'title',
                                'order' => 'ASC'
                            );
                            $args = array_merge_recursive($argsDefault, $argsTerms);

                            if ( ! $argsTerms) {
                                $args['meta_query'] = [
                                    'relation' => 'OR',
                                    [
                                        'key' => 'show_on_local_grupp',
                                        'compare' => 'NOT EXISTS'
                                    ],
                                    [
                                        'key' => 'show_on_local_grupp',
                                        'value' => '0',
                                        'compare' => '='
                                    ]
                                ];
                            }

                            $posts_array = query_posts($args);
                            wp_reset_query();

                            if ($posts_array) {
                                foreach ($posts_array as $singlePost) {
                                    $orderVal = '';
                                    $terms = get_the_terms($singlePost->ID, 'display-order');
                                    if (isset($terms) && !empty($terms)) {
                                        $orderVal = $terms[0]->name;
                                    }
                                    if ($orderVal) {
                                        $singlePost->displayOrder = $orderVal;
                                    } else{
                                        $singlePost->displayOrder = '9999999';
                                    }
                                }
                                usort($posts_array, 'sortByVal');
                            }

                            echo '<tbody class="active">';
                            echo '<tr>';
                            echo '<td>';
                            echo '</td>';
                            echo '<td>';
                            echo '</td>';
                            echo '</tr>';
                            $postsRows = array_chunk($posts_array, 2);
                            if ($postsRows) {
                                foreach ($postsRows as $postsRow) {
                                    echo '<tr>';
                                        foreach ($postsRow as $item) {
                                            echo '<td>';
                                            echo '<div class="tabs-contact-people">';
                                            $args = array(
                                                'contactsType' => 'postID',
                                                'pageID' => $item->ID,
                                            );
                                            $contact = getContact($args);

                                            $renderArgs = array(
                                                'gaCategory' => 'Kontaktileht',
                                                'takeContact' => false,
                                                'thumbnailClass' => 'halfed-contact-thumbnail',
                                                'infoClass' => 'halfed-contact-info',
                                                'buttonsClass' => 'hidden',
                                            );
                                            renderContact($contact, $renderArgs);

                                            echo '</div>';
                                            echo '</td>';
                                        }
                                    echo '</tr>';
                                }
                            } else{
                                echo '<tr><td>' . pll__('No results found!') . '</td></tr>';
                            }
                            echo '</tbody>';
                        ?>
                    </table>

                </div>
            </div>
        </div>
    </div>

    <script type="text/html" id="brokers">
        <tbody class="active">
            <tr class="hidden-xs">
                <td>
                </td>
                <td>
                </td>
            </tr>
            {{#brokers}}
                <tr>
                    {{#contacts}}
                        <td>
                            <div class="tabs-contact-people">
                                <div class="contact-wrap">
                                    <div class="contact">
                                        <div class="contact-thumbnail halfed-contact-thumbnail">
                                            {{#thumbnail}}
                                                <img src="{{thumbnail.sizes.medium}}" alt="{{thumbnail.alt}}" class="contact-thumbnail-img">
                                            {{/thumbnail}}
                                            {{^thumbnail}}
                                                <img src="<?php echo get_template_directory_uri(); ?>/assets/img/no_pic_placeholder.png" alt="No image available" class="contact-thumbnail-img">
                                            {{/thumbnail}}
                                        </div>
                                        <div class="contact-info halfed-contact-info">
                                            {{#name}}
                                                <div class="contact-info-name"><a class="contact-info-link" href="{{link}}">{{name}}</a></div>
                                            {{/name}}

                                            {{#occupation}}
                                                <div class="contact-info-title">{{occupation}}</div>
                                            {{/occupation}}
                                            {{#languagesFixed.length}}
                                                <div class="contact-languages">
                                                    <div class="flags">
                                                        <ul class="flags-list">
                                                            {{#languagesFixed}}
                                                                <li class="flag-item flag-{{lang}}"></li>
                                                            {{/languagesFixed}}
                                                        </ul>
                                                    </div>
                                                </div>
                                            {{/languagesFixed.length}}
                                            <ul class="contact-details">
                                                {{#phone}}
                                                    <li class="contact-details-item">
                                                        <a class="contact-details-item-link" href="tel:{{phone}}">{{phone}}</a>
                                                    </li>
                                                {{/phone}}
                                                {{#mobile}}
                                                    <li class="contact-details-item">
                                                        <a class="contact-details-item-link" href="tel:{{mobile}}">{{mobile}}</a>
                                                    </li>
                                                {{/mobile}}
                                                {{#email}}
                                                    <li class="contact-details-item">
                                                        <a class="contact-details-item-link" href="mailto:{{email}}">{{email}}</a>
                                                    </li>
                                                {{/email}}
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </td>
                    {{/contacts}}
                </tr>
            {{/brokers}}
        </tbody>
    </script>

    <?php get_template_part('blocks/b_mob_social_icons'); ?>

    <?php get_template_part('blocks/b_footer'); ?>

    <?php get_template_part('blocks/b_javascripts'); ?>


</body>
</html>
