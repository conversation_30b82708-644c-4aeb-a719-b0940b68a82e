<?php /* Template Name: Regional contact template */ ?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>


    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-regional-contact">

    <?php get_template_part('blocks/b_header'); ?>

    <div class="container">
        <div class="content-divider">
            <div class="row">
                <div class="col-sm-4 col-md-3 hidden-xs">

                    <?php get_template_part('blocks/b_sidebar_children_pages'); ?>

                </div>

                <div class="col-sm-8 col-md-9 print-col-12 padding-left">

                    <?php get_template_part('blocks/b_breadcrumbs'); ?>

                    <div class="regional-contact">

                        <div class="regional-contact-general">
                            <div class="company">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <div class="company-contacts">
                                            <div class="firm-contact">
                                                <h1 class="firm-contact-heading"><?php echo get_the_title(); ?></h1>
                                                <?php
                                                    $contactInfo = get_fields();
                                                    if ($contactInfo['contact_row']) {
                                                        foreach ($contactInfo['contact_row'] as $contactRow) {
                                                            echo '<div class="firm-contact-block ' . ($contactRow['create_space_after_line'] ? 'margin-bottom' : '') . '">';
                                                            if ($contactRow['label'] && $contactRow['value']) {
                                                                echo '<div class="firm-contact-label">' . $contactRow['label'] . '</div>';

                                                                echo '<div class="firm-contact-item">';
                                                                if ($contactRow['field_type'] == 'phone') {
                                                                    $phone = preg_replace('/[^\+0-9]+/', '', $contactRow['value']); // remove everything exept numbers and +
                                                                    echo '<a href="tel:' . $phone . '" class="firm-contact-link">' . $contactRow['value'] . '</a>';
                                                                } elseif ($contactRow['field_type'] == 'email') {
                                                                    echo '<a href="mailto:' . $contactRow['value'] . '" class="firm-contact-link">' . $contactRow['value'] . '</a>';
                                                                } else{
                                                                    echo $contactRow['value'];
                                                                }
                                                                echo '</div>';
                                                            }
                                                            echo '</div>';
                                                        }
                                                    }
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="company-person hidden-xs">
                                            <?php
                                                $args = array(
                                                    'title' => get_field('daily_contact_person_title'),
                                                    'contactsType' => 'field',
                                                    'customFieldsName' => 'daily_contact_person',
                                                    'pageID' => get_the_ID(),
                                                );

                                                $contact = getContact($args);
                                                $renderArgs = array(
                                                    'gaCategory' => 'Piirkonnaleht',
                                                    'thumbnailClass' => 'halfed-contact-thumbnail',
                                                    'infoClass' => 'halfed-contact-info',
                                                    'singleButtonClass' => 'hidden',
                                                );
                                                renderContact($contact, $renderArgs);
                                            ?>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="user-added-content">
                            <?php
                                while ( have_posts() ) : the_post();
                                    the_content();
                                endwhile;
                            ?>
                        </div>

                        <?php
                        $location = get_field('google_map');
                        if($location) { ?>
                            <div class="regional-contact-map">
                                <div class="locale text-center">
                                    <div class="locale-map">
                                        <div class="map-canvas acf-map">
                                            <div class="marker" data-lat="<?php echo $location['lat']; ?>" data-lng="<?php echo $location['lng']; ?>"></div>
                                        </div>
                                    </div>
                                    <a href="http://maps.google.com/maps?q=<?php echo $location['lat'] . ',' . $location['lng']; ?>" target="_BLANK" class="locale-button"><?php pll_e('View larger map'); ?></a>
                                </div>
                            </div>
                            <div class="regional-contact-address">
                                <?php
                                    echo pll__('We are located at') . ': ' . $location['address'];
                                ?>
                            </div>
                        <?php } ?>

                        <div class="regional-contact-person visible-xs">
                            <?php
                                $args = array(
                                    'title' => get_field('daily_contact_person_title'),
                                    'contactsType' => 'field',
                                    'customFieldsName' => 'daily_contact_person',
                                    'pageID' => get_the_ID(),
                                );
                                $contact = getContact($args);

                                $renderArgs = array(
                                    'gaCategory' => 'Piirkonnaleht',
                                    'thumbnailClass' => 'halfed-contact-thumbnail',
                                    'infoClass' => 'halfed-contact-info',
                                    'buttonsClass' => 'hidden',
                                );
                                renderContact($contact, $renderArgs);
                            ?>
                        </div>

                        <div class="regional-contact-tabs">
                            <?php
                            $locationTerms = array(
                                array(
                                    'term_id' => getTranslatedId('68'),
                                    'name' => pll__('Bureau'),
                                ),
                                array(
                                    'term_id' => getTranslatedId('66'),
                                    'name' => pll__('Brokers'),
                                ),
                                array(
                                    'term_id' => getTranslatedId('67'),
                                    'name' => pll__('Evaluators'),
                                ),
                                array(
                                    'term_id' => getTranslatedId('74'),
                                    'name' => pll__('Administrator'),
                                ),
                            );
                            ?>
                            <table class="table table-contacts contacts">
                                <thead>
                                    <th colspan="2" class="visible-sm print">
                                        <h2 class="contacts-heading"><?php the_field('page_contacts_block_title'); ?></h2>
                                        <?php
                                            if ($locationTerms) {
                                                echo '<ul class="visible-sm contacts-tabs-small contacts-tabs">';
                                                foreach ($locationTerms as $key => $locationTerm) {
                                                    $activeLocationID = get_field('area_indentifier');
                                                    $posts_array = query_posts(array(
                                                        'post_type' => 'broker',
                                                        'post_status' => 'publish',
                                                        'posts_per_page' => '1',
                                                        'tax_query' => array(
                                                            array(
                                                                'taxonomy' => 'active_location',
                                                                'field' => 'term_id',
                                                                'terms' => $activeLocationID,
                                                            ),
                                                            array(
                                                                'taxonomy' => 'team-group',
                                                                'field' => 'term_id',
                                                                'terms' => $locationTerm['term_id'],
                                                            )
                                                        ),
                                                    ));
                                                    if (!$posts_array) continue;

                                                    echo '<li class="contacts-tabs-item" link-identifier="' . $locationTerm['name'] . '">' . $locationTerm['name'] . '</li>';
                                                }
                                                echo '</ul>';
                                            }
                                        ?>
                                    </th>
                                    <th  class="hidden-sm">
                                        <h2 class="contacts-heading"><?php the_field('page_contacts_block_title'); ?></h2>
                                    </th>

                                    <?php
                                        if ($locationTerms) {
                                            echo '<th class="hidden-sm">';
                                            echo '<ul class="contacts-tabs">';
                                            foreach ($locationTerms as $key => $locationTerm) {
                                                $activeLocationID = get_field('area_indentifier');
                                                $posts_array = query_posts(array(
                                                    'post_type' => 'broker',
                                                    'post_status' => 'publish',
                                                    'posts_per_page' => '1',
                                                    'tax_query' => array(
                                                        array(
                                                            'taxonomy' => 'active_location',
                                                            'field' => 'term_id',
                                                            'terms' => $activeLocationID,
                                                        ),
                                                        array(
                                                            'taxonomy' => 'team-group',
                                                            'field' => 'term_id',
                                                            'terms' => $locationTerm['term_id'],
                                                        )
                                                    ),
                                                ));
                                                if (!$posts_array) continue;

                                                echo '<li class="contacts-tabs-item" link-identifier="' . $locationTerm['name'] . '">' . $locationTerm['name'] . '</li>';
                                            }
                                            echo '</ul>';
                                            echo '</th>';
                                        }
                                    ?>

                                </thead>
                                    <?php
                                        if ($locationTerms) {
                                            $i=0;
                                            $allContacts = array();
                                            foreach ($locationTerms as $locationTerm) {
                                                $posts_array = query_posts(array(
                                                    'post_type' => 'broker',
                                                    'post_status' => 'publish',
                                                    'posts_per_page' => '-1',
                                                    'tax_query' => array(
                                                        array(
                                                            'taxonomy' => 'active_location',
                                                            'field' => 'term_id',
                                                            'terms' => $activeLocationID,
                                                        ),
                                                        array(
                                                            'taxonomy' => 'team-group',
                                                            'field' => 'term_id',
                                                            'terms' => $locationTerm['term_id'],
                                                        )
                                                    ),
                                                    'orderby' => 'date',
                                                    'order' => 'DESC'
                                                ));
                                                if (!$posts_array) continue;

                                                $postsArrayFiltered = array();

                                                if ($posts_array) {
                                                    foreach ($posts_array as $item) {
                                                        $orderVal = '';
                                                        $terms = get_the_terms($item->ID, 'display-order');
                                                        if (isset($terms) && !empty($terms)) {
                                                            $orderVal = $terms[0]->name;
                                                        }
                                                        if ($orderVal) {
                                                            $item->displayOrder = $orderVal;
                                                        } else{
                                                            $item->displayOrder = '9999999';
                                                        }

                                                        if(has_term( '', 'local-group', $item->ID)){
                                                            $localGroupTerms = wp_get_object_terms($item->ID, 'local-group');

                                                            $activeLocationTerms = wp_get_object_terms($item->ID, 'active_location');
                                                            foreach ($activeLocationTerms as $locTerm) {
                                                                if ($locTerm->term_id == $activeLocationID) {
                                                                    $activeLocationTermName = $locTerm->name;
                                                                }
                                                            }

                                                            $teamGroups = wp_get_object_terms($item->ID, 'team-group');
                                                            foreach ($teamGroups as $teamGroup) {
                                                                if ($teamGroup->term_id == $locationTerm['term_id']) {
                                                                    $teamGroupTermName = $teamGroup->name;
                                                                }
                                                            }

                                                            if ($activeLocationTermName && $teamGroupTermName) {
                                                                $curTermName = $activeLocationTermName .  ' ' . $teamGroupTermName;
                                                                foreach ($localGroupTerms as $localGroupTerm) {
                                                                    if (strtolower($curTermName) == strtolower($localGroupTerm->name)) {
                                                                        array_push($postsArrayFiltered, $item);
                                                                    }
                                                                }
                                                            }
                                                        } else{
                                                            array_push($postsArrayFiltered, $item);
                                                        }
                                                    }
                                                }
                                                usort($postsArrayFiltered, 'sortByVal');

                                                echo '<tbody content-indentifier=' . $locationTerm['name'] . '>';
                                                echo '<tr class="hidden-xs">';
                                                echo '<td>';
                                                echo '</td>';
                                                echo '<td>';
                                                echo '</td>';
                                                echo '</tr>';
                                                array_push($allContacts, $postsArrayFiltered);
                                                $postsRows = array_chunk($postsArrayFiltered, 2);
                                                if ($postsRows) {
                                                    foreach ($postsRows as $postsRow) {
                                                        echo '<tr>';
                                                            foreach ($postsRow as $item) {
                                                                echo '<td>';
                                                                echo '<div class="tabs-contact-people">';
                                                                $args = array(
                                                                    'contactsType' => 'postID',
                                                                    'pageID' => $item->ID,
                                                                );
                                                                $contact = getContact($args);
                                                                $renderArgs = array(
                                                                    'gaCategory' => 'Piirkonnaleht',
                                                                    'thumbnailClass' => 'halfed-contact-thumbnail',
                                                                    'infoClass' => 'halfed-contact-info',
                                                                    'buttonsClass' => 'hidden',
                                                                );
                                                                renderContact($contact, $renderArgs);

                                                                echo '</div>';
                                                                echo '</td>';
                                                            }
                                                        echo '</tr>';
                                                    }
                                                } else{
                                                    echo '<tr><td>' . pll__('No results found!') . '</td></tr>';
                                                }
                                                echo '</tbody>';
                                                $i++;
                                            }

                                            // display all contacts
                                            echo '<tbody class="active" content-indentifier="all">';
                                            echo '<tr class="hidden-xs">';
                                            echo '<td>';
                                            echo '</td>';
                                            echo '<td>';
                                            echo '</td>';
                                            echo '</tr>';
                                                $brokers = array();
                                                $existingItems = array();
                                                foreach ($allContacts as $contactRow) {
                                                    foreach ($contactRow as $contact) {
                                                        if (in_array($contact->ID, $existingItems)) continue;

                                                        array_push($brokers, $contact);
                                                        array_push($existingItems, $contact->ID);
                                                    }
                                                }


                                                usort($brokers, 'sortByVal');

                                                $brokersChuncked = array_chunk($brokers, 2);
                                                if ($brokersChuncked) {
                                                    foreach ($brokersChuncked as $row) {
                                                        echo '<tr>';
                                                            foreach ($row as $item) {
                                                                echo '<td>';
                                                                echo '<div class="tabs-contact-people">';
                                                                $args = array(
                                                                    'contactsType' => 'postID',
                                                                    'pageID' => $item->ID,
                                                                );
                                                                $contact = getContact($args);
                                                                $renderArgs = array(
                                                                    'gaCategory' => 'Piirkonnaleht',
                                                                    'thumbnailClass' => 'halfed-contact-thumbnail',
                                                                    'infoClass' => 'halfed-contact-info',
                                                                    'buttonsClass' => 'hidden',
                                                                );
                                                                renderContact($contact, $renderArgs);

                                                                echo '</div>';
                                                                echo '</td>';
                                                            }
                                                        echo '</tr>';
                                                    }
                                                }
                                            echo '</tbody>';
                                        }
                                    ?>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <?php get_template_part('blocks/b_mob_social_icons'); ?>

    <?php get_template_part('blocks/b_footer'); ?>

    <?php get_template_part('blocks/b_javascripts'); ?>

</body>
</html>
