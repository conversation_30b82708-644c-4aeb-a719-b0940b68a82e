<?php /* Template Name: Two Blocks Bottom */ ?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>


    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-evaluation">

    <?php get_template_part('blocks/b_header'); ?>

    <div class="container">
        <div class="content-divider">
            <div class="row">
                <div class="col-sm-4 col-md-3 hidden-xs">
                    <?php get_template_part('blocks/b_sidebar_primary'); ?>
                </div>
                <div class="col-sm-8 col-md-9 print-col-12 padding-left">
                    <?php get_template_part('blocks/b_breadcrumbs'); ?>

                    <h1 class="primary-heading"><?php echo get_the_title(); ?></h1>
                    <div class="user-added-content">
                        <?php
                            while ( have_posts() ) : the_post();
                                the_content();
                            endwhile;
                        ?>
                    </div>

                    <?php
                        $blocks = get_fields();
                        if ($blocks):
                    ?>
                        <div class="services">
                            <div class="row">
                                <?php if ($blocks['left_block_title'] && $blocks['left_block_content']): ?>
                                    <div class="col-sm-6 print-col-sm-12 relative js-service-block">
                                        <?php
                                            if ($blocks['left_block_title']) {
                                                echo '<h3 class="services-main-heading">' . $blocks['left_block_title'] . '</h3>';
                                            }
                                        ?>

                                        <?php if ($blocks['left_block_content']): ?>
                                            <div class="user-added-content services-user-added-content js-services-user-added-content">
                                                <?php echo $blocks['left_block_content']; ?>
                                            </div>
                                        <?php endif ?>

                                        <div class="services-content-hider visible-xs js-services-content-hider">
                                            <div class="btn main-button btn-services-content-hider js-btn-services-content-show"><?php pll_e('Read more'); ?></div>
                                            <div class="btn main-button btn-services-content-hider js-btn-services-content-hide hidden"><?php pll_e('Hide'); ?></div>
                                        </div>

                                        <div class="services-contact hidden-xs">
                                            <?php
                                                $args = array(
                                                    'contactsType' => 'fromInfoPage',
                                                    'customFieldsName' => 'business_customer_contact',
                                                    'title' => get_field('business_customer_contact_block_title', pll_get_post($infoPageId)),

                                                );
                                                $contact = getContact($args);

                                                $renderArgs = array(
                                                    'gaCategory' => 'Teenus',
                                                    'thumbnailClass' => 'halfed-contact-thumbnail',
                                                    'infoClass' => 'halfed-contact-info',
                                                    'singleButtonClass' => 'hidden',
                                                );
                                                renderContact($contact, $renderArgs);
                                            ?>
                                        </div>
                                    </div>
                                <?php endif ?>
                                <?php if ($blocks['right_block_title'] && $blocks['right_block_content']): ?>
                                    <div class="col-sm-6 print-col-sm-12 relative js-service-block">
                                        <?php
                                            if ($blocks['right_block_title']) {
                                                echo '<h3 class="services-main-heading">' . $blocks['right_block_title'] . '</h3>';
                                            }
                                        ?>

                                        <?php if ($blocks['right_block_content']): ?>
                                            <div class="user-added-content services-user-added-content js-services-user-added-content">
                                                <?php echo $blocks['right_block_content']; ?>
                                            </div>
                                        <?php endif ?>

                                        <div class="services-content-hider visible-xs js-services-content-hider">
                                            <div class="btn main-button btn-services-content-hider js-btn-services-content-show"><?php pll_e('Read more'); ?></div>
                                            <div class="btn main-button btn-services-content-hider js-btn-services-content-hide hidden"><?php pll_e('Hide'); ?></div>
                                        </div>

                                        <div class="services-contact hidden-xs">
                                            <?php
                                                $args = array(
                                                    'contactsType' => 'fromInfoPage',
                                                    'customFieldsName' => 'private_customer_contact',
                                                    'title' => get_field('private_customer_contact_block_title', pll_get_post($infoPageId)),
                                                    'exludeRandom' => $contact['oldRand'],
                                                );
                                                $contact = getContact($args);
                                                $renderArgs = array(
                                                    'gaCategory' => 'Teenus',
                                                    'thumbnailClass' => 'halfed-contact-thumbnail',
                                                    'infoClass' => 'halfed-contact-info',
                                                    'singleButtonClass' => 'hidden',
                                                );
                                                renderContact($contact, $renderArgs);
                                            ?>
                                        </div>
                                    </div>
                                <?php endif ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>


    <?php get_template_part('blocks/b_mob_social_icons'); ?>

    <?php get_template_part('blocks/b_footer'); ?>

    <?php get_template_part('blocks/b_javascripts'); ?>

</body>
</html>
