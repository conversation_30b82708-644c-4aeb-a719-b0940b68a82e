<?php 
 /* Template Name: Vahendus RU */
?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>


    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-vahendus">
	<?php get_template_part('blocks/b_header'); ?>

	<div class="container container-narrow pb-50">
		<div class="row">
			<div class="col-12">
				<h1 class="primary-heading text-center mb-30"><?php echo get_the_title(); ?></h1>

				<div class="user-added-content pl-30 pr-30">
					<div class="green-box">
						<div class="row pt-20 pb-20">
							<div class="col-md-6"><?php echo get_field('content-top-left'); ?></div>
							<div class="col-md-6"><?php echo get_field('content-top-right'); ?></div>
						</div>
					</div>
				</div>

				<?php $brokers = get_field('broker-list'); ?>
				<?php if ($brokers): ?>
				<div class="row flex-row pt-30 pb-30">
					<?php foreach ($brokers as $broker): ?>
						<div class="col-12 col-xs-12 col-sm-6">
							<?php $contact = getContact([
								'contactsType' => 'postID',
								'pageID' => $broker['contact']->ID
							]); ?>
							<?php include( locate_template( 'blocks/broker-contact.php', false, false ) ); ?>
						</div>
					<?php endforeach; ?>
				</div>
				<?php endif; ?>

				<div class="user-added-content">
					<?php echo get_field('content-bottom'); ?>
				</div>
			</div>
		</div>
	</div>

	<?php get_template_part('blocks/b_mob_social_icons'); ?>

	<?php get_template_part('blocks/b_footer'); ?>

	<?php get_template_part('blocks/b_javascripts'); ?>
</body>

</html>