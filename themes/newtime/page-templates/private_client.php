<?php /* Template Name: Private customer template */ ?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>


    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-buisness-client">

    <?php get_template_part('blocks/b_header'); ?>

    <div class="container">
        <div class="content-divider">
            <div class="row">
                <div class="col-sm-4 col-md-3 hidden-xs">

                    <?php get_template_part('blocks/b_sidebar_children_pages'); ?>

                </div>
                <div class="col-sm-8 col-md-9 print-col-12 padding-left">
                    <?php get_template_part('blocks/b_breadcrumbs'); ?>

                    <?php
                        global $infoPageId;
                        $image = get_field('private_banner_image', pll_get_post($infoPageId));
                        $text = get_field('private_banner_text', pll_get_post($infoPageId));
                    ?>
                    <?php if ($image && $text): ?>
                        <div class="top-intro hidden-xs">
                            <div class="top-intro-thumbnail">
                                <?php echo '<img src="' . $image["sizes"]["custom-banner-image"] . '" alt="' . $image["alt"] . '" class="top-intro-img">'; ?>
                            </div>
                            <h2 class="top-intro-heading">
                                <?php echo $text; ?>
                            </h2>
                        </div>
                    <?php endif ?>

                    <h1 class="primary-heading"><?php echo get_the_title(); ?></h1>
                    <div class="added-content-contact hidden-xs">
                        <?php
                            $args = array(
                                'contactsType' => 'fromInfoPage',
                                'customFieldsName' => 'private_customer_contact',
                                'title' => get_field('private_customer_contact_block_title', pll_get_post($infoPageId)),
                            );
                            $contact = getContact($args);
                            $renderArgs = array(
                                'gaCategory' => 'Teenus',
                                'thumbnailClass' => 'halfed-contact-thumbnail',
                                'infoClass' => 'halfed-contact-info',
                                'singleButtonClass' => 'hidden',
                            );
                            renderContact($contact, $renderArgs);
                        ?>
                    </div>

                    <div class="user-added-content">
                        <?php
                            while ( have_posts() ) : the_post();
                                the_content();
                            endwhile;
                        ?>
                    </div>

                    <div class="added-content-contact visible-xs">
                        <?php
                            $args = array(
                                'contactsType' => 'fromInfoPage',
                                'customFieldsName' => 'private_customer_contact',
                                'title' => get_field('private_customer_contact_block_title', pll_get_post($infoPageId)),
                            );
                            $contact = getContact($args);
                            $renderArgs = array(
                                'gaCategory' => 'Teenus',
                            );
                            renderContact($contact, $renderArgs);
                        ?>
                    </div>
                    <?php
                    $selectedProjects = get_field('private_customer_projects', pll_get_post($infoPageId));
                    $args = array(
                        'items' => $selectedProjects,
                        'limit' => 3,
                    );
                    $projects = getProject($args);
                    if ($projects) { ?>
                        <div class="featured-products">
                            <div class="featured-products-head">
                                <div class="devprojects">
                                    <h2 class="devprojects-heading secondary-heading">
                                        <?php pll_e('Pindi real estate development projects'); ?>
                                    </h2>
                                    <?php
                                        $devsPageTemplateArgs = [
                                            'post_type' => 'page',
                                            'fields' => 'ids',
                                            'nopaging' => true,
                                            'meta_key' => '_wp_page_template',
                                            'meta_value' => 'page-templates/new_developments.php'
                                        ];
                                        $devsPageTemplateID = array_values(get_posts($devsPageTemplateArgs))[0];

                                        $filterArgs = array(
                                            'filters' => array(
                                                'term' => 'objectTypeMeta|living',
                                            ),
                                        );
                                    ?>
                                    <a class="devprojects-link hidden-xs" href="<?php echo get_the_permalink($devsPageTemplateID) . '?' . http_build_query($filterArgs) ; ?>"><?php pll_e('See all development projects'); ?></a>
                                </div>
                            </div>
                            <div class="featured-products-content">
                                <div class="row hidden-xs">
                                    <?php
                                    foreach ($projects as $project) {
                                        $link = get_field('new_development_link', $project['ID']);
                                        ?>
                                        <div class="col-xs-4">
                                            <div class="project">
                                                <a <?php echo ($link ? 'target="_BLANK"' : ''); ?> href="<?php echo ($link ? $link : get_the_permalink($project['ID'])); ?>" class="project-thumbnail">
                                                    <div class="project-thumbnail-abs">
                                                        <div class="offer-thumbnail-img-wrap">
                                                            <?php
                                                            if ($project['images']) {
                                                                echo '<img class="project-thumbnail-img" src="' . $project['images'][0]['image']['sizes']['large'] . '" alt="' . $project['images'][0]['image']['alt'] . '">';
                                                            }
                                                            if ($project['transactionType']) { ?>
                                                                <div class="project-thumbnail-info">
                                                                    <?php echo $project['transactionType'] ?>
                                                                </div>
                                                            <?php } ?>
                                                        </div>
                                                    </div>
                                                </a>
                                                <a <?php echo ($link ? 'target="_BLANK"' : ''); ?> href="<?php echo ($link ? $link : get_the_permalink($project['ID'])); ?>" class="project-name-link">
                                                    <?php echo $project['name']; ?>
                                                </a>
                                            </div>
                                        </div>
                                    <?php } ?>
                                </div>
                                <div class="mob-slider js-mob-slider visible-xs">
                                    <?php
                                    foreach ($projects as $project) {
                                        $link = get_field('new_development_link', $project['ID']); ?>
                                        <a <?php echo ($link ? 'target="_BLANK"' : ''); ?> href="<?php echo ($link ? $link : get_the_permalink($project['ID'])); ?>" class="mob-slider-item-wrap">
                                            <?php
                                            if ($project['images']) {
                                                echo '<div class="mob-slider-item">';
                                                echo '<div class="mob-slider-img-wrap">';
                                                echo '<div class="offer-thumbnail-img-wrap">';
                                                echo '<img class="mob-slider-img" src="' . $project['images'][0]['image']['sizes']['large'] . '" alt="' . $project['images'][0]['image']['alt'] . '">';
                                                echo '</div>';
                                                echo '</div>';
                                                if ($project['transactionType']) { ?>
                                                    <div class="mob-slider-info project-thumbnail-info">
                                                        <?php echo $project['transactionType'] ?>
                                                    </div>
                                                <?php }
                                                echo '</div>';
                                             } ?>
                                            <div class="project-name">
                                                <?php echo $project['name']; ?>
                                            </div>
                                        </a>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>

    <?php get_template_part('blocks/b_mob_social_icons'); ?>
    <?php get_template_part('blocks/b_footer'); ?>

    <?php get_template_part('blocks/b_javascripts'); ?>

</body>
</html>
