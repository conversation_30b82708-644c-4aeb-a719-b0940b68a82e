<meta charset="utf-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<meta name="authoring-tool" content="Adobe_Animate_CC"/>
<meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0"/>

<!-- Icons & Colors -->
<!-- <meta property="og:image" content="/og-image.png"> -->
<link rel="apple-touch-icon" sizes="152x152" href="/apple-touch-icon-152x152.png"/>
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon-180x180.png"/>
<link rel="icon" type="image/png" href="/android-chrome-192x192.png" sizes="192x192"/>
<link rel="shortcut icon" type="image/png" href="<?php echo get_template_directory_uri(); ?>/assets/img/favicon.ico"/>
<meta name="msapplication-TileImage" content="/mstile-144x144.png"/>
<meta name="msapplication-TileColor" content="#000000"/>
<meta name="theme-color" content="#000000"/>
<meta name="google-site-verification" content="NdXfMqTnJp4zOaPFkOavRmSzIebYlRMJjwRw0kcVQsU"/>
<meta name="facebook-domain-verification" content="6r3uahcsv554r4kp353qgysrx1by1d" />
<?php
    if (pll_current_language() != 'et') {
        echo '<meta name="robots" content="noindex, nofollow">';
    }

    $excluded_redirect_page_ids = [609407, 651016, 673327, 673225, 673899, 737065, 762223, 763117,763119];
    if (!is_user_logged_in() && pll_current_language('slug') !== 'et' && !in_array(get_the_ID(), $excluded_redirect_page_ids)) {
        if (wp_redirect(pll_home_url('et'))) {
            exit();
        }
    }
?>

<!-- <script src="https://browser.plumbr.io/pa.js"
    data-plumbr='{
        "accountId":"91t6tl5b29jboksdfqq2krhaqt",
        "appName":"Pindi",
        "serverUrl":"https://bdr.plumbr.io"
    }'>
</script> -->

<!-- CSS -->
<link rel="stylesheet" type="text/css" href="<?php echo get_template_directory_uri(); ?>/vendor/bootstrap/css/bootstrap-custom.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo get_template_directory_uri(); ?>/vendor/dlmenu/css/dlmenu-1.2.1.custom.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo get_template_directory_uri(); ?>/vendor/polyfill-object-fit/css/polyfill.object-fit.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo get_template_directory_uri(); ?>/vendor/font-awesome/css/font-awesome-4.4.0-custom.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo get_template_directory_uri(); ?>/vendor/tablesaw/css/tablesaw.stackonly.css"/>
<link rel="stylesheet" type="text/css" href="<?php echo get_template_directory_uri(); ?>/vendor/slick/css/slick.css"/>



<link rel="stylesheet" type="text/css" href="<?php echo get_template_directory_uri(); ?>/assets/css/style.css?ver=<?php echo filemtime(get_template_directory() . '/assets/css/style.css'); ?>" />
<link rel="stylesheet" type="text/css" href="<?php echo get_template_directory_uri(); ?>/assets/css/print.css?ver=<?php echo filemtime(get_template_directory() . '/assets/css/print.css'); ?>"/>

<!--  JS -->
<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="<?php echo get_template_directory_uri(); ?>/vendor/modernizr/js/modernizr-3.3.1.custom.min.js"></script>
<!-- giosg tag -->
<!-- <script>
(function(w, t, f) {
  var s='script',o='_giosg',h='https://service.giosg.com',e,n;e=t.createElement(s);e.async=1;e.src=h+'/live/';
  w[o]=w[o]||function(){(w[o]._e=w[o]._e||[]).push(arguments)};w[o]._c=f;w[o]._h=h;n=t.getElementsByTagName(s)[0];n.parentNode.insertBefore(e,n);
})(window,document,4900);
</script> -->
<!-- giosg tag -->
<!--[if lt IE 9]>
    <script src="<?php echo get_template_directory_uri(); ?>/vendor/respond/js/respond-1.4.2.min.js"></script>
<![endif]-->

<!-- Hotjar Tracking Code for https://www.pindi.ee/ -->
<script>
    (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:2110488,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
</script>
<!-- End Hotjar Tracking Code -->

<!-- Google Tag Manager -->
<script>
window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('consent', 'default', {
    'ad_storage': 'denied',
    'ad_user_data': 'denied',
    'ad_personalization': 'denied',
    'analytics_storage': 'denied'
});
(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5ZPGRK6');</script>
<!-- End Google Tag Manager -->

<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5ZPGRK6"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

<div class="cookie-header cookie-header-hidden js-cookie-header">
    <form class="cookie-bar" name="cookie-bar-settings">
        <div class="cookie-header-content">
            Sellel veebilehel kasutatakse küpsiseid. Veebilehe kasutamist jätkates nõustute küpsiste kasutamisega.
        </div>
        <div class="cookie-header-buttons">
            <button class="main-button" type="submit">Nõustun</button>
            <button class="main-button js-modify-cookies">Halda sätteid</button>
            <a href="<?php echo get_privacy_policy_url(); ?>" class="main-button">Rohkem infot</a>
        </div>

        <div class="cookie-header-settings">
            <div class="cookie-bar__check-area">
              <div class="cookie-bar__check-group">
                <input type="checkbox" id="cookie-mandatory" class="cookie-checkbox" name="mandatory" checked="" disabled>
                <label for="cookie-mandatory"><?php _e('Vajalikud', 'mobire'); ?></label>
                <span class="tippy-toggle disabled" data-id="mandatory"></span>
                <div class="cookiebar-tooltip" role="tooltip"><strong>Lehe toimimiseks vajalikud küpsised</strong> aitavad meil muuta veebilehe paremini kasutatavaks, aktiveerides sellised põhifunktsioonid nagu lehel navigeerimine ja juurdepääs selle turvalistele osadele. Veebileht ei saa ilma nende küpsisteta korralikult toimida. Kuna neid küpsiseid on vaja teenuste turvaliseks pakkumiseks, ei ole külastajal võimalik neist keelduda.</div>
              </div>
              <div class="cookie-bar__check-group">
                <input type="checkbox" id="cookie-analytics" class="cookie-checkbox" name="analytics" checked="">
                <label for="cookie-analytics"><?php _e('Analüütika', 'mobire'); ?></label>
                <span class="tippy-toggle" data-id="analytics"></span>
                <div class="cookiebar-tooltip" role="tooltip">Statistikaküpsised aitavad meil mõista, kuidas konkreetne külastaja veebilehte kasutab. Nii näeme, kui palju inimesi kindlal ajavahemikul lehte külastab, kuidas veebilehtedel liigutakse ja millele klikitakse.</div>
              </div>
              <div class="cookie-bar__check-group">
                <input type="checkbox" id="cookie-preferences" class="cookie-checkbox" name="preferences" checked="">
                <label for="cookie-preferences"><?php _e('Eelistused', 'mobire'); ?></label>
                <span class="tippy-toggle" data-id="preferences"></span>
                <div class="cookiebar-tooltip" role="tooltip">Funktsionaalsed küpsised võimaldavad meil salvestada teavet, mis muudavad seda, kuidas ja mida veebilehel kuvatakse ning kuidas see toimib.</div>
              </div>
              <div class="cookie-bar__check-group">
                <input type="checkbox" id="cookie-marketing" class="cookie-checkbox" name="marketing" checked="">
                <label for="cookie-marketing"><?php _e('Turundus', 'mobire'); ?></label>
                <span class="tippy-toggle" data-id="marketing"></span>
                <div class="cookiebar-tooltip" role="tooltip">Reklaamiküpsiste abil jälgime, milliseid veebilehti külastajad kasutavad. Nende küpsiste eesmärk on kuvada sulle internetis asjakohaseid ja huvipakkuvaid reklaame.</div>
              </div>
            </div>
            <div class="justify-content-end align-items-center agree-button__area">
              <button id="agreeCookies" class="main-button" type="submit"><?php _e('Nõustun', 'mobire'); ?></button>
            </div>
        </div>
    </form>
</div>

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-4YLDWKDF6J"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-4YLDWKDF6J',{
  'send_pageview': true
});
</script>

<script type="text/javascript">
    (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "rbwrqfew22");
</script>
<?php wp_head();
