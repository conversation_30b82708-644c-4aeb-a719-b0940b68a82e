<header class="header">
	<div class="container">
		<div class="row relative">
			<div class="col-sm-8 col-sm-push-4 hidden-xs">
				<div class="nav">
					<div class="nav-misc">
						<?php
							$languages = pll_the_languages(array('raw' => 1));
							if (isset($languages)) { 
							?>
								<!-- <ul class="lang">
								<?php
									foreach ($languages as $lang) {
										echo '<li class="lang-item ' . (($lang['current_lang'] == 1) ? 'lang-item-current' : '') .'"><a class="lang-link" href="'. $lang['url'] .'">' . NnaApi::getInstance()->getLanguageCode($lang['slug']) . '</a></li>';
									}
								?>
								</ul> -->
						<?php } ?>
						<?php get_search_form(); ?>
					</div>
					<div class="nav-main">
						<?php 
							$menuHeaderArgs = array(
								'theme_location'  => 'primary',
								'container'       => 'div',
								'container_class' => 'menu',
								'menu_class'      => 'menu-list',
								'depth'           => 1,
								'walker'          => new customWalkerPrimaryMenu(),
							);
							wp_nav_menu($menuHeaderArgs); 
						?>
					</div>
				</div>
			</div>
			<div class="col-sm-4 col-sm-pull-8 print-col-12 txt-center-xs logo-col print-txt-center">
				<div class="logo">
					<a class="logo-link" href="<?php echo get_home_url(); ?>">
						<img class="logo-img" src="<?php echo get_template_directory_uri() ?>/assets/img/pindi_kinnisvara_logo.svg" alt="Pindi Kinnisvara">
					</a>
				</div>
				<!-- Mobile menu button -->
				<div class="dl-button dl-button-top js-dl-button visible-xs" data-dl-show-button-text="yes">
					<span class="dl-burger-icon"></span>
					<div class="dl-button-top-text"><?php pll_e('Menu') ?></div>
				</div>
			</div>
		</div>
	</div>
	<div class="mob-menu visible-xs">

		<div class="mob-menu visible-xs">
			<!-- Mobile dropdown menu -->
			<div id="dl-menu" class="dl-menuwrapper visible-xs" data-dl-back-text="<?php pll_e('Back') ?>" data-dl-trigger=".js-dl-button">
				<?php
					$menuArgs = array(
						'theme_location'  => 'mobile',
						'container'       => '',
						'menu_class'      => 'dl-menu dl-menu-top',
						'walker'          => new customWalkerMobMain(),
					);
					wp_nav_menu($menuArgs); 
				?>
			</div>

			<!-- Mobile static menu -->
			<div class="mob-static-menu">
				<?php
					$menuArgs = array(
						'theme_location'  => 'mobile-static',
						'container'       => '',
						'menu_class'      => 'secondary-menu-list static-mobile-menu-list',
						'depth'           => 1,
						'walker'          => new customWalkerFrontPageSecondaryMenu(),
					);
					wp_nav_menu($menuArgs);
				?>
			</div>
		</div>

	</div>
</header>
