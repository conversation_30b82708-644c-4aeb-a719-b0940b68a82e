<?php
/*
 * The template for front page/landing page
 */
?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-js" lang="et"><!--<![endif]-->

<head>
	<title><?php echo wp_title('', true, 'left'); ?></title>

	<?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-front">
	<header class="header">
		<div class="container">
			<div class="row relative">
				<div class="col-sm-8 col-sm-push-4 hidden-xs">
					<div class="nav">
						<?php
						global $infoPageId;
						$facebook = get_field('facebook_link', pll_get_post($infoPageId));
						$instagram = get_field('instagram_link', pll_get_post($infoPageId));
						$linkedin = get_field('linkedin_link', pll_get_post($infoPageId));
						$rss = get_field('rss_link', pll_get_post($infoPageId));
						?>
						<div class="nav-misc nav-misc-secondary">
							<?php
							$languages = pll_the_languages(array('raw' => 1));
							if (isset($languages)) {
							?>
								<!-- <ul class="lang lang-secondary">
									<?php
									foreach ($languages as $lang) {
										echo '<li class="lang-item ' . (($lang['current_lang'] == 1) ? 'lang-item-current' : '') . '"><a class="lang-link" href="' . $lang['url'] . '">' . NnaApi::getInstance()->getLanguageCode($lang['slug']) . '</a></li>';
									}
									?>
									</ul> -->
							<?php } ?>
							<ul class="social-icons right-social-icons">
								<li class="social-icons-item">
									<a class="social-icons-link social-icons-link-sm" href="<?php echo $linkedin; ?>" target="_BLANK">
										<i class="fa fa-linkedin social-icons-link-icon social-icons-link-icon-sm"></i>
									</a>
								</li>
								<li class="social-icons-item">
									<a class="social-icons-link social-icons-link-sm" href="<?php echo $rss; ?>">
										<i class="fa fa-rss social-icons-link-icon social-icons-link-icon-sm"></i>
									</a>
								</li>
								<li class="social-icons-item">
									<a class="social-icons-link social-icons-link-sm" href="<?php echo $facebook; ?>" target="_BLANK">
										<i class="fa fa-facebook social-icons-link-icon social-icons-link-icon-sm social-icons-link-fb"></i>
									</a>
								</li>
								<li class="social-icons-item">
									<a class="social-icons-link social-icons-link-sm" href="<?php echo $instagram; ?>" target="_BLANK">
										<i class="fa fa-instagram social-icons-link-icon social-icons-link-icon-sm"></i>
									</a>
								</li>
							</ul>

							<?php get_search_form(); ?>

						</div>

						<div class="nav-main">
							<?php
							$menuHeaderArgs = array(
								'theme_location'  => 'primary',
								'container'       => 'div',
								'container_class' => 'menu',
								'menu_class'      => 'menu-list',
								'depth'           => 1,
								'walker'          => new customWalkerPrimaryMenu(),
							);
							wp_nav_menu($menuHeaderArgs);
							?>
						</div>
					</div>
				</div>
				<div class="col-sm-4 col-sm-pull-8 print-col-12 logo-col txt-center-xs print-txt-center">
					<h1 class="logo">
						<a class="logo-link" href="<?php echo get_home_url(); ?>">
							<img class="logo-img" src="<?php echo get_template_directory_uri() ?>/assets/img/pindi_kinnisvara_logo.svg" alt="Pindi Kinnisvara">
						</a>
					</h1>
					<!-- Mobile menu button -->
					<div class="dl-button dl-button-top js-dl-button visible-xs" data-dl-show-button-text="yes">
						<span class="dl-burger-icon"></span>
						<div class="dl-button-top-text"><?php pll_e('Menu') ?></div>
					</div>
				</div>
			</div>
		</div>
		<div class="mob-menu visible-xs">

			<!-- Mobile dropdown menu -->
			<div id="dl-menu" class="dl-menuwrapper visible-xs" data-dl-back-text="<?php pll_e('Back') ?>" data-dl-trigger=".js-dl-button">
				<?php
				$menuArgs = array(
					'theme_location'  => 'mobile',
					'container'       => '',
					'menu_class'      => 'dl-menu dl-menu-top',
					'walker'          => new customWalkerMobMain(),
				);
				wp_nav_menu($menuArgs);
				?>
			</div>

			<!-- Mobile static menu -->
			<div class="mob-static-menu">
				<?php
				$menuArgs = array(
					'theme_location'  => 'mobile-static',
					'container'       => '',
					'menu_class'      => 'secondary-menu-list static-mobile-menu-list',
					'depth'           => 1,
					'walker'          => new customWalkerFrontPageSecondaryMenu(),
				);
				wp_nav_menu($menuArgs);
				?>
			</div>

		</div>
	</header>

	<div class="secondary-menu hidden-xs hidden-print">
		<div class="container">

			<?php
			$menuArgs = array(
				'theme_location'  => 'services',
				'container'       => '',
				'menu_class'      => 'secondary-menu-list',
				'depth'           => 1,
				'walker'          => new customWalkerFrontPageSecondaryMenu(),
			);
			wp_nav_menu($menuArgs);
			?>
		</div>
	</div>

	<?php
	$slides = get_field('slides');
	if ($slides) {
		$i = 0;
		echo '<div class="slider-wrap js-slider">';
		echo '<div>';
		foreach ($slides as $slide) {
			if ($slide['slider_image']) {
				echo '<a href="' . (isset($slide['slider_link']) && !empty($slide['slider_link']) ? $slide['slider_link'] : '#') . '" class="ga-banner slider js-slider-single ' . ($i == 0 ? 'active' : '') . '" style="--desktop: url(' . $slide['slider_image']['url'] . '); --mobile: url(' . $slide['slider_image_mobile']['url'] . ');" slide-identifier="' . $i . '">';
				if ($slide['slider_main_title'] || $slide['slider_secondary_title']) {
					echo '<div class="slider-bg">';
					echo '<div class="container">';
					echo '<div class="slider-slogan text-center">';
					if ($slide['slider_main_title']) {
						echo '<h2 class="ga-label slider-slogan-main">' . $slide['slider_main_title'] . '</h2>';
					}
					if ($slide['slider_secondary_title']) {
						echo '<h2 class="slider-slogan-secondary">' . $slide['slider_secondary_title'] . '</h2>';
					}
					echo '</div>';
					echo '</div>';
					echo '</div>';
				}
				echo '</a>';

				$i++;
			}
		}
		$i = 0;
		echo '</div>';
		echo '<ul class="js-slider-buttons slider-buttons hidden-xs">';
		foreach ($slides as $slide) {
			if ($slide['slider_image']) {
				echo '<li class="slider-buttons-btn js-slider-buttons-btn ' . ($i == 0 ? 'active' : '') . '" button-identifier="' . $i . '">';
				echo '</li>';
				$i++;
			}
		}
		echo '</ul>';
		echo '</div>';
	}
	?>

	<div class="container">
		<?php
		$selectors = array();

		$pipeline = array(
			array(
				'$group' => array(
					'_id' => '$Object.objectTypes.type',
				),
			),
		);
		$selectors['objectTypes.type'] = array(
			'label' => pll__('Object type'),
			'allPlaceholder' => pll__('All (Object type)'),
			'data' => NnaApi::getInstance()->aggregateForSelector($pipeline),
		);

		$pipeline = array(
			array(
				'$group' => array(
					'_id' => '$Object.transactions.transaction.type',
				),
			),
		);
		$selectors['transactions.transaction.type'] = array(
			'label' => pll__('Transaction type'),
			'allPlaceholder' => pll__('All (Transaction type)'),
			'data' => NnaApi::getInstance()->aggregateForSelector($pipeline),
		);

		$pipeline = array(
			array(
				'$group' => array(
					'_id' => array(
						'level1' => '$Object.level1',
						'level2' => '$Object.level2',
						'level3' => '$Object.level3',
					),
				)
			),
			array(
				'$group' => array(
					'_id' => array(
						'level1' => '$_id.level1',
						'level2' => '$_id.level2',
					),
					'level3' => array(
						'$push' => '$_id.level3',
					)
				),

			),
			array(
				'$group' => array(
					'_id' => '$_id.level1',
					'level2' => array(
						'$push' => array(
							'_id' => '$_id.level2',
							'level3' => '$level3',
						)
					)
				),
			)
		);
		$hierarchicalSelectors = NnaApi::getInstance()->aggregateObjects($pipeline);
		$levels = array(
			'level1' => array(
				'label' => pll__('County'),
				'allPlaceholder' => pll__('All (County)'),
				'items' => array(),
				'parent' => '',
				'hidden' => false,
			),
		);
		foreach ($hierarchicalSelectors as $level1) {
			$level1Hidden = false;
			$levels['level1']['items'][] = array(
				'name' => $level1->_id,
				'value' => $level1->_id,
				'parent' => '',
				'hidden' => $level1Hidden,
			);
		}

		$fields = get_fields();
		//Get id for offers page
		$pageTemplateArgs = [
			'post_type' => 'page',
			'fields' => 'ids',
			'nopaging' => true,
			'meta_key' => '_wp_page_template',
			'meta_value' => 'nna-objects.php'
		];
		$pageTemplate = array_values(get_posts($pageTemplateArgs))[0];
		?>


		<?php
		$slides = get_field('slides');
		if ($slides) {
			$i = 0;
			echo '<div class="mob-slider-buttons-wrap visible-xs">';
			echo '<ul class="js-slider-buttons mob-slider-buttons">';
			foreach ($slides as $slide) {
				if ($slide['slider_image']) {
					echo '<li class="slider-buttons-btn js-slider-buttons-btn ' . ($i == 0 ? 'active' : '') . '" button-identifier="' . $i . '">';
					echo '</li>';
					$i++;
				}
			}
			echo '</ul>';
			echo '</div>';
		}
		?>


		<form class="filter filter-mini hidden-xs hidden-print" method="get" action="<?php echo get_permalink($pageTemplate); ?>">
			<?php
			foreach ($selectors as $field => $item) {
				$translator = NnaTranslations::getInstance();
				$options = $item['data'];
				usort($options, function ($a, $b) {
					return strcmp(NnaTranslations::getInstance()->__($a->_id), NnaTranslations::getInstance()->__($b->_id));
				});

				$scoroTopItemnames = array(
					'apartment',
					'house',
					'cottage',
				);
				$topItemsNames = array();
				$map = NnaApi::getInstance()->getBrenollisValueMap(true);
				foreach ($scoroTopItemnames as $name) {
					if (isset($map[$name])) {
						$topItemsNames[] = $map[$name];
					} else {
						$topItemsNames[] = $name;
					}
				}
				$topItemsNames = array_reverse($topItemsNames);
				$itemsCount = 0;
				foreach ($topItemsNames as $singleName) {
					foreach ($options as $key => $option) {
						if ($option->_id == $singleName) {
							unset($options[$key]);
							array_unshift($options, $option);
							$itemsCount++;
							break;
						}
					}
				}

			?>
				<div class="filter-mini-group">
					<label class="filter-mini-label">
						<?php echo $item['label']; ?>
					</label>

					<div class="nna-dropdown dropdown-filter filter-mini-dropdown js-filter-mini-input dropdown">
						<input value="<?php echo $selectedValue ?>" name="filters[<?php echo $field; ?>]" hidden>
						<button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
							<?php echo $item['allPlaceholder']; ?>
							<i class="dropdown-filter-btn-icon"></i>
						</button>
						<ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
							<li class="nna-filter-option filter-dropdown-option" value="" text=""><?php echo $item['allPlaceholder']; ?></li>
							<?php foreach ($options as $key => $option) { ?>
								<li class="nna-filter-option filter-dropdown-option" filter-value="<?php echo $option->_id; ?>"><?php echo $translator->__($option->_id); ?></li>
								<?php if ($itemsCount != 0 && $key == $itemsCount - 1): ?>
									<li class="dropdown-seperator"></li>
								<?php endif ?>
							<?php } ?>
						</ul>
					</div>
				</div>
			<?php
			} ?>

			<div class="filter-mini-group">
				<?php foreach ($levels as $field => $options) {
					usort($options['items'], function ($a, $b) {
						return strcmp($a['name'], $b['name']);
					});

					$topItemsNames = array(
						array(
							'name' => 'Tallinn',
							'county' => 'Harju maakond',
							'target' => 'Tallinn',
						),
						array(
							'name' => 'Tartu',
							'county' => 'Tartu maakond',
							'target' => 'Tartu linn',
						),
						array(
							'name' => 'Pärnu',
							'county' => 'Pärnu maakond',
							'target' => 'Pärnu linn',
						),
					);
					$topItemsNames = array_reverse($topItemsNames);
					$itemsCount = 0;
					foreach ($topItemsNames as $city => $singleName) {
						foreach ($options['items'] as $key => $option) {
							if ($option['name'] == $singleName['county']) {
								$option['name'] = $singleName['name'];
								$option['filter-child-action'] = 'filter-pick-child="' . $singleName['target'] . '"';
								array_unshift($options['items'], $option);
								$itemsCount++;
								break;
							}
						}
					}
				?>
					<label class="filter-mini-label">
						<?php echo $options['label']; ?>
					</label>

					<div class="nna-dropdown dropdown-filter filter-mini-dropdown js-filter-mini-input dropdown">
						<input value="<?php echo $selectedValue ?>" name="filters[<?php echo $field; ?>]" hidden>
						<button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
							<?php echo $options['allPlaceholder']; ?>
							<i class="dropdown-filter-btn-icon"></i>
						</button>
						<ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
							<li class="nna-filter-option filter-dropdown-option" value="" text=""><?php echo $options['allPlaceholder']; ?></li>
							<?php foreach ($options['items'] as $key => $option) { ?>
								<li class="nna-filter-option filter-dropdown-option" filter-value="<?php echo $option['value'] ?>" data-parent="<?php echo $option['parent'] ?>" <?php echo (isset($option['filter-child-action']) ? $option['filter-child-action'] : ''); ?>><?php echo $option['name']; ?></li>
								<?php if ($itemsCount != 0 && $key == $itemsCount - 1): ?>
									<li class="dropdown-seperator"></li>
								<?php endif ?>
							<?php } ?>
						</ul>
					</div>
				<?php
				}
				?>
			</div>

			<div class="filter-mini-input-group filter-mini-group">
				<label class="filter-mini-label"><?php pll_e('Keyword'); ?></label>
				<input name="filters[keyWord]" class="filter-input filter-mini-input js-filter-mini-lg-input" placeholder="<?php pll_e('Street, object ID, broker, etc'); ?>">
				<button class="main-button filter-mini-btn js-filter-mini-btn"><?php pll_e('Search'); ?></button>
			</div>
		</form>

		<div class="mob-filter visible-xs">

			<button class="mob-filter-btn js-mob-filter-btn">
				<span class="mob-filter-btn-txt">
					<?php pll_e('Search properties (filter)'); ?>
				</span>
			</button>

			<form class="mob-filter-dropdown js-mob-filter-dropdown filter filter-mini" method="get" action="<?php echo get_permalink($pageTemplate); ?>">
				<?php
				foreach ($selectors as $field => $item) {
					$translator = NnaTranslations::getInstance();
					$options = $item['data'];
					usort($options, function ($a, $b) {
						return strcmp(NnaTranslations::getInstance()->__($a->_id), NnaTranslations::getInstance()->__($b->_id));
					});

					$topItemsNames = array(
						'apartment',
						'house',
						'cottage',
					);
					$topItemsNames = array_reverse($topItemsNames);
					$itemsCount = 0;
					foreach ($topItemsNames as $singleName) {
						foreach ($options as $key => $option) {
							if ($option->_id == $singleName) {
								unset($options[$key]);
								array_unshift($options, $option);
								$itemsCount++;
								break;
							}
						}
					}
				?>
					<div class="mob-filter-group">
						<label class="filter-label">
							<?php echo $item['label']; ?>:
						</label>

						<div class="nna-dropdown dropdown-filter dropdown">
							<input value="<?php echo $selectedValue ?>" name="filters[<?php echo $field; ?>]" hidden>
							<button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
								<?php echo $item['allPlaceholder']; ?>
								<i class="dropdown-filter-btn-icon"></i>
							</button>
							<ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
								<li class="nna-filter-option filter-dropdown-option" value="" text=""><?php echo $item['allPlaceholder']; ?></li>
								<?php foreach ($options as $key => $option) { ?>
									<li class="nna-filter-option filter-dropdown-option" filter-value="<?php echo $option->_id; ?>"><?php echo $translator->__($option->_id); ?></li>
									<?php if ($itemsCount != 0 && $key == $itemsCount - 1): ?>
										<li class="dropdown-seperator"></li>
									<?php endif ?>
								<?php } ?>
							</ul>
						</div>
					</div>
				<?php
				} ?>
				<?php foreach ($levels as $field => $options) {
					usort($options['items'], function ($a, $b) {
						return strcmp($a['name'], $b['name']);
					});

					$topItemsNames = array(
						array(
							'name' => 'Tallinn',
							'county' => 'Harju maakond',
							'target' => 'Tallinn',
						),
						array(
							'name' => 'Tartu',
							'county' => 'Tartu maakond',
							'target' => 'Tartu linn',
						),
						array(
							'name' => 'Pärnu',
							'county' => 'Pärnu maakond',
							'target' => 'Pärnu linn',
						),
					);
					$topItemsNames = array_reverse($topItemsNames);
					$itemsCount = 0;
					foreach ($topItemsNames as $city => $singleName) {
						foreach ($options['items'] as $key => $option) {
							if ($option['name'] == $singleName['county']) {
								$option['name'] = $singleName['name'];
								$option['filter-child-action'] = 'filter-pick-child="' . $singleName['target'] . '"';
								array_unshift($options['items'], $option);
								$itemsCount++;
								break;
							}
						}
					}
				?>
					<div class="mob-filter-group">
						<label class="filter-label">
							<?php echo $options['label']; ?>:
						</label>

						<div class="nna-dropdown dropdown-filter dropdown-filter dropdown">
							<input value="<?php echo $selectedValue ?>" name="filters[<?php echo $field; ?>]" hidden>
							<button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
								<?php echo $options['allPlaceholder']; ?>
								<i class="dropdown-filter-btn-icon"></i>
							</button>
							<ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
								<li class="nna-filter-option filter-dropdown-option" value="" text=""><?php echo $options['allPlaceholder']; ?></li>
								<?php foreach ($options['items'] as $key => $option) { ?>
									<li class="nna-filter-option filter-dropdown-option" filter-value="<?php echo $option['value'] ?>" data-parent="<?php echo $option['parent'] ?>" <?php echo (isset($option['filter-child-action']) ? $option['filter-child-action'] : ''); ?>><?php echo $option['name']; ?></li>
									<?php if ($itemsCount != 0 && $key == $itemsCount - 1): ?>
										<li class="dropdown-seperator"></li>
									<?php endif ?>
								<?php } ?>
							</ul>
						</div>
					</div>
				<?php
				}
				?>
				<div class="mob-filter-group">
					<label class="filter-label"><?php pll_e('Keyword'); ?></label>
					<input name="filters[keyWord]" class="filter-table-input" placeholder="<?php pll_e('Street, object ID, broker, etc'); ?>">
				</div>
				<div class="mob-filter-group text-center mob-filter-group-wrap">
					<button class="main-button filter-submit-btn"><?php pll_e('Search'); ?></button>
				</div>
			</form>
		</div>

	</div>
	<div class="container">
		<div class="row">
			<div class="col-sm-5 featured">
				<?php
				$fields = get_fields();
				//Get id for offers page
				$pageTemplateArgs = [
					'post_type' => 'page',
					'fields' => 'ids',
					'nopaging' => true,
					'meta_key' => '_wp_page_template',
					'meta_value' => 'nna-objects.php'
				];
				$pageTemplate = array_values(get_posts($pageTemplateArgs))[0];
				?>
				<div class="featured-switch">
					<div class="featured-trigger active" link-identifier="home-residential">
						<?php pll_e('Find yourself a home') ?>
					</div>
					<div class="featured-trigger" link-identifier="business-residential">
						<?php pll_e('Find commercial area') ?>
					</div>
				</div>
				<div class="featured-residences active" content-indentifier="home-residential">
					<?php
					if ($fields['featured_home_residential']) {
						if (count($fields['featured_home_residential']) == 1) {
							$foreachObject = array('0');
						} else if (count($fields['featured_home_residential']) < 3) {
							$length = count($fields['featured_home_residential']);

							$foreachObject = array_rand($fields['featured_home_residential'], $length);
						} else {
							$length = 3;

							$foreachObject = array_rand($fields['featured_home_residential'], 3);
						}

						foreach ($foreachObject as $key) {
							$featuredHomeResidential = $fields['featured_home_residential'][$key];

							$condition = array('PostIds.' . pll_current_language() => $featuredHomeResidential->ID);
							$data = NnaApi::getInstance()->getObjects($condition);
							if ($data) {
								$object = $data[0]->Object;
							}
							if (isset($object) && !empty($object)) { ?>
								<div class="featured-block">
									<div class="property">
										<a href="<?php echo get_the_permalink($featuredHomeResidential->ID); ?>" class="property-thumbnail-link">
											<div class="property-thumbnail">
												<?php
												$curLang = pll_current_language();
												$images = get_field('images', $data[0]->PostIds->$curLang);
												if ($images) {
													$firstImage = $images[0]['image'];
													echo '<img class="property-thumbnail-img" src="' . $firstImage['sizes']['medium'] . '" alt="' . $firstImage['alt'] . '">';
												} else {
													echo '<img class="property-thumbnail-img" src="' . get_template_directory_uri() . '/assets/img/pindi_pakkumise_placeholder.jpg" alt="No image found">';
												}
												?>
												<div class="property-thumbnail-text">
													<?php echo NnaTranslations::getInstance()->__($object->transactions->transaction->type) . ', ' . NnaTranslations::getInstance()->__($object->objectTypes->type); ?>
												</div>
											</div>
										</a>
										<div class="property-info">
											<a href="<?php echo get_the_permalink($featuredHomeResidential->ID); ?>" class="property-info-name">
												<?php
												echo $featuredHomeResidential->post_title;
												?>
											</a>
											<div class="property-info-price">
												<?php if ($object->transactions->transaction->price) { ?>
													<?php echo trimTrailingZeroes(number_format((float)$object->transactions->transaction->price->_, 2, '.', ' ')) . ' ' . pll__('(Currency symbol)'); ?>

													<?php if ($object->areaSize) { ?>
														<span class="property-info-extra">
															<?php echo '(' . trimTrailingZeroes(number_format((float)$object->transactions->transaction->pricem2->_, 2, '.', ' ')) . ' ' . pll__('(Currency symbol)') . '/m²)'; ?>
														</span>
													<?php } ?>

												<?php } ?>
												<?php
												$condition = array(
													'Object.transactions.transaction.type' => $object->transactions->transaction->type,
													'Object.objectTypes.type' => $object->objectTypes->type,
												);

												$filters = array(
													'filters' => array(
														'transactions.transaction.type' => $object->transactions->transaction->type,
														'objectTypes.type' => $object->objectTypes->type,

													),
												);
												?>
												<div class="property-button-wrap">
													<a href="<?php echo get_permalink($pageTemplate) . '?' . http_build_query($filters); ?>" class="main-button property-button">
														<?php

														$count = NnaApi::getInstance()->getObjectsCount($condition);

														echo pll__('See') . ' ' . $count . ' ' . NnaTranslations::getInstance()->__($object->objectTypes->type . 's');
														?>
													</a>
												</div>
											</div>
										</div>
									</div>
								</div>
					<?php
							}
						}
					}
					?>
					<a href="<?php echo get_permalink($pageTemplate); ?>" class="link-button featured-link-button">
						<?php pll_e('See all offers'); ?>
					</a>
				</div>
				<div class="featured-residences" content-indentifier="business-residential">
					<?php

					if ($fields['featured_business_residential']) {
						if (count($fields['featured_business_residential']) == 1) {
							$foreachObject = array('0');
						} else if (count($fields['featured_business_residential']) < 3) {
							$length = count($fields['featured_business_residential']);

							$foreachObject = array_rand($fields['featured_business_residential'], $length);
						} else {
							$length = 3;

							$foreachObject = array_rand($fields['featured_business_residential'], 3);
						}

						foreach ($foreachObject as $key) {
							$featuredHomeResidential = $fields['featured_business_residential'][$key];

							$condition = array('PostIds.' . pll_current_language() => $featuredHomeResidential->ID);
							$data = NnaApi::getInstance()->getObjects($condition);
							if ($data) {
								$object = $data[0]->Object;
							}
							if (isset($object) && !empty($object)) { ?>
								<div class="featured-block">
									<div class="property">
										<a href="<?php echo get_the_permalink($featuredHomeResidential->ID); ?>" class="property-thumbnail-link">
											<div class="property-thumbnail">
												<div class="offer-thumbnail-img-wrap">
													<?php
													$curLang = pll_current_language();
													$images = get_field('images', $data[0]->PostIds->$curLang);
													if ($images) {
														$firstImage = $images[0]['image'];
														echo '<img class="property-thumbnail-img" src="' . $firstImage['sizes']['medium'] . '" alt="' . $firstImage['alt'] . '">';
													} else {
														echo '<img class="property-thumbnail-img" src="' . get_template_directory_uri() . '/assets/img/pindi_pakkumise_placeholder.jpg" alt="No image found">';
													}
													?>
													<div class="property-thumbnail-text">
														<?php echo NnaTranslations::getInstance()->__($object->transactions->transaction->type) . ', ' . NnaTranslations::getInstance()->__($object->objectTypes->type); ?>
													</div>
												</div>
											</div>
										</a>
										<div class="property-info">
											<a href="<?php echo get_the_permalink($featuredHomeResidential->ID); ?>" class="property-info-name">
												<?php
												echo $featuredHomeResidential->post_title;
												?>
											</a>
											<div class="property-info-price">
												<?php if ($object->transactions->transaction->price) { ?>
													<?php echo trimTrailingZeroes(number_format((float)$object->transactions->transaction->price->_, 2, '.', ' ')) . ' ' . pll__('(Currency symbol)'); ?>

													<?php if ($object->areaSize) { ?>
														<span class="property-info-extra">
															<?php echo '(' . trimTrailingZeroes(number_format((float)$object->transactions->transaction->pricem2->_, 2, '.', ' ')) . ' ' . pll__('(Currency symbol)') . '/m²)'; ?>
														</span>
													<?php } ?>

												<?php } ?>

												<?php
												$condition = array(
													'Object.transactions.transaction.type' => $object->transactions->transaction->type,
													'Object.objectTypes.type' => $object->objectTypes->type,
												);

												$filters = array(
													'filters' => array(
														'transactions.transaction.type' => $object->transactions->transaction->type,
														'objectTypes.type' => $object->objectTypes->type,

													),
												);
												?>
												<div class="property-button-wrap">
													<a href="<?php echo get_permalink($pageTemplate) . '?' . http_build_query($filters); ?>" class="main-button property-button">

														<?php
														$condition = array(
															'Object.transactions.transaction.type' => $object->transactions->transaction->type,
															'Object.objectTypes.type' => $object->objectTypes->type,
														);

														$count = NnaApi::getInstance()->getObjectsCount($condition);

														echo pll__('See') . ' ' . $count . ' ' . NnaTranslations::getInstance()->__($object->objectTypes->type . 's');
														?>
													</a>
												</div>
											</div>
										</div>
									</div>
								</div>
					<?php
							}
						}
					}
					?>
					<a href="<?php echo get_permalink($pageTemplate); ?>" class="link-button featured-link-button">
						<?php pll_e('See all offers'); ?>
					</a>
				</div>
			</div>
			<div class="col-sm-7">
				<?php
				$offices = array(
					array(
						'officeName' => 'tallinn',
						'tooltip' => 'Tallinn',
						'county' => 'harjumaa',
						'page' => get_field('tallinn_contact_page'),
					),
					array(
						'officeName' => 'haapsalu',
						'tooltip' => 'Haapsalu',
						'county' => 'laanemaa',
						'page' => get_field('haapsalu_contact_page'),
					),
					array(
						'officeName' => 'jogevamaa',
						'tooltip' => 'Jõgevamaa',
						'county' => 'jogevamaa',
						'page' => get_field('jogevamaa_contact_page'),
					),
					array(
						'officeName' => 'johvi',
						'tooltip' => 'Jõhvi',
						'county' => 'idaVirumaa',
						'page' => get_field('johvi_contact_page'),
					),
					array(
						'officeName' => 'kuressaare',
						'tooltip' => 'Kuressaare',
						'county' => 'saaremaa',
						'page' => get_field('kuressaare_contact_page'),
					),
					array(
						'officeName' => 'hiiumaa',
						'tooltip' => 'Hiiumaa',
						'county' => 'hiiumaa',
						'page' => get_field('hiiumaa_contact_page'),
					),
					array(
						'officeName' => 'narva',
						'tooltip' => 'Narva',
						'county' => 'idaVirumaa',
						'page' => get_field('narva_contact_page'),
					),
					array(
						'officeName' => 'paide',
						'tooltip' => 'Paide',
						'county' => 'jarvamaa',
						'page' => get_field('paide_contact_page'),
					),
					array(
						'officeName' => 'polvamaa',
						'tooltip' => 'Põlvamaa',
						'county' => 'polvamaa',
						'page' => get_field('polvamaa_contact_page'),
					),
					array(
						'officeName' => 'parnu',
						'tooltip' => 'Pärnu',
						'county' => 'parnumaa',
						'page' => get_field('parnu_contact_page'),
					),
					array(
						'officeName' => 'rakvere',
						'tooltip' => 'Rakvere',
						'county' => 'laaneVirumaa',
						'page' => get_field('rakvere_contact_page'),
					),
					array(
						'officeName' => 'rapla',
						'tooltip' => 'Rapla',
						'county' => 'raplamaa',
						'page' => get_field('rapla_contact_page'),
					),
					array(
						'officeName' => 'tapa',
						'tooltip' => 'Tapa',
						'county' => 'laaneVirumaa',
						'page' => get_field('tapa_contact_page'),
					),
					array(
						'officeName' => 'tartu',
						'tooltip' => 'Tartu',
						'county' => 'tartumaa',
						'page' => get_field('tartu_contact_page'),
					),
					array(
						'officeName' => 'turi',
						'tooltip' => 'Türi',
						'county' => 'jarvamaa',
						'page' => get_field('turi_contact_page'),
					),
					array(
						'officeName' => 'valga',
						'tooltip' => 'Valga',
						'county' => 'valgamaa',
						'page' => get_field('valga_contact_page'),
					),
					array(
						'officeName' => 'viljandi',
						'tooltip' => 'Viljandi',
						'county' => 'viljandimaa',
						'page' => get_field('viljandi_contact_page'),
					),
					array(
						'officeName' => 'voru',
						'tooltip' => 'Võru',
						'county' => 'vorumaa',
						'page' => get_field('voru_contact_page'),
					),
				);
				?>

				<h3 class="primary-heading heading-no-margin">
					<?php pll_e('Find our closest Pindi office near you!'); ?>
				</h3>
				<div class="location">
					<div class="location-map">
						<div class="map-wrapper" style="position: relative;">
							<img src="<?php echo get_template_directory_uri(); ?>/assets/img/map_estonia.svg" usemap="#estonia-map" id="estonia-map" style="width: 100%; height: 420px;" />
							<map name="estonia-map">
								<area class="em-county" identifier="harjumaa" name="harjumaa" shape="poly" href="#" coords="296,117,290,117,289,111,284,110,283,104,277,104,277,99,272,98,269.8,92,265,93,265,80,236,81,235,87,235,98.5,218,99,217,105,211,105,212,111,199,111,199,104,199,92,176,93,175,86,169,86,169,80,163,80,164,63,193,45,214.5,33,217,12.5,245.8,12.5,271,5.5,302,5.5,340.5,1.8,344,17.5,343,50,350,51,349,69,326,69,326,75,313,75,314,88,313,93,307,93,308,104,302,105,302,116" />
								<area class="em-county" identifier="hiiumaa" name="hiiumaa" shape="poly" href="#" coords="58.5,171,70.5,171,82.5,155.5,110,146,109,137.5,103,131.5,100.5,119.5,97,113.5,86,110,84.2,105,76.5,101.5,62,101.5,52.5,107.5,44,117,31,119.5,17.7,119.5,19,131.5,28.5,137.5,46.5,137.5,50,167.5" />
								<area class="em-county" identifier="saaremaa" name="saaremaa" shape="poly" href="#" coords="20,293.5,37,279,46.5,251.5,74,248,98,233.5,118.5,224,136.5,206,142.5,194,134,179.5,127,161.5,112.5,161.5,100.5,173.5,91,179.5,67,179.5,61,176,52.5,177,46.5,185.5,34.5,188,25,195,16.5,192.7,4.5,195,0,213,0,237,10.5,249,14,269.5,10.5,291" />
								<area class="em-county" identifier="polvamaa" name="polvamaa" shape="poly" href="#" coords="488,242,496.5,250,502.5,267,514.5,287.5,520.5,302,512,305.5,496.5,304,494,302.9,493,296,476,296,475,285,458,284,457,279,452,279,451,284,409,284,410,255,415,254,415,248,433,248,434,242" />
								<area class="em-county" identifier="vorumaa" name="vorumaa" shape="poly" href="#" coords="494,302.9,493,296,476,296,475,285,458,284,457,279,452,279,451,284,439,284,409,284,391,285,392,290,391,296,392,302,397,302,397,308,403,308,403,314,398,314,397,351.8,434,350,436.5,340,442.5,340.3,446,351.8,472.5,351.8,487,336.3,494,320" />
								<area class="em-county" identifier="valgamaa" name="valgamaa" shape="poly" href="#" coords="397,344,397,321,398,314,403,314,403,308,397,308,397,302,392,302,391,296,392,290,391,285,409,284,410,255,398,254,385,255,385,260,349,260,349,255,343,255,343,261,338,261,337,266,331,266,331,272,326,272,325,278,320,278,319,285,341.5,303,356,305.5,365.5,323.5,388.5,349,397,351.8" />
								<area class="em-county" identifier="viljandimaa" name="viljandimaa" shape="poly" href="#" coords="319,285,320,278,325,278,326,272,331,272,331,266,337,266,338,261,343,261,343,255,349,255,349,260,361,260,362,243,355,243,355,231,344,230,343,225,355,224,355,218,355,213,361,212,366.1,213.6,370.5,212,373,210.8,373,201,368,200,368,195,362,195,361,189,356,189,355,183,352.5,182,337,182,337.5,170,320,171,319,176,289,177,289,188,283,188,284,194,272,194,271,224,283,224,283,230,289,230,289,248,278,249,276.7,256.5,277,280" />
								<area class="em-county" identifier="parnumaa" name="parnumaa" shape="poly" href="#" coords="151,203.5,165.5,231,172.5,260,194,273,215.5,302,226.5,303,235,297,232.5,287.5,251.8,286.3,256.5,281.5,262.5,287.5,277,280,276.7,256.5,278,249,289,248,289,230,283,230,283,224,271,224,271,200,272,194,284,194,283,188,289,188,289,177,290,177,295,177,295,164,266,164,265,171,259,170,259,158.5,241,158,241,164,224,164,223,170,182,170,181,176,176,177,175,188,149.8,189" />
								<area class="em-county" identifier="laanemaa" name="laanemaa" shape="poly" href="#" coords="110,102.5,130.5,102.5,139,94,134,83.5,137.5,71.5,164,64,163,80,169,80,169,86,175,86,176,93,199,92,199,111,193,111,194,134,200,134,199,140,194,140,194,152,200,152,199,170,182,170,181,176,176,177,175,188,149.8,189,142.5,183,137.8,170,142.5,159,155,155.5,153,148,140,155.5,129,142,136.5,137.5,131.5,128,121,124.8,110,124,106.5,107.5" />
								<area class="em-county" identifier="raplamaa" name="raplamaa" shape="poly" href="#" coords="193,128,193,112.3,193,111,199,111,212,111,211,105,217,105,218,99,235,98.5,236,81,265,80,265,93,269.8,92,272,98,277,99,277,104,283,104,284,110,289,111,290,117,296,117,295,128,290,129,290,140,295,141,295,146,289,147,289,164,266,164,265,171,259,170,259,158.5,241,158,241,164,224,164,223,170,199,170,199,164,200,152,194,152,194,140,199,140,200,134,194,134" />
								<area class="em-county" identifier="jarvamaa" name="jarvamaa" shape="poly" href="#" coords="313,75,314,88,313,93,307,93,308,104,302,105,302,116,296,117,295,128,290,129,290,140,295,141,295,146,289,147,289,164,295,164,295,177,319,176,320,171,326,171,337.5,170,338,159,343,158,343,153,355,152,356,141,367,140,368,135,367,117,362,117,361,113.5,362,105,349,105,350,93,355,92,356,87,349,87,349,81,319,79.2,319,75" />
								<area class="em-county" identifier="laaneVirumaa" name="laaneVirumaa" shape="poly" href="#" coords="344,17.5,343,32,343,50,350,51,349,69,326,69,326,75,319,75,319,79.2,325,80,349,81,349,87,356,87,355,92,350,93,349,105,362,105,361,113.5,362,117,367,117,368,135,373,135,373,129,385,128,385,134,392,134,392,128,409,128,409,123,421,122,422,93,433,92,434,81,428,80,427,77.5,427,75,421,74,421,57,415,56,416,23.5,415,20,393,17.5,362,11.5" />
								<area class="em-county" identifier="jogevamaa" name="jogevamaa" shape="poly" href="#" coords="452,161.5,445,155.5,439,152,439,135,421,134,421,122,409,123,409,128,392,128,392,134,385,134,385,128,373,129,373,135,368,135,367,140,356,141,355,147,355,152,343,153,343,158,338,159,337,182,352.5,182,355,183,356,189,361,189,362,195,379,195,391,194,392,189,411.5,189,427,188,427,177,451,176" />
								<area class="em-county" identifier="idaVirumaa" name="idaVirumaa" shape="poly" href="#" coords="547,56,535,68,526.5,74,524,89.5,517,95.5,512,110,464,111,463,116,451,117,451,122,445,123,445,128,439,129,439,135,421,134,421,119.5,422,93,433,92,434,81,428,80,427,77.5,427,75,421,74,421,57,415,56,415,50,416,23.5,440,35.5,466.5,35.5,506,41.5,535,35.5,547,44" />
								<area class="em-county" identifier="tartumaa" name="tartumaa" shape="poly" href="#" coords="490.5,238,482,236,481,212,475,212,475,206,463,206,463,182,463,176,458,176,457,165,451.8,164,451,176,427,177,427,188,392,189,391,194,368,195,368,200,373,201,373,210.8,367.8,213,367.8,224,367,236,367,242,362,243,361,260,385,260,385,255,398,254,410,255,415,254,415,248,433,248,434,242,488,242" />
							</map>
							<ul class="em-tags" style="list-style: none;">
								<?php
								foreach ($offices as $office) {
									echo '<li class="em-tag tags tags-' . $office['officeName'] . '" identifier="' . $office['officeName'] . '" county-identifier="' . $office['county'] . '">';
									echo '<div class="em-tooltip">' . $office['tooltip'] . '</div>';
									echo '<span class="tags tags-inner"></span>';
									echo '</li>';
								}
								?>
							</ul>
						</div>
					</div>

					<?php
					$officeContacts = array();
					foreach ($offices as $office) {
						$officeId = '';
						if ($office['page']) {
							$officeId = $office['page']->ID;
						}
						$contact = array(
							'name' => $office['officeName'],
							'id' => $officeId,
						);
						array_push($officeContacts, $contact);
					}

					if ($officeContacts) {
						$i = 0;
						echo '<div class="location-contact location-contact-front-page">';
						foreach ($officeContacts as $officeContact) {
							if ($officeContact) {
								$args = array(
									'title' => get_field($officeContact['name'] . '_title'),
									'contactsType' => 'field',
									'customFieldsName' => 'daily_contact_person',
									'pageID' => $officeContact['id'],
								);
								$contact = getContact($args);
								if ($contact) { ?>
									<div class="em-content contact contact-front <?php echo ($i == 0 ? 'active' : ''); ?>" identifier="<?php echo $officeContact['name']; ?>">
										<?php
										if ($contact['title']) {
											echo '<div class="contact-heading">';
											echo '<h3>' . $contact['title'] . '</h3>';
											echo '</div>';
										}
										?>
										<div class="contact-thumbnail secondary-contact-thumbnail">
											<?php if ($contact['thumbnail']): ?>
												<img src="<?php echo $contact['thumbnail']['sizes']['medium']; ?>" alt="<?php echo $contact['thumbnail']['alt']; ?>" class="contact-thumbnail-img">
											<?php else: ?>
												<img src="<?php echo get_template_directory_uri(); ?>/assets/img/no_pic_placeholder.png" alt="No image available" class="contact-thumbnail-img">
											<?php endif; ?>
										</div>
										<div class="contact-info secondary-contact-info">
											<?php if ($contact['name']): ?>
												<div class="contact-info-name"><a class="contact-info-link" href="<?php echo $contact['link']; ?>"><?php echo $contact['name']; ?></a></div>
											<?php endif ?>
											<?php if ($contact['occupation']): ?>
												<div class="contact-info-title"><?php echo $contact['occupation']; ?></div>
											<?php endif ?>

											<?php if ($contact['languages']): ?>
												<div class="contact-languages visible-xs">
													<div class="flags">
														<ul class="flags-list">
															<?php
															foreach ($contact['languages'] as $lang) {
																echo '<li class="flag-item flag-' . $lang . '"></li>';
															}
															?>
														</ul>
													</div>
												</div>
											<?php endif ?>
											<ul class="contact-details">
												<?php if ($contact['phone']): ?>
													<li class="contact-details-item">
														<!-- <?php pll_e('Phone'); ?>: -->
														<a class="contact-details-item-link" href="tel:<?php echo $contact['phone']; ?>">
															<?php echo $contact['phone'] ?>
														</a>
													</li>
												<?php endif; ?>
												<?php if ($contact['mobile']): ?>
													<li class="contact-details-item">
														<!-- <?php pll_e('Mobile'); ?>: -->
														<a class="contact-details-item-link" href="tel:<?php echo $contact['mobile']; ?>">
															<?php echo $contact['mobile'] ?>
														</a>
													</li>
												<?php endif; ?>
												<?php if ($contact['email']): ?>
													<li class="contact-details-item">
														<!-- <?php pll_e('Email'); ?>: -->
														<a class="contact-details-item-link" href="mailto:<?php echo $contact['email']; ?>">
															<?php echo $contact['email'] ?>
														</a>
													</li>
												<?php endif; ?>
											</ul>
											<div class="js-contact-links contact-links">
												<div class="make-contact js-make-contact">
													<div class="make-contact-lightbox js-close-make-contact">
													</div>
													<div class="make-contact-box mailto">
														<div class="mailto-heading">
															<div class="gallery-close-button mailto-close-button js-close-make-contact"></div>
															<h5 class="mailto-heading-header"><?php pll_e('Contact us') ?></h5>
														</div>
														<div class="mailto-content clearfix">
															<div class="mailto-content-contact">
																<div class="contact">
																	<div class="contact-thumbnail contact-thumbnail-block">
																		<?php if ($contact['thumbnail']): ?>
																			<img src="<?php echo $contact['thumbnail']['sizes']['medium']; ?>" alt="<?php echo $contact['thumbnail']['alt']; ?>" class="contact-thumbnail-img">
																		<?php else: ?>
																			<img src="<?php echo get_template_directory_uri(); ?>/assets/img/no_pic_placeholder.png" alt="No image available" class="contact-thumbnail-img">
																		<?php endif; ?>
																	</div>
																	<div class="contact-info contact-info-block">
																		<?php
																		if ($contact['name']) {
																			echo '<div class="contact-info-name">' . $contact['name'] . '</div>';
																		}
																		if ($contact['occupation']) {
																			echo '<div class="contact-info-title">' . $contact['occupation'] . '</div>';
																		}

																		if ($contact['languages']) {
																			echo '<div class="contact-languages">';
																			echo '<div class="flags">';
																			echo '<ul class="flags-list">';
																			foreach ($contact['languages'] as $lang) {
																				echo '<li class="flag-item flag-' . $lang . '"></li>';
																			}
																			echo '</ul>';
																			echo '</div>';
																			echo '</div>';
																		}
																		?>
																		<ul class="contact-details">
																			<?php if ($contact['phone']): ?>
																				<li class="contact-details-item">
																					<!-- <?php pll_e('Phone'); ?>: -->
																					<a class="contact-details-item-link" href="tel:<?php echo $contact['phone']; ?>">
																						<?php echo $contact['phone'] ?>
																					</a>
																				</li>
																			<?php endif; ?>
																			<?php if ($contact['mobile']): ?>
																				<li class="contact-details-item">
																					<!-- <?php pll_e('Mobile'); ?>: -->
																					<a class="contact-details-item-link" href="tel:<?php echo $contact['mobile']; ?>">
																						<?php echo $contact['mobile'] ?>
																					</a>
																				</li>
																			<?php endif; ?>
																			<?php if ($contact['email']): ?>
																				<li class="contact-details-item">
																					<!-- <?php pll_e('Email'); ?>: -->
																					<a class="contact-details-item-link" href="mailto:<?php echo $contact['email']; ?>">
																						<?php echo $contact['email'] ?>
																					</a>
																				</li>
																			<?php endif; ?>
																		</ul>
																	</div>
																</div>
															</div>

															<form class="ga-contact-form js-contact-person-form mailto-content-form clearfix" identifier-action="sendMailTakeContact" ga-identifier="<?php echo get_the_permalink(); ?>" ga-category="Esileht">
																<div class="js-form-submitted-message mailto-form-after-submit-message">
																</div>
																<div class="mailto-form-group js-field">
																	<input name="mailForm[id]" type="text" value="<?php echo $contact['contactID']; ?>" hidden>
																	<label class="mailto-label">
																		<?php pll_e('Name'); ?>
																	</label>
																	<input name="mailForm[name]" type="text" class="js-input js-required mailto-form-input">
																	<span class='js-error'></span>
																</div>
																<div class="mailto-form-group mailto-form-group-last js-field">
																	<label class="mailto-label">
																		<?php pll_e('Email'); ?>
																	</label>
																	<input name="mailForm[email]" type="text" class="js-input js-required js-email mailto-form-input">
																	<span class='js-error'></span>
																</div>
																<div class="clearfix"></div>

																<div class="mailto-form-group-full js-field">
																	<label class="mailto-label">
																		<?php pll_e('Message content'); ?>
																	</label>
																	<textarea name="mailForm[message]" class="js-input js-required mailto-form-textarea"></textarea>
																	<span class='js-error'></span>
																</div>
																<button class="js-contact-person-form-submit btn main-button" type="button"><?php pll_e('Send message'); ?></button>
															</form>
														</div>
													</div>
												</div>

												<button class="btn main-button contact-links-button js-make-contact-button">
													<?php pll_e('Take contact') ?>
												</button>

												<div class="contact-link-wrap hidden-xs">
													<a class="link-button contact-links-link" href="<?php echo get_permalink($officeContact['id']); ?>">
														<?php pll_e('See all employers'); ?>
													</a>
												</div>
											</div>
										</div>
									</div>
					<?php
								}
								$i++;
							}
						}
						echo '</div>';
					}
					?>
				</div>
			</div>
		</div>
	</div>

	<?php
	$newDevelopmentsTitle = get_field('featured_new_developments_title');
	$newDevelopmentsUnfixed = get_field('featured_new_developments_objects');
	if (isset($newDevelopmentsUnfixed) && !empty($newDevelopmentsUnfixed)) {
		$newDevelopments = array_values($newDevelopmentsUnfixed);
	}

	$newDevelopmentsOrderRules = get_field('featured_new_developments_objects_deny_side_by_side');
	$denialRules = array();
	// to get the other side of the array
	$ruleKeysVariations = array(
		'object_1' => 'object_2',
		'object_2' => 'object_1',
	);
	if (isset($newDevelopmentsOrderRules) && !empty($newDevelopmentsOrderRules)) {
		foreach ($newDevelopmentsOrderRules as $rulegroup) {
			foreach ($rulegroup as $key => $rule) {
				foreach ($rule as $id) {
					foreach ($rulegroup[$ruleKeysVariations[$key]] as $denyId) {
						// create key, if it doesnt exist
						if (!array_key_exists($id, $denialRules)) {
							$denialRules[$id] = array();
						}
						// push value to array
						if (!in_array($denyId, $denialRules[$id])) {
							array_push($denialRules[$id], $denyId);
						}
					}
				}
			}
		}
	}

	if ($newDevelopments) {	?>
		<div class="featured-products bg-featured-products">
			<div class="container">
				<div class="featured-products-header devprojects hidden-xs">
					<?php if ($newDevelopmentsTitle) {
						echo ' <h4 class="devprojects-heading"> ';
						echo $newDevelopmentsTitle;
						echo ' </h4> ';
					} ?>
					<?php
					$newDevsTemplateArgs = [
						'post_type' => 'page',
						'fields' => 'ids',
						'nopaging' => true,
						'meta_key' => '_wp_page_template',
						'meta_value' => 'page-templates/new_developments.php'
					];
					$newDevs = array_values(get_posts($newDevsTemplateArgs))[0];
					?>
					<a class="devprojects-link front-devprojects-link" href="<?php echo get_the_permalink($newDevs); ?>"><?php pll_e('See all new developments') ?></a>
				</div>
				<div class="featured-products-content">
					<div class="row hidden-xs">
						<?php
						// shuffle($newDevelopments);
						$newDevelopmentObjects = array();
						$prevID = '';
						if (count($newDevelopments) > 4) {
							$count = 4;
						} else {
							$count = count($newDevelopments);
						}
						for ($i = 0; $i < $count; $i++) {
							$hadValue = false;
							shuffle($newDevelopments);
							foreach ($newDevelopments as $key => $item) {
								$allowed = false;
								if (!array_key_exists($item->ID, $denialRules)) {
									$allowed = true;
								} elseif (!in_array($prevID, $denialRules[$item->ID])) {
									$allowed = true;
								}

								if ($allowed) {
									$prevID = $item->ID;
									array_push($newDevelopmentObjects, $item);
									unset($newDevelopments[$key]);
									$hadValue = true;
									break;
								}
							}
							if (!$hadValue) {
								break;
							}
						}

						foreach ($newDevelopmentObjects as $object) {
							$newDevelopment = $object;
							$newDevelopmentsLink = get_field('new_development_link', $newDevelopment->ID);
						?>
							<div class="col-xs-3">
								<a <?php echo ($newDevelopmentsLink ? 'target="_BLANK"' : ''); ?> href="<?php echo ($newDevelopmentsLink ? $newDevelopmentsLink : get_the_permalink($newDevelopment->ID)); ?>" class="project">
									<div class="project-thumbnail">
										<div class="project-thumbnail-abs">
											<div class="offer-thumbnail-img-wrap">
												<?php
												$images = get_field('product_images', $newDevelopment->ID);
												if ($images) {
													$firstImage = $images[0]['image'];
													echo '<img class="project-thumbnail-img" src="' . $firstImage['sizes']['large'] . '" alt="' .  $firstImage['alt'] . '">';
												} else {
													echo '<img class="project-thumbnail-img" src="' . get_template_directory_uri() . '/assets/img/pindi_pakkumise_placeholder.jpg" alt="No image found">';
												}

												$thumbnailInfo = get_field('transaction_type', $newDevelopment->ID);
												if ($thumbnailInfo) { ?>
													<div class="project-thumbnail-info">
														<?php echo $thumbnailInfo; ?>
													</div>
												<?php
												}
												?>
											</div>
										</div>
									</div>
									<div class="project-name">
										<?php echo $newDevelopment->post_title; ?>
									</div>
								</a>
							</div>
						<?php
						}
						?>
					</div>

					<div class="mob-slider js-mob-slider visible-xs">
						<?php
						foreach ($newDevelopmentObjects as $object) {
							$newDevelopment = $object;
							$newDevelopmentsLink = get_field('new_development_link', $newDevelopment->ID);
						?>
							<a <?php echo ($newDevelopmentsLink ? 'target="_BLANK"' : ''); ?> href="<?php echo ($newDevelopmentsLink ? $newDevelopmentsLink : get_the_permalink($newDevelopment->ID)); ?>" class="mob-slider-item-wrap">
								<?php
								$images = get_field('product_images', $newDevelopment->ID);
								echo '<div class="mob-slider-item">';
								echo '<div class="mob-slider-img-wrap">';
								echo '<div class="offer-thumbnail-img-wrap">';
								if ($images) {
									$firstImage = $images[0]['image'];
									echo '<img class="mob-slider-img" src="' . $firstImage['sizes']['large'] . '" alt="' .  $firstImage['alt'] . '">';
								} else {
									echo '<img class="mob-slider-img" src="' . get_template_directory_uri() . '/assets/img/pindi_pakkumise_placeholder.jpg" alt="No image found">';
								}
								echo '</div>';
								echo '</div>';
								$thumbnailInfo = get_field('transaction_type', $newDevelopment->ID);
								if ($thumbnailInfo) { ?>
									<div class="project-thumbnail-info mob-slider-info">
										<?php echo $thumbnailInfo; ?>
									</div>
								<?php
								}
								echo '</div>'; ?>
								<div class="project-name">
									<?php echo $newDevelopment->post_title; ?>
								</div>
							</a>
						<?php } ?>
					</div>

				</div>
			</div>
		</div>
	<?php
	}
	?>

	<div class="container">
		<div class="row">
			<?php
			$newsQueryArgs = array(
				'post_type' => array(
					'post',
				),
				'post_status' => array(
					'publish',
				),
				'posts_per_page' => 3,
			);

			$newsQuery = new WP_Query($newsQueryArgs);

			if ($newsQuery->have_posts()) { ?>
				<div class="col-sm-5">
					<h3 class="secondary-heading-w-border">
						<?php pll_e('Latest news'); ?>
					</h3>
					<div class="news">
						<ul class="news-list">
							<?php
							while ($newsQuery->have_posts()) : $newsQuery->the_post();
							?>
								<li class="news-item news-item-mini">
									<a href="<?php echo get_the_permalink(); ?>" class="news-link">
										<div class="message">
											<div class="message-date message-date-mini">
												<?php echo get_the_date('d.m.Y'); ?>
											</div>
											<h3 class="message-heading message-heading-mini">
												<?php echo get_the_title(); ?>
											</h3>
											<div class="message-intro message-intro-mini">
												<?php echo customExcerpt(120); ?>
											</div>
										</div>
									</a>
								</li>
							<?php endwhile; ?>
						</ul>
					</div>
					<a class="link-button" href="<?php echo get_permalink(get_option('page_for_posts')); ?>"><?php pll_e('Read all news'); ?></a>
				</div>
			<?php
			}
			/* Restore original Post Data */
			wp_reset_postdata(); ?>

			<div class="col-sm-7">
				<h3 class="secondary-heading-w-border">
					<?php pll_e('Realestate analysis and info'); ?>
				</h3>
				<div class="row">
					<div class="col-sm-5">
						<?php
						$index = get_field('index_percentage', pll_get_post($infoPageId));
						$indexVal = get_field('index_value', pll_get_post($infoPageId));
						$indexIsRaising = get_field('index_is_raising', pll_get_post($infoPageId));
						$indexLastUpdated = get_field('index_last_updated', pll_get_post($infoPageId));
						$offersCount = NnaApi::getInstance()->getObjectsCount();
						$args = array(
							'posts_per_page'   => -1,
							'post_type'        => 'new-developments',
							'post_status'      => 'publish',
						);
						$newDevsCount = count(get_posts($args));

						//Get id for market survey page
						$marketSurveyArgs = [
							'post_type' => 'page',
							'fields' => 'ids',
							'nopaging' => true,
							'meta_key' => '_wp_page_template',
							'meta_value' => 'page-templates/market_surveys.php'
						];
						$marketSurveyPageId = array_values(get_posts($marketSurveyArgs))[0];
						?>
						<div class="stats">
							<?php if ($index || $indexVal) { ?>
								<div class="stats-index">
									<div class="factor">

										<div class="factor-label"><?php pll_e('Pindi index'); ?>:</div>
										<?php if ($index) { ?>
											<a href="<?php echo get_the_permalink($marketSurveyPageId); ?>" class="factor-data factor-data-link factor-data-lg <?php echo ($indexIsRaising == 'asc' ? 'ascending' : '') . ($indexIsRaising == 'desc' ? 'descending' : ''); ?>"><?php echo $index; ?></a>
										<?php } ?>

										<?php if ($indexVal) { ?>
											<div class="factor-details">
												<?php echo $indexVal; ?>
											</div>
										<?php } ?>
									</div>
								</div>
							<?php } ?>

							<div class="stats-offers">
								<div class="factor">
									<div class="factor-label"><?php pll_e('Total offers'); ?>:</div>
									<div class="factor-data"><?php echo $offersCount; ?></div>
								</div>
							</div>
							<div class="stats-projects">
								<div class="factor">
									<div class="factor-label"><?php pll_e('New development projects'); ?>:</div>
									<div class="factor-data"><?php echo $newDevsCount; ?></div>
								</div>
							</div>
							<span class="stats-date">
								<?php echo $indexLastUpdated; ?>
							</span>

							<a href="<?php echo get_the_permalink($marketSurveyPageId); ?>" class="link-button link-button-secondary"><?php pll_e('Market analysis'); ?></a>


						</div>
					</div>
					<div class="col-sm-7">
						<div class="subscribe">
							<form class="js-subscribe-newsletter subscribe-join">
								<div class="subscribe-success">
									<span class="subscribe-success-text">
										<?php pll_e('Successfully joined newsletter'); ?>
									</span>
								</div>
								<h6 class="subscribe-title">
									<?php pll_e('Front page subscription heading'); ?>
								</h6>
								<span class="subscribe-info">
									<?php pll_e('Front page subscription intro'); ?>
								</span>
								<?php
								$langs = array(
									'et' => 'Estonian',
									'en' => 'English',
									'ru' => 'Russian',
								);
								?>
								<input class="js-field-language hidden" value="<?php echo $langs[pll_current_language()]; ?>">
								<input class="js-field-email subscribe-join-input" placeholder="<?php pll_e('Insert email address'); ?>">

								<div class="subscribe-privacy">
									<input type="checkbox" name="checkbox" value="check" class="privacy-checkbox js-privacy-checkbox" />
									<a href="/privaatsuspoliitika/" target="_blank"><?php pll_e('Newsletter privacy agreement'); ?></a>
								</div>
								<div class="subscribe-privacy-error">
									<span class="subscribe-error-text"><?php pll_e('Newsletter privacy agreement error'); ?></span>
								</div>
								<div class="subscribe-email-error">
									<span class="subscribe-error-text"><?php pll_e('Newsletter field error'); ?></span>
								</div>

								<button type="submit" class="main-button main-button-larger"><?php pll_e('Join'); ?></button>
							</form>
							<div class="subscribe-share">
								<ul class="social-icons subscribe-social-icons">
									<li class="social-icons-item">
										<a class="social-icons-link" href="<?php echo $facebook; ?>" target="_BLANK">
											<i class="fa fa-facebook social-icons-link-icon social-icons-link-fb"></i>
										</a>
									</li>
									<li class="social-icons-item">
										<a class="social-icons-link" href="<?php echo $linkedin; ?>" target="_BLANK">
											<i class="fa fa-linkedin social-icons-link-icon"></i>
										</a>
									</li>
									<li class="social-icons-item">
										<a class="social-icons-link" href="<?php echo $rss; ?>">
											<i class="fa fa-rss social-icons-link-icon social-icons-link-rss"></i>
										</a>
									</li>
									<li class="social-icons-item">
										<a class="social-icons-link" href="<?php echo $instagram; ?>">
											<i class="fa fa-instagram social-icons-link-icon"></i>
										</a>
									</li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<?php if (is_user_logged_in()): ?>
		<?php $frontPageId = pll_get_post(551); ?>

		<?php if (get_field('ad_banner_visibility', $frontPageId)): ?>
			<div class="ad-banner js-ad-banner">
				<span class="icon icon--button icon--button-close icon--ad-banner js-ad-banner-close"></span>

				<?php if (get_field('ad_banner_type', $frontPageId) === 'HTML'): ?>
					<?php echo get_field('ad_banner_html', $frontPageId); ?>
				<?php endif; ?>
			</div>
		<?php endif; ?>
	<?php endif; ?>

	<?php get_template_part('blocks/b_footer'); ?>

</body>

</html>