<?php
/**
 * The Template for displaying a single object
 */
?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>


    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-offer-single">
    <?php
        $queryData = array(
            'transactions.transaction.price._' => array(
                'min' => '',
                'max' => '',
            ),
            'transactions.transaction.pricem2._' => array(
                'min' => '',
                'max' => '',
            ),
            'floorNumber' => array(
                'min' => '',
                'max' => '',
            ),
            'numberOfRooms' => array(
                'min' => '',
                'max' => '',
            ),
            'areaSize._' => array(
                'min' => '',
                'max' => '',
            ),
            'level1' => '',
            'level2' => '',
            //'level3' => '',
            'objectTypes.type' => '',
            'transactions.transaction.type' => '',
            'alacrity' => '',
            'keyWord' => '',
        );

        $pricem2 = false;
        if (isset($_GET['filters']) && is_array($_GET['filters'])) {
            $queryData = array_merge($queryData, $_GET['filters']);
            if (isset($_GET['filters']['transactions.transaction.pricem2._'])) {
                $pricem2 = true;
            }
        }
        // print_r($queryData);

        $pipeline = array(
            array(
                '$group' => array(
                    '_id' => array(
                        'level1' => '$Object.level1',
                        'level2' => '$Object.level2',
                        'level3' => '$Object.level3',
                    ),
                )
            ),
            array(
                '$group' => array(
                    '_id' => array(
                        'level1' => '$_id.level1',
                        'level2' => '$_id.level2',
                    ),
                    //'level3' => array(
                    //    '$push' => '$_id.level3',
                    //)
                ),

            ),
            array(
                '$group' => array(
                    '_id' => '$_id.level1',
                    'level2' => array(
                        '$push' => array(
                            '_id' => '$_id.level2',
                            //'level3' => '$level3',
                        )
                    )
                ),
            )
        );

        $hierarchicalSelectors = NnaApi::getInstance()->aggregateObjects($pipeline);
        $levels = array(
            'level1' => array(
                'label' => pll__('County'),
                'allPlaceholder' => pll__('All (County)'),
                'items' => array(),
                'parent' => '',
                'hidden' => false,
            ),
            'level2' => array(
                'label' => pll__('City'),
                'allPlaceholder' => pll__('All (City)'),
                'items' => array(),
                'parent' => 'level1',
                'hidden' => !empty($queryData['level1']),
            ),
            /*'level3' => array(
                'label' => pll__('Village'),
                'allPlaceholder' => pll__('All (Village)'),
                'items' => array(),
                'parent' => 'level2',
                'hidden' => !empty($queryData['level2']),
            ),*/
        );
        $selected = array(
            'level1' => false,
            'level2' => false,
        );
        foreach ($hierarchicalSelectors as $level1) {
            $level1Hidden = false;
            $levels['level1']['items'][] = array(
                'name' => $level1->_id,
                'value' => $level1->_id,
                'parent' => '',
                'hidden' => $level1Hidden,
            );
            $selected['level1'] = $level1->_id === $queryData['level1'];
            if (isset($level1->level2)) {
                foreach ($level1->level2 as $level2) {
                    $level2Hidden = $levels['level2']['hidden'] && !$selected['level1'];
                    $levels['level2']['items'][] = array(
                        'name' => $level2->_id,
                        'value' => $level2->_id,
                        'parent' => $level1->_id,
                        'hidden' => $level2Hidden,
                    );
                    $selected['level2'] = $level2->_id === $queryData['level2'];
                    if (isset($level2->level3)) {
                        foreach ($level2->level3 as $level3) {
                            if (!empty($level3)) {
                                $levels['level3']['items'][] = array(
                                    'name' => $level3,
                                    'value' => $level3,
                                    'parent' => $level2->_id,
                                    'hidden' => ($levels['level3']['hidden'] || $level2Hidden) && !$selected['level2']
                                );
                            }
                        }
                    }
                }
            }
        }
        $selectors = array();

        $pipeline = array(
            array(
                '$group' => array(
                    '_id' => '$Object.objectTypes.type',
                ),
            ),
        );
        $selectors['objectTypes.type'] = array(
            'label' => pll__('Object type'),
            'allPlaceholder' => pll__('All (Object type)'),
            'data' => NnaApi::getInstance()->aggregateForSelector($pipeline),
        );

        $pipeline = array(
            array(
                '$group' => array(
                    '_id' => '$Object.transactions.transaction.type',
                ),
            ),
        );
        $selectors['transactions.transaction.type'] = array(
            'label' => pll__('Transaction type'),
            'allPlaceholder' => pll__('All (Transaction type)'),
            'data' => NnaApi::getInstance()->aggregateForSelector($pipeline),
        );

        $pipeline = array(
            array(
                '$group' => array(
                    '_id' => '$Object.alacrity',
                ),
            ),
        );
        $alacrity = array(
            'label' => pll__('Condition'),
            'allPlaceholder' => pll__('All (Condition)'),
            'data' => NnaApi::getInstance()->aggregateForSelector($pipeline),
        );


        if (isset($_GET['sort'])) {
            $sort = $_GET['sort'];
        } else {
            $sort = array('lastModified' => 1);
        }
        $sortKey = array_keys($sort)[0];


        if (isset($_GET['filterOpen'])) {
            $filterOpen = $_GET['filterOpen'];
        } else {
            $filterOpen = 0;
        }

        //Get id for offers page
        $pageTemplateArgs = [
            'post_type' => 'page',
            'fields' => 'ids',
            'nopaging' => true,
            'meta_key' => '_wp_page_template',
            'meta_value' => 'nna-objects.php'
        ];
        $pageTemplate = array_values(get_posts($pageTemplateArgs))[0];

        $condition = array();
        if (isset($_GET['favourites']) && $_GET['favourites'] == 1) {
            if ($_SESSION['favourites']){
                $condition = array(
                    'Object.originalId' => array(
                        '$in' => array(
                        ),
                    ),
                );
                foreach ($_SESSION['favourites'] as $itemId) {
                    array_push($condition['Object.originalId']['$in'], (int)$itemId);
                }
            }
        } else if (isset($_GET['filters'])) {
            $condition = NnaApi::getInstance()->handleObjectFilters($_GET['filters']);
        }

        // get the current item position number
        $options = array();
        $options['sort'] = NnaApi::getInstance()->handleSort($sort);

        $offset = $_GET['offset'];

        if ($offset) {
            $currentCount = (int)$offset + 1;
        } else{
            $currentCount = 1;
            $offset = 0;
            $options['skip'] = 0;
        }
        $count = (int)NnaApi::getInstance()->getObjectsCount($condition);
        $noFirst = false;
        $noLast = false;
        if ($count != 1) {
            $filters = array();
            if (isset($_GET) && !empty($_GET)) {
                $filters = $_GET;
            }

            if ($offset == 0) {
                $noFirst = true;
                $options['limit'] = 2;
                $options['skip'] = (int)$offset;

                $data = NnaApi::getInstance()->getObjects($condition, $options);
                $objects = NnaApi::getInstance()->fixObjects($data, false, $options['skip']);

                $filters['offset'] = $objects[1]->Object->offset;
                $nextLink = $objects[1]->Object->url . ($filters ? '?' . http_build_query($filters) : '');
            } elseif ($offset + 1 == $count) {
                $noLast = true;
                $options['limit'] = 2;
                $options['skip'] = (int)$offset - 1;

                $data = NnaApi::getInstance()->getObjects($condition, $options);
                $objects = NnaApi::getInstance()->fixObjects($data, false, $options['skip']);

                $filters['offset'] = $objects[0]->Object->offset;
                $prevLink = $objects[0]->Object->url . ($filters ? '?' . http_build_query($filters) : '');
            } else{
                $options['limit'] = 3;
                $options['skip'] = (int)$offset - 1;

                $data = NnaApi::getInstance()->getObjects($condition, $options);
                $objects = NnaApi::getInstance()->fixObjects($data, false, $options['skip']);

                $filters['offset'] = $objects[0]->Object->offset;
                $prevLink = $objects[0]->Object->url . ($filters ? '?' . http_build_query($filters) : '');
                $filters['offset'] = $objects[2]->Object->offset;
                $nextLink = $objects[2]->Object->url . ($filters ? '?' . http_build_query($filters) : '');
            }
        } else{
            $noFirst = true;
            $noLast = true;
        }

        $condition = array('PostIds.' . pll_current_language() => get_the_ID());
        $data = NnaApi::getInstance()->getObjects($condition);
        $object = $data[0]->Object;

        // print_r($object);

        $postIdByTemplateArgs = [
            'post_type' => 'page',
            'fields' => 'ids',
            'nopaging' => true,
            'meta_key' => '_wp_page_template',
            'meta_value' => 'nna-objects.php'
        ];
        $postIdByTemplate = array_values(get_posts($postIdByTemplateArgs))[0];

        global $wp_query;
        $save_query = $wp_query->queried_object_id;
        $wp_query->queried_object_id = $postIdByTemplate;

        get_template_part('blocks/b_header');

        $wp_query->queried_object_id = $save_query;
    ?>

    <div class="container">
        <div class="printable-header visible-print">
            <span class="printable-object-type">
                <!-- <?php
                    $optionGroups = NnaApi::getInstance()->optionGroups();
                    echo NnaTranslations::getInstance()->__($object->transactions->transaction->type . ' (printing template)') . ' ' . NnaTranslations::getInstance()->__($optionGroups[$object->objectTypes->type] . ' (printing template)') . '!';
                ?> -->
            </span>

            <div class="logo printable-logo">
                <a class="printable-logo-link" href="<?php echo get_home_url(); ?>">
                    <img src="<?php echo get_template_directory_uri(); ?>/assets/img/pindi_kinnisvara_logo.svg" alt="">
                </a>
            </div>
        </div>
        <form class="nna-filters hidden-xs filter main-filter <?php echo (!$filterOpen ? 'not-detail' : ''); ?>" method="get" action="<?php echo get_permalink($pageTemplate); ?>">
            <input name="curPage" value="1" hidden>
            <input name="perPage" value="9" hidden>
            <input class="nna-sort-field" name="sort[<?php echo $sortKey; ?>]" value="<?php echo $sort[$sortKey]; ?>" hidden>
            <input class="nna-has-favourites" name="favourites" value="0" hidden>
            <input class="nna-filter-open" name="filterOpen" value="<?php echo $filterOpen; ?>" hidden>
            <table class="table filter-table filter-table-top">
                <tr>
                    <?php
                    $translator = NnaTranslations::getInstance();
                    foreach ($selectors as $field => $item) {
                        $options = $item['data'];
                        usort($options, function($a, $b){
                            return strcmp(NnaTranslations::getInstance()->__($a->_id), NnaTranslations::getInstance()->__($b->_id));
                        });

                        $scoroTopItemnames = array(
                            'apartment',
                            'house',
                            'cottage',
                        );
                        $topItemsNames = array();
                        $map = NnaApi::getInstance()->getBrenollisValueMap(true);
                        foreach ($scoroTopItemnames as $name) {
                            if (isset($map[$name])) {
                                $topItemsNames[] = $map[$name];
                            } else {
                                $topItemsNames[] = $name;
                            }
                        }
                        $topItemsNames = array_reverse($topItemsNames);
                        $itemsCount = 0;
                        foreach ($topItemsNames as $singleName) {
                            foreach ($options as $key => $option) {
                                if ($option->_id == $singleName) {
                                    unset($options[$key]);
                                    array_unshift($options, $option);
                                    $itemsCount++;
                                    break;
                                }
                            }
                        }

                        ?>
                        <td>
                            <div class="nna-dropdown dropdown-filter dropdown">
                                <?php
                                $selectedValue = '';
                                foreach ($options as $option) {
                                    if ($option->_id == $queryData[$field]) {
                                        $selectedValue = $option->_id;
                                    }
                                }
                                ?>
                                <input value="<?php echo $selectedValue; ?>" name="filters[<?php echo $field; ?>]" hidden>
                                <label class="filter-label"><?php echo $item['label'] ?></label>
                                <button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle <?php echo (!isset($selectedValue) || empty($selectedValue) ? 'gray' : ''); ?>" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <?php
                                    if ($selectedValue) {
                                        echo $translator->__($selectedValue);
                                    } else{
                                        echo $item['allPlaceholder'];
                                    }
                                    ?>
                                    <i class="dropdown-filter-btn-icon"></i>
                                </button>
                                <ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
                                    <li class="nna-filter-option filter-dropdown-option" value="" text=""><?php echo $item['allPlaceholder']; ?></li>
                                    <?php foreach ($options as $key => $option) { ?>
                                        <li class="nna-filter-option filter-dropdown-option" filter-value="<?php echo $option->_id; ?>"><?php echo $translator->__($option->_id); ?></li>
                                        <?php if ($itemsCount != 0 && $key == $itemsCount - 1): ?>
                                            <li class="dropdown-seperator"></li>
                                        <?php endif ?>
                                    <?php } ?>
                                </ul>
                            </div>
                        </td>
                    <?php } ?>

                    <?php
                    foreach ($levels as $field => $options) {
                        usort($options['items'], function($a, $b){
                            return strcmp($a['name'], $b['name']);
                        });
                        $selectedValue = '';
                        foreach ($options['items'] as $option) {
                            if ($option['name'] == $queryData[$field]) {
                                $selectedValue = $option['name'];
                            }
                        }

                        $topItemsNames = array(
                            array(
                                'name' => 'Tallinn',
                                'county' => 'Harju maakond',
                                'target' => 'Tallinn',
                            ),
                            array(
                                'name' => 'Tartu',
                                'county' => 'Tartu maakond',
                                'target' => 'Tartu linn',
                            ),
                            array(
                                'name' => 'Pärnu',
                                'county' => 'Pärnu maakond',
                                'target' => 'Pärnu linn',
                            ),
                        );
                        $topItemsNames = array_reverse($topItemsNames);
                        $itemsCount = 0;
                        foreach ($topItemsNames as $city => $singleName) {
                            foreach ($options['items'] as $key => $option) {
                                if ($option['name'] == $singleName['county']) {
                                    $option['name'] = $singleName['name'];
                                    $option['filter-child-action'] = 'filter-pick-child="' . $singleName['target'] . '"';
                                    array_unshift($options['items'], $option);
                                    $itemsCount++;
                                    break;
                                }
                            }
                        }
                        ?>
                        <td>
                            <div class="nna-dropdown dropdown-filter dropdown">
                                <input class="nna-hierarchical-selector" value="<?php echo $selectedValue ?>" name="filters[<?php echo $field; ?>]" data-parent="filters[<?php echo $options['parent'] ?>]" hidden>
                                <label class="filter-label"><?php echo $options['label'] ?></label>
                                <button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <?php
                                    if ($selectedValue) {
                                        echo NnaTranslations::getInstance()->__($selectedValue);
                                    } else{
                                        echo $options['allPlaceholder'];
                                    }
                                    ?>
                                    <i class="dropdown-filter-btn-icon"></i>
                                </button>
                                <ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
                                    <li class="all nna-filter-option filter-dropdown-option" value="" text=""><?php echo $options['allPlaceholder']; ?></li>
                                    <?php foreach ($options['items'] as $key => $option) { ?>
                                        <li class="nna-filter-option filter-dropdown-option" filter-value="<?php echo $option['value'] ?>" data-parent="<?php echo $option['parent'] ?>" <?php echo (isset($option['filter-child-action']) ? $option['filter-child-action'] : ''); ?>><?php echo $option['name']; ?></li>
                                        <?php if ($itemsCount != 0 && $key == $itemsCount - 1): ?>
                                            <li class="dropdown-seperator"></li>
                                        <?php endif ?>
                                    <?php } ?>
                                </ul>
                            </div>
                        </td>
                    <?php } ?>

                    <td class="js-filter-td">
                        <div class="filter-table-group filter-table-group-half js-filter-table-group">
                            <label class="filter-label filter-label-radio">
                                <input class="nna-change-pricetype filter-radio-input" name="nna-change-pricetype" type="radio" value="filters[transactions.transaction.price._]"<?php if (!$pricem2) { echo ' checked';} ?>>
                                <span class="filter-radio-btn"></span>
                                <?php pll_e('Price'); ?>
                            </label>
                            <?php
                            if ($pricem2) { ?>
                                <input class="nna-change-pricetype-field-min filter-table-input filter-table-input-full" type="text" name="filters[transactions.transaction.pricem2._][min]" value="<?php echo $queryData['transactions.transaction.pricem2._']['min']; ?>" placeholder="<?php pll_e('Price from'); ?>">
                                <?php
                            } else {
                                ?>
                                <input class="nna-change-pricetype-field-min filter-table-input  filter-table-input-full" type="text" name="filters[transactions.transaction.price._][min]" value="<?php echo $queryData['transactions.transaction.price._']['min']; ?>" placeholder="<?php pll_e('Price from'); ?>">
                                <?php
                            } ?>
                        </div>
                        <span class="filter-divider">-</span>
                        <div class="filter-table-group filter-table-group-half js-filter-table-group">
                            <label class="filter-label filter-label-radio">
                                <input class="nna-change-pricetype filter-radio-input" name="nna-change-pricetype" type="radio" value="filters[transactions.transaction.pricem2._]"<?php if ($pricem2) { echo ' checked';} ?>>
                                <span class="filter-radio-btn"></span>
                                <?php pll_e('Price per square metre (filter)'); ?>

                            </label>
                            <?php
                            if ($pricem2) { ?>
                                <input class="nna-change-pricetype-field-max filter-table-input filter-table-input-full" type="text" name="filters[transactions.transaction.pricem2._][max]" value="<?php echo $queryData['transactions.transaction.pricem2._']['max']; ?>" placeholder="<?php pll_e('Price to'); ?>">
                                <?php
                            } else {
                                ?>
                                <input class="nna-change-pricetype-field-max filter-table-input filter-table-input-full" type="text" name="filters[transactions.transaction.price._][max]" value="<?php echo $queryData['transactions.transaction.price._']['max']; ?>" placeholder="<?php pll_e('Price to'); ?>">
                                <?php
                            }
                            ?>
                        </div>
                    </td>
                    <td>
                        <label class="filter-label js-filter-label"><?php pll_e('Number of rooms'); ?></label>
                        <div class="filter-table-group filter-table-group-half js-filter-table-group">
                            <input class="filter-table-input filter-table-input-full" type="text" name="filters[numberOfRooms][min]" value="<?php echo $queryData['numberOfRooms']['min']; ?>" placeholder="<?php pll_e('From'); ?>">
                        </div>
                        <span class="filter-divider">-</span>
                        <div class="filter-table-group filter-table-group-half js-filter-table-group">
                            <input class="filter-table-input filter-table-input-full" type="text" name="filters[numberOfRooms][max]" value="<?php echo $queryData['numberOfRooms']['max']; ?>" placeholder="<?php pll_e('To'); ?>">
                        </div>
                    </td>
                </tr>
            </table>
            <table class="table filter-table filter-table-bottom">
                <tr>
                    <td class="filter-table-data">
                        <div class="js-filter-table-data" style="width:100%;">
                            <label class="filter-label filter-label-inline js-keywords-label"><?php pll_e('Keyword'); ?></label>
                            <input class="filter-table-input filter-table-input-lg js-keywords-input" name="filters[keyWord]" value="<?php echo $queryData['keyWord']; ?>" placeholder="<?php pll_e('Street, object ID, broker, etc'); ?>">
                        </div>
                    </td>
                    <td class="text-right">
                        <button type="button" class="nna-detailed-search-button filter-submit-btn filter-submit-btn-sm js-filter-button"><?php pll_e('Detailed search'); ?></button>
                        <button type="submit" class="filter-submit-btn js-filter-button"><?php pll_e('Find objects'); ?></button>
                    </td>
                </tr>
            </table>
            <table class="table filter-table  filter-table-hidden">
                <tr class="nna-detailed-search">
                    <?php
                        $options = $alacrity['data'];
                        usort($options, function($a, $b){
                            return strcmp(NnaTranslations::getInstance()->__($a->_id), NnaTranslations::getInstance()->__($b->_id));
                        });
                        $field = 'alacrity';
                        $selectedValue = '';
                        foreach ($options as $option) {
                            if ($option->_id == $queryData[$field]) {
                                $selectedValue = $option->_id;
                            }
                        }
                    ?>

                    <td class="filter-table-data">
                        <label class="filter-label  js-filter-label"><?php pll_e('Size'); ?></label>
                        <div class="filter-table-group filter-table-group-half js-filter-table-group">
                            <input class="filter-table-input filter-table-input-full" name="filters[areaSize._][min]" value="<?php echo $queryData['areaSize._']['min']; ?>" placeholder="<?php pll_e('Size from'); ?>">
                        </div>
                        <span class="filter-divider">-</span>
                        <div class="filter-table-group filter-table-group-half js-filter-table-group">
                            <input class="filter-table-input filter-table-input-full" name="filters[areaSize._][max]" value="<?php echo $queryData['areaSize._']['max']; ?>" placeholder="<?php pll_e('Size to'); ?>">
                        </div>
                    </td>
                    <td>
                        <div class="nna-dropdown dropdown-filter dropdown js-filter-dropdown">
                            <input value="<?php echo $selectedValue; ?>" name="filters[<?php echo $field; ?>]" hidden>
                            <label class="filter-label"><?php echo $alacrity['label'] ?></label>
                            <button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-filter-btn-inline dropdown-toggle dropdown" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <?php
                                if ($selectedValue) {
                                    echo NnaTranslations::getInstance()->__($selectedValue);
                                } else{
                                    echo $alacrity['allPlaceholder'];
                                }
                                ?>
                                <i class="dropdown-filter-btn-icon"></i>
                            </button>
                            <ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
                                <li class="nna-filter-option filter-dropdown-option" value="" text=""><?php echo $alacrity['allPlaceholder']; ?></li>
                                <?php foreach ($options as $option) { ?>
                                    <li class="nna-filter-option filter-dropdown-option" filter-value="<?php echo $option->_id; ?>"><?php echo $translator->__($option->_id); ?></li>
                                <?php } ?>
                            </ul>
                        </div>
                    </td>

                    <td class="filter-table-data">
                        <label class="filter-label filter-label js-filter-label"><?php pll_e('Floor (filter)'); ?>:</label>
                        <div class="filter-table-group filter-table-group-half js-filter-table-group">
                            <input class="filter-table-input filter-table-input-full" type="text" name="filters[floorNumber][min]" value="<?php echo $queryData['floorNumber']['min']; ?>" placeholder="<?php pll_e('From'); ?>">
                        </div>
                        <span class="filter-divider">-</span>
                        <div class="filter-table-group filter-table-group-half js-filter-table-group">
                            <input class="filter-table-input filter-table-input-full" type="text" name="filters[floorNumber][max]" value="<?php echo $queryData['floorNumber']['max']; ?>" placeholder="<?php pll_e('To'); ?>">
                        </div>
                    </td>
                    <td class="filter-empty">
                        <div class="filter-empty-element">
                            &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp;&amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp;&amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp;
                            &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp;&amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp;&amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp;
                        </div>
                    </td>
                </tr>
            </table>
        </form>

        <div class="mob-filter visible-xs">
            <button class="mob-filter-btn js-mob-filter-btn">
                <span class="mob-filter-btn-txt">
                     <?php pll_e('Search properties (filter)'); ?>
                </span>
            </button>
            <form class="nna-filters filter mob-filter-dropdown js-mob-filter-dropdown main-filter <?php echo (!$filterOpen ? 'not-detail' : ''); ?>" method="get" action="<?php echo get_permalink($pageTemplate); ?>">
                <input name="curPage" value="1" hidden>
                <input name="perPage" value="9" hidden>
                <input class="nna-sort-field" name="sort[<?php echo $sortKey; ?>]" value="<?php echo $sort[$sortKey]; ?>" hidden>
                <input class="nna-has-favourites" name="favourites" value="0" hidden>
                <input class="nna-filter-open" name="filterOpen" value="<?php echo $filterOpen; ?>" hidden>
                <?php
                $translator = NnaTranslations::getInstance();
                foreach ($selectors as $field => $item) {
                    $options = $item['data'];
                    usort($options, function($a, $b){
                        return strcmp(NnaTranslations::getInstance()->__($a->_id), NnaTranslations::getInstance()->__($b->_id));
                    });

                    $topItemsNames = array(
                        'apartment',
                        'house',
                        'cottage',
                    );
                    $topItemsNames = array_reverse($topItemsNames);
                    $itemsCount = 0;
                    foreach ($topItemsNames as $singleName) {
                        foreach ($options as $key => $option) {
                            if ($option->_id == $singleName) {
                                unset($options[$key]);
                                array_unshift($options, $option);
                                $itemsCount++;
                                break;
                            }
                        }
                    }
                    ?>
                        <div class="nna-dropdown dropdown-filter dropdown">
                            <?php
                            $selectedValue = '';
                            foreach ($options as $option) {
                                if ($option->_id == $queryData[$field]) {
                                    $selectedValue = $option->_id;
                                }
                            }
                            ?>
                            <input value="<?php echo $selectedValue; ?>" name="filters[<?php echo $field; ?>]" hidden>
                            <label class="filter-label"><?php echo $item['label'] ?></label>
                            <button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle <?php echo (!isset($selectedValue) || empty($selectedValue) ? 'gray' : ''); ?>" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <?php
                                if ($selectedValue) {
                                    echo NnaTranslations::getInstance()->__($selectedValue);
                                } else{
                                    pll_e('All');
                                }
                                ?>
                                <i class="dropdown-filter-btn-icon"></i>
                            </button>
                            <ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
                                <li class="nna-filter-option filter-dropdown-option" value="" text=""><?php pll_e('All'); ?></li>
                                <?php foreach ($options as $key => $option) { ?>
                                    <li class="nna-filter-option filter-dropdown-option" filter-value="<?php echo $option->_id; ?>"><?php echo $translator->__($option->_id); ?></li>
                                    <?php if ($itemsCount != 0 && $key == $itemsCount - 1): ?>
                                        <li class="dropdown-seperator"></li>
                                    <?php endif ?>
                                <?php } ?>
                            </ul>
                        </div>
                <?php } ?>

                <?php
                foreach ($levels as $field => $options) {
                    usort($options['items'], function($a, $b){
                        return strcmp($a['name'], $b['name']);
                    });
                    $selectedValue = '';
                    foreach ($options['items'] as $option) {
                        if ($option['name'] == $queryData[$field]) {
                            $selectedValue = $option['name'];
                        }
                    }

                    $topItemsNames = array(
                        array(
                            'name' => 'Tallinn',
                            'county' => 'Harju maakond',
                            'target' => 'Tallinn',
                        ),
                        array(
                            'name' => 'Tartu',
                            'county' => 'Tartu maakond',
                            'target' => 'Tartu linn',
                        ),
                        array(
                            'name' => 'Pärnu',
                            'county' => 'Pärnu maakond',
                            'target' => 'Pärnu linn',
                        ),
                    );
                    $topItemsNames = array_reverse($topItemsNames);
                    $itemsCount = 0;
                    foreach ($topItemsNames as $city => $singleName) {
                        foreach ($options['items'] as $key => $option) {
                            if ($option['name'] == $singleName['county']) {
                                $option['name'] = $singleName['name'];
                                $option['filter-child-action'] = 'filter-pick-child="' . $singleName['target'] . '"';
                                array_unshift($options['items'], $option);
                                $itemsCount++;
                                break;
                            }
                        }
                    }
                ?>
                <div class="nna-dropdown dropdown-filter dropdown">
                    <input class="nna-hierarchical-selector" value="<?php echo $selectedValue ?>" name="filters[<?php echo $field; ?>]" data-parent="filters[<?php echo $options['parent'] ?>]" hidden>
                    <label class="filter-label"><?php echo $options['label'] ?></label>
                    <button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <?php
                        if ($selectedValue) {
                            echo NnaTranslations::getInstance()->__($selectedValue);
                        } else{
                            echo $options['allPlaceholder'];
                        }
                        ?>
                        <i class="dropdown-filter-btn-icon"></i>
                    </button>
                    <ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
                        <li class="all nna-filter-option filter-dropdown-option" value="" text=""><?php echo $options['allPlaceholder']; ?></li>
                        <?php foreach ($options['items'] as $key => $option) { ?>
                            <li class="nna-filter-option filter-dropdown-option" filter-value="<?php echo $option['value'] ?>" data-parent="<?php echo $option['parent'] ?>" <?php echo (isset($option['filter-child-action']) ? $option['filter-child-action'] : ''); ?>><?php echo $option['name']; ?></li>
                            <?php if ($itemsCount != 0 && $key == $itemsCount - 1): ?>
                                <li class="dropdown-seperator"></li>
                            <?php endif ?>
                        <?php } ?>
                    </ul>
                </div>
                <?php } ?>
                    <div class="mob-filter-group">
                        <div class="filter-table-group">
                            <label class="filter-label filter-label-radio">
                                <input class="nna-change-pricetype filter-radio-input" name="nna-change-pricetype" type="radio" value="filters[transactions.transaction.price._]"<?php if (!$pricem2) { echo ' checked';} ?>>
                                <span class="filter-radio-btn"></span>
                                <?php pll_e('Price'); ?>
                            </label>
                            <?php
                            if ($pricem2) { ?>
                                <input class="nna-change-pricetype-field-min filter-table-input" type="text" name="filters[transactions.transaction.pricem2._][min]" value="<?php echo $queryData['transactions.transaction.pricem2._']['min']; ?>" placeholder="<?php pll_e('Price from'); ?>">
                                <?php
                            } else {
                                ?>
                                <input class="nna-change-pricetype-field-min filter-table-input" type="text" name="filters[transactions.transaction.price._][min]" value="<?php echo $queryData['transactions.transaction.price._']['min']; ?>" placeholder="<?php pll_e('Price from'); ?>">
                                <?php
                            } ?>
                        </div>
                        <span class="filter-divider">-</span>
                        <div class="filter-table-group filter-table-group-right">
                            <label class="filter-label filter-label-radio">
                                <input class="nna-change-pricetype filter-radio-input" name="nna-change-pricetype" type="radio" value="filters[transactions.transaction.pricem2._]"<?php if ($pricem2) { echo ' checked';} ?>>
                                <span class="filter-radio-btn"></span>
                                <?php pll_e('Price per square metre (filter)'); ?>
                            </label>
                            <?php
                            if ($pricem2) { ?>
                                <input class="nna-change-pricetype-field-max filter-table-input" type="text" name="filters[transactions.transaction.pricem2._][max]" value="<?php echo $queryData['transactions.transaction.pricem2._']['max']; ?>" placeholder="<?php pll_e('Price to'); ?>">
                                <?php
                            } else {
                                ?>
                                <input class="nna-change-pricetype-field-max filter-table-input" type="text" name="filters[transactions.transaction.price._][max]" value="<?php echo $queryData['transactions.transaction.price._']['max']; ?>" placeholder="<?php pll_e('Price to'); ?>">
                                <?php
                            }
                            ?>
                        </div>
                    </div>
                    <div class="mob-filter-group">
                        <label class="filter-label"><?php pll_e('Size'); ?></label>
                        <div class="filter-table-group">
                            <input class="filter-table-input" name="filters[areaSize._][min]" value="<?php echo $queryData['areaSize._']['min']; ?>" placeholder="<?php pll_e('Size from'); ?>">
                        </div>
                        <span class="filter-divider">-</span>
                        <div class="filter-table-group  filter-table-group-right">
                            <input class="filter-table-input" name="filters[areaSize._][max]" value="<?php echo $queryData['areaSize._']['max']; ?>" placeholder="<?php pll_e('Size to'); ?>">
                        </div>
                    </div>
                    <div class="mob-filter-group">
                        <label class="filter-label"><?php pll_e('Keyword'); ?></label>
                        <input class="filter-table-input" name="filters[keyWord]" value="<?php echo $queryData['keyWord']; ?>" placeholder="<?php pll_e('Street, object ID, broker, etc'); ?>">
                    </div>

                    <?php
                        $options = $alacrity['data'];
                        usort($options, function($a, $b){
                            return strcmp(NnaTranslations::getInstance()->__($a->_id), NnaTranslations::getInstance()->__($b->_id));
                        });
                        $field = 'alacrity';
                        $selectedValue = '';
                        foreach ($options as $option) {
                            if ($option->_id == $queryData[$field]) {
                                $selectedValue = $option->_id;
                            }
                        }
                    ?>

                    <div class="nna-dropdown dropdown-filter dropdown">
                        <input value="<?php echo $selectedValue; ?>" name="filters[<?php echo $field; ?>]" hidden>
                        <label class="filter-label"><?php echo $alacrity['label'] ?></label>
                        <button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <?php
                            if ($selectedValue) {
                                echo NnaTranslations::getInstance()->__($selectedValue);
                            } else{
                                pll_e('All');
                            }
                            ?>
                            <i class="dropdown-filter-btn-icon"></i>
                        </button>
                        <ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
                            <li class="nna-filter-option filter-dropdown-option" value="" text=""><?php pll_e('All'); ?></li>
                            <?php foreach ($options as $option) { ?>
                                <li class="nna-filter-option filter-dropdown-option" filter-value="<?php echo $option->_id; ?>"><?php echo $translator->__($option->_id); ?></li>
                            <?php } ?>
                        </ul>
                    </div>
                    <div class="mob-filter-group">
                        <label class="filter-label"><?php pll_e('Floor'); ?>:</label>
                        <div class="filter-table-group">
                            <input class="filter-table-input" type="text" name="filters[floorNumber][min]" value="<?php echo $queryData['floorNumber']['min']; ?>" placeholder="<?php pll_e('From'); ?>">
                        </div>
                            <span class="filter-divider">-</span>
                        <div class="filter-table-group filter-table-group-right">
                            <input class="filter-table-input" type="text" name="filters[floorNumber][max]" value="<?php echo $queryData['floorNumber']['max']; ?>" placeholder="<?php pll_e('To'); ?>">
                        </div>
                    </div>
                    <div class="mob-filter-group">
                        <label class="filter-label"><?php pll_e('Number of rooms'); ?>:</label>
                        <div class="filter-table-group">
                            <input class="filter-table-input" type="text" name="filters[numberOfRooms][min]" value="<?php echo $queryData['numberOfRooms']['min']; ?>" placeholder="<?php pll_e('From'); ?>">
                        </div>
                            <span class="filter-divider">-</span>
                        <div class="filter-table-group filter-table-group-right">
                            <input class="filter-table-input" type="text" name="filters[numberOfRooms][max]" value="<?php echo $queryData['numberOfRooms']['max']; ?>" placeholder="<?php pll_e('To'); ?>">
                        </div>
                    </div>
                    <div class="mob-filter-group text-center mob-filter-group-wrap">
                        <button type="submit" class="filter-submit-btn"><?php pll_e('Find objects'); ?></button>
                    </div>
            </form>
        </div>

        <div class="visible-xs text-center">
            <div class="main-button mob-offer-button js-back">
                <?php pll_e('Back to results'); ?>
            </div>
        </div>
        <div class="mob-paginate visible-xs">
            <?php
                if (!$noFirst) {
                    echo '<a href="' . $prevLink . '" class="mob-paginate-button paginate-prev"><i class="fa fa-angle-left"></i></a>';
                }
                echo $currentCount . '/' . $count;
                if (!$noLast) {
                    echo '<a href="' . $nextLink . '" class="mob-paginate-button paginate-next"><i class="fa fa-angle-right"></i></a>';
                }
            ?>
        </div>
        <div class="offer">
            <div class="offer-header hidden-xs">
                <div class="offer-header-nav">
                    <div class="main-button offer-header-button js-back">
                        <?php pll_e('Back to results'); ?>
                    </div>
                    <div class="paginate">
                        <?php
                            if (!$noFirst) {
                                echo '<a href="' . $prevLink . '" class="paginate-button paginate-prev"><i class="fa fa-angle-left"></i></a>';
                            }
                            echo '<div class="paginate-number">' . $currentCount . '/' . $count . '</div>';
                            if (!$noLast) {
                                echo '<a href="' . $nextLink . '" class="paginate-button paginate-next"><i class="fa fa-angle-right"></i></a>';
                            }
                        ?>
                    </div>
                </div>
                <div class="offer-header-misc hidden-xs">
                    <?php $favourites = isset($_SESSION['favourites']) ? $_SESSION['favourites'] : array(); ?>
                    <ul class="misc">
                        <li class="misc-item"><span class="nna-add-favourite nna-single-object misc-icon fa <?php echo (in_array($object->originalId, $favourites) ? 'fa-star active' : 'fa-star-o'); ?>" item-id="<?php echo $object->originalId; ?>"></span></li>
                        <li class="misc-item"><a href="" class="js-fb-share misc-icon fa fa-share-alt"></a></li>
                        <li class="misc-item"><a href="javascript:if(window.print)window.print()" class="misc-icon fa fa-print"></a></li>
                    </ul>
                </div>
            </div>

            <div class="offer-name lg-offer-name visible-print">
                <?php
                    $title = NnaApi::getInstance()->getAddress($object);
                    echo $title;
                    if ($object->Country) { ?>
                        <div class="offer-name-extra">
                            <?php echo $object->Country . ($object->level1 ? ', ' . $object->level1 : ''); ?>
                        </div>
                <?php } ?>
                <?php
                    if ($object->isBooked) {
                        echo '<div class="offer-booked">' . pll__('Booked until (single object)') . ' ' . $object->bookedUntil . '</div>';
                    }
                ?>
            </div>
            <div class="row">
                <div class="col-sm-6 print-col-5">
                <div class="offer-name lg-offer-name hidden-print">
                    <?php
                        $title = NnaApi::getInstance()->getAddress($object);
                        echo $title;
                        if ($object->Country) { ?>
                            <div class="offer-name-extra">
                                <?php echo $object->Country . ($object->level1 ? ', ' . $object->level1 : ''); ?>
                            </div>
                    <?php } ?>
                    <?php
                        if ($object->isBooked) {
                            echo '<div class="offer-booked">' . pll__('Booked until (single object)') . ' ' . $object->bookedUntil . '</div>';
                        }
                    ?>
                </div>
                    <div class="row">
                        <div class="hidden">
                            <?php
                                // print_r($object);
                                $detailsList = getObjectDetails($object);
                                $detailsLists = array_chunk($detailsList, ceil(count($detailsList) / 2));
                            ?>
                        </div>
                        <?php
                            foreach ($detailsLists as $list) {
                                echo '<div class="col-md-6"><div class="offer-details">';
                                foreach ($list as $detail) {
                                    ?>
                                    <div class="offer-details-block">
                                        <div class="offer-details-label" test="<?php echo $detail['label']; ?>"><?php pll_e($detail['label']); ?>:</div>
                                        <div class="offer-details-content">
                                    <?php
                                    echo $detail['data'];
                                    echo '</div></div>';
                                }
                                echo '</div></div>';
                            }
                        ?>
                    </div>

                    <div class="offer-price offer-price-object">
                         <?php if ($object->transactions->transaction->price){ ?>
                            <div class="offer-price-amount offer-price-amount-object active js-offer-price-amount">

                                <?php echo trimTrailingZeroes(number_format((float)$object->transactions->transaction->price->_, 2, '.', ' ')) . ' ' . pll__('(Currency symbol)'); ?>
                                <span class="offer-price-per-area">
                                    <?php echo '(' . trimTrailingZeroes(number_format((float)$object->transactions->transaction->pricem2->_, 2, '.', ' ')) . ' ' . pll__('(Currency symbol)') . '/m²)'; ?>
                                </span>
                                <div class="offer-price-amount-button-wrap">
                                    <button class="btn offer-price-amount-button js-offer-price-button"><?php pll_e('Offer price'); ?></button>
                                </div>
                            </div>
                            <form class="offer-price-send js-offer-price-send js-contact-person-form" identifier-action="sendMailAskOffer">
                                <?php
                                    if ($object->Brokers) {
                                        $condition = array('Broker.originalId' => (int)$object->Brokers->originalId);
                                        $broker = NnaApi::getInstance()->getBrokers($condition)[0];

                                        foreach ($broker->PostIds as $lang => $id) {
                                            if (pll_current_language() == $lang) {
                                                $brokerID = $id;
                                            }
                                        }
                                    }
                                ?>
                                <input name="mailForm[brokerID]" value="<?php echo $brokerID; ?>" hidden>
                                <input name="mailForm[objectID]" value="<?php echo $object->originalId; ?>" hidden>
                                <input name="mailForm[address]" value="<?php echo NnaApi::getInstance()->getAddress($object); ?>" hidden>
                                <input name="mailForm[url]" value="<?php echo get_permalink(); ?>" hidden>
                                <span class="offer-price-field-wrap js-field">
                                    <input name="mailForm[price]" class="js-input js-required offer-price-send-input offer-price-send-input-sm" value="<?php echo $object->transactions->transaction->price->_; ?>">
                                    <span class='offer-price-field-error js-error'></span>
                                </span>
                                <span class="offer-price-field-wrap offer-price-field-wrap-lg js-field">
                                    <input name="mailForm[email]" class="js-input js-required js-email offer-price-send-input offer-price-send-input-lg" placeholder="Sisesta enda e-posti aadress">
                                    <span class='offer-price-field-error js-error'></span>
                                </span>
                                <button class="btn offer-price-send-button js-contact-person-form-submit">
                                    <?php pll_e('Send price'); ?>
                                </button>
                            </form>
                            <div class="offer-price-sent js-form-submitted-message">
                            </div>
                        <?php } ?>
                    </div>

                    <div class="offer-content hidden-xs">
                        <?php if ($object->options) { ?>
                            <h3 class="offer-content-heading"><?php pll_e('Additional data'); ?>:</h3>
                            <div class="offer-content-text">
                                <?php
                                    $i = 0;
                                    $additionalInformation = '';
                                    if (is_array($object->options->option)) {
                                        foreach ($object->options->option as $option) {
                                            if ($i == 0) {
                                                $additionalInformation = NnaTranslations::getInstance()->__($option->_);
                                            } else{
                                                $additionalInformation .= ', ' . strtolower(NnaTranslations::getInstance()->__($option->_));
                                            }
                                            $i++;
                                        }
                                    } else{
                                        $additionalInformation = NnaTranslations::getInstance()->__($object->options->option->_);
                                    }
                                    $additionalInformation .= '.';
                                    echo ucfirst($additionalInformation); // also echoed in mobile, so change there as well (lower down)
                                ?>
                            </div>
                        <?php } ?>

                        <?php if ( have_posts() ) : ?>
                            <h3 class="offer-content-heading"><?php pll_e('Additional data of property'); ?>:</h3>
                            <div class="offer-content-text">
                            <?php while ( have_posts() ) : the_post(); ?>
                                <?php the_content(); ?>
                            <?php endwhile; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <?php
                    $options = NnaApi::getInstance()->optionGroups();
                    $objectType = $object->objectTypes->type;
                    global $infoPageId;

                    if ($options[$objectType] == 'living') {
                        $title = get_field('residential_links_title', pll_get_post($infoPageId));
                        $links = get_field('residential_links', pll_get_post($infoPageId));
                    } elseif($options[$objectType] == 'business'){
                        $title = get_field('commercial_links_title', pll_get_post($infoPageId));
                        $links = get_field('commercial_links', pll_get_post($infoPageId));
                    } elseif($options[$objectType] == 'land'){
                        $title = get_field('land_links_title', pll_get_post($infoPageId));
                        $links = get_field('land_links', pll_get_post($infoPageId));
                    }

                    if ($links) { ?>
                        <div class="offer-links hidden-xs">
                            <div class="services offer-services">
                                <?php
                                    if ($title) { ?>
                                        <h1 class="services-secondary-heading"><?php echo $title; ?></h1>
                                    <?php
                                    }
                                ?>
                                <ul class="services-links">
                                    <?php
                                        foreach ($links as $link) {
                                            if ($link['page_directed_to'] && $link['link_name_displayed']) {
                                                echo '<li class="services-list-item"><a href="' . $link['page_directed_to'] . '" class="services-links-redirect">' . $link['link_name_displayed'] . '</a></li>';
                                            }
                                        }
                                    ?>
                                </ul>
                            </div>
                        </div>
                    <?php } ?>
                </div>

                <div class="col-sm-6 print-col-7">
                    <?php
                    $images = get_field('images');
                    if ($images) { ?>
                        <div class="offer-thumbnails hidden-xs">
                            <div class="thumbnails">
                                <?php
                                $i = 0;
                                foreach ($images as $image) {
                                    if($i==5) break;
                                    if (!isset($image) || empty($image)) continue;
                                    if ($i == 0) { //special case for first image ?>
                                        <div class="thumbnails-main">
                                            <div class="thumbnails-main-abs">
                                                <div class="offer-thumbnail-img-wrap">
                                                <img src="<?php echo $image['image']['sizes']['large']; ?>" alt="<?php echo $image['image']['alt']; ?>" class="js-image-thumbnail thumbnails-main-img" image-identifier="<?php echo $i; ?>">
                                                    <?php
                                                        $location = get_field('google_map_object', pll_get_post(get_the_ID()));
                                                        if($location) { ?>
                                                            <div class="regional-contact-map object-regional-contact-map">
                                                                <div class="locale-map object-locale-map">
                                                                    <div class="map-canvas object-map-canvas acf-map">
                                                                        <div class="marker" data-lat="<?php echo $location['lat']; ?>" data-lng="<?php echo $location['lng']; ?>"></div>
                                                                    </div>
                                                                </div>
                                                                <div class="object-locale-misc">
                                                                    <a href="http://maps.google.com/maps?q=<?php echo $location['lat'] . ',' . $location['lng']; ?>" target="_BLANK" class="main-button object-locale-button js-object-locale-button"><i class="object-locale-icon"></i></a>
                                                                    <button class="main-button object-locale-close js-object-locale-close"><?php pll_e('Close'); ?></button>
                                                                </div>
                                                            </div>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php
                                    } elseif ($i == 1){ //create containing div for rest of the images but leaves it open
                                        echo '<div class="thumbnails-block">';?>
                                            <div class="thumbnails-block-item"  data-src="<?php echo $image['image']['sizes']['large']; ?>">
                                                <div class="thumbnails-block-item-wrap">
                                                    <img src="<?php echo $image['image']['sizes']['large']; ?>" alt="<?php echo $image['image']['alt']; ?>" class="js-image-thumbnail thumbnails-main-img thumbnails-block-img" image-identifier="<?php echo $i; ?>">
                                                </div>
                                            </div>
                                    <?php
                                    } else{ //put rest of the images in ?>
                                        <div class="thumbnails-block-item"  data-src="<?php echo $image['image']['sizes']['large']; ?>">
                                            <div class="thumbnails-block-item-wrap">
                                                <img src="<?php echo $image['image']['sizes']['large']; ?>" alt="<?php echo $image['image']['alt']; ?>" class="js-image-thumbnail thumbnails-main-img thumbnails-block-img" image-identifier="<?php echo $i; ?>">
                                            </div>
                                        </div>
                                    <?php
                                    }
                                    $i++;
                                }
                                if (count($images) > 1) { // this closes rest of the images container
                                    echo '</div>';
                                }
                                ?>
                            </div>
                        </div>
                        <div class="offer-more hidden-xs">
                            <div class="offer-more-pics js-offer-more-pics">
                                <?php
                                    echo pll__('Total images') . ' (' . count($images) . ')';
                                ?>
                            </div>
                            <div class="offer-more-map js-offer-more-map">
                                <?php pll_e('See location on map'); ?>
                            </div>
                        </div>

                        <div class="offer-images js-gallery hidden-print hidden-xs">
                            <div class="js-close lightbox">
                            </div>
                            <div class="gallery-title">
                                <h1 class="gallery-title-heading">
                                    <?php
                                    echo get_the_title();
                                    if ($object->Country) { ?>
                                        <div class="offer-name-extra gallery-offer-name-extra">
                                            <?php echo $object->Country . ($object->level1 ? ', ' . $object->level1 : ''); ?>
                                        </div>
                                    <?php } ?>
                                </h1>
                                <i class=""></i>
                                <span class="gallery-close-button js-close"></span>
                            </div>
                            <!--<div class="gallery js-gallery-container">-->
                            <div class="gallery">
                                <div class="gallery-images js-images">
                                    <div class="gallery-images-img-wrap">
                                    <div class="gallery-images-img js-main-image gallery-main-img">
                                        <ul class="gallery-images-misc">
                                            <li class="gallery-images-misc-item">
                                                <span class="gallery-btn gallery-btn-prev js-gallery-btn-prev"></span>
                                            </li>
                                            <li class="gallery-images-misc-item">
                                                <div class="gallery-counter">
                                                    <span class="js-current-amount"></span>
                                                    /
                                                    <span class="js-total-amount"></span>
                                                </div>
                                            </li>
                                            <li class="gallery-images-misc-item">
                                                <span class="gallery-btn gallery-btn-next js-gallery-btn-next"></span>
                                            </li>
                                        </ul>
                                    </div>
                                    </div>
                                </div>
                                <div class="gallery-images-list-wrap">
                                    <div class="gallery-container js-gallery-container">
                                        <div class="gallery-thumbs-warp js-gallery-container">
                                            <button class="gallery-thumbs-scroll gallery-thumbs-scroll-right js-scroll-right">
                                                <i class="fa fa-angle-right"></i>
                                            </button>
                                            <button class="gallery-thumbs-scroll gallery-thumbs-scroll-left js-scroll-left">
                                                <i class="fa fa-angle-left"></i>
                                            </button>
                                        </div>
                                        <ul class="gallery-images-list js-images-list">
                                            <?php
                                            $i = 0;
                                            foreach ($images as $imageItem){
                                                $image = $imageItem['image'];
                                                if (!isset($image) || empty($image)) continue;
                                                // print_r($image);
                                                echo '<li class="gallery-images-item-wrap js-image-wrap">';
                                                echo '<div class="gallery-images-item-wrap-abs">';
                                                echo '<div class="gallery-images-item js-image" style="background-image: url(' . $image['url'] . ');" image-identifier="' . $i . '"></div>';
                                                echo '</div>';
                                                echo '</li>';
                                                $i++;
                                            }
                                            ?>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php } ?>


                    <?php
                    if ($images) { ?>
                    <div class="mob-slider js-mob-slider visible-xs">
                        <?php
                        $i = 0;
                        foreach ($images as $imageItem) {
                            $image = $imageItem['image'];
                            if (!isset($image) || empty($image)) continue;
                            //print_r($image);
                            ?>
                            <div class="mob-slider-item-wrap">
                                <div class="mob-slider-item">
                                    <div class="mob-slider-img-wrap">
                                        <div class="offer-thumbnail-img-wrap">
                                            <div class="offer-thumbnail-img-wrap">
                                                <img class="mob-slider-img" src="<?php echo $image['sizes']['large']; ?>" alt="<?php echo $image['image']['alt']; ?>"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>

                    <div class="mob-counter visible-xs">
                        <span class="mob-counter-label"><?php pll_e('images total'); ?></span>
                        (<span class="js-mob-current-amount"></span>
                        /
                        <span class="js-mob-total-amount"></span>)
                    </div>
                    <?php
                    }
                    $i++;
                    ?>
                    <div class="visible-xs text-center">
                        <a href="http://maps.google.com/maps?q=<?php echo $location['lat'] . ',' . $location['lng']; ?>" target="_BLANK" class="main-button object-locale-button js-object-locale-button"><?php pll_e('Location from map') ?></a>
                    </div>
                    <div class="offer-contact hidden-print hidden-xs">
                        <?php
                        if ($object->Brokers) {
                            $condition = array('Broker.originalId' => (int)$object->Brokers->originalId);
                            $broker = NnaApi::getInstance()->getBrokers($condition)[0];

                            foreach ($broker->PostIds as $lang => $id) {
                                if (pll_current_language() == $lang) {
                                    $brokerID = $id;
                                }
                            }

                            if ($brokerID) {
                                $args = array(
                                    'contactsType' => 'postID',
                                    'pageID' => $brokerID,
                                    'title' => pll__('Ask more info from broker'),
                                );
                                $contact = getContact($args);

                                $renderArgs = array(
                                    'gaCategory' => 'Objekt',
                                    'singleButtonClass' => 'hidden',
                                );

                                renderContact($contact, $renderArgs);
                            }
                        } ?>
                    </div>

                    <?php
                    if ($links) { ?>
                        <div class="offer-links visible-xs">
                            <div class="services offer-services">
                                <?php
                                    if ($title) { ?>
                                        <h1 class="services-secondary-heading"><?php echo $title; ?></h1>
                                    <?php
                                    }
                                ?>
                                <ul class="services-links">
                                    <?php
                                        foreach ($links as $link) {
                                            if ($link['page_directed_to'] && $link['link_name_displayed']) {
                                                echo '<li class="services-list-item"><a href="' . $link['page_directed_to'] . '" class="services-links-redirect">' . $link['link_name_displayed'] . '</a></li>';
                                            }
                                        }
                                    ?>
                                </ul>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>

            <div class="offer-content printable-offer-content visible-xs">
                <div class="offer-contact visible-print">
                    <?php
                    if ($object->Brokers) {
                        $condition = array('Broker.originalId' => (int)$object->Brokers->originalId);
                        $broker = NnaApi::getInstance()->getBrokers($condition)[0];

                        foreach ($broker->PostIds as $lang => $id) {
                            if (pll_current_language() == $lang) {
                                $brokerID = $id;
                            }
                        }

                        if ($brokerID) {
                            $args = array(
                                'contactsType' => 'postID',
                                'pageID' => $brokerID,
                                'title' => pll__('Ask more info from broker'),
                            );
                            $contact = getContact($args);

                            $renderArgs = array(
                                'gaCategory' => 'Objekt',
                                'singleButtonClass' => 'hidden',
                            );

                            renderContact($contact, $renderArgs);
                        }
                    } ?>
                </div>
                <div class="offer-content-group">
                    <h3 class="offer-content-heading">
                        <?php pll_e('Additional data'); ?>
                    </h3>
                    <?php if ($object->options) { ?>
                        <div class="offer-content-text">
                            <?php
                                echo $additionalInformation; // created in desktop view (bit up)
                            ?>
                        </div>
                    <?php } ?>
                </div>

                <div class="offer-content-group">
                    <div class="offer-content-heading">
                        <h3><?php pll_e('Additional data of property'); ?></h3>
                    </div>
                    <?php if ( have_posts() ) : ?>
                        <div class="offer-content-text">
                            <?php while ( have_posts() ) : the_post(); ?>
                                <?php the_content(); ?>
                            <?php endwhile; ?>
                        </div>
                    <?php endif; ?>
                        <!--
                        <div class="offer-content-text">
                            <?php if ($object->transactions->transaction->info){
                                foreach ($object->transactions->transaction->info as $info){
                                    if ($info->language == NnaApi::getInstance()->getLanguageCode(pll_current_language()) && $info->_){ ?>
                                            <?php echo $info->_; ?>
                                    <?php
                                    }
                                }
                            }
                            ?>
                        </div>
                        -->
                </div>
            </div>

            <div class="offer-contact visible-xs">
                <?php
                if ($object->Brokers) {
                    $condition = array('Broker.originalId' => (int)$object->Brokers->originalId);
                    $broker = NnaApi::getInstance()->getBrokers($condition)[0];

                    foreach ($broker->PostIds as $lang => $id) {
                        if (pll_current_language() == $lang) {
                            $brokerID = $id;
                        }
                    }

                    if ($brokerID) {
                        $args = array(
                            'contactsType' => 'postID',
                            'pageID' => $brokerID,
                            'title' => pll__('Ask more info from broker'),
                        );
                        $contact = getContact($args);

                        $renderArgs = array(
                            'gaCategory' => 'Objekt',
                            'singleButtonClass' => 'hidden',
                        );
                        renderContact($contact, $renderArgs);
                    }
                } ?>
                <div class="yoloswag" style="display:none;">
                <?php
                    var_dump($brokerID);
                ?>
                </div>
            </div>

            <div class="printable-thumbnails">
                <?php
                $i = 0;
                foreach ($images as $image) {
                    if (!isset($image['image']) || empty($image['image'])) continue;

                    if ($i % 3 == 0) {
                        echo '<div class="thumbnails-row">';
                    }
                    ?>
                    <div class="thumbnails-block-item printable-thumbnails-block-item"  data-src="<?php echo $image['image']['sizes']['large']; ?>">
                        <div class="thumbnails-block-item-wrap">
                            <img src="<?php echo $image['image']['sizes']['large']; ?>" alt="<?php echo $image['image']['alt']; ?>" class="js-image-thumbnail thumbnails-main-img thumbnails-block-img" image-identifier="<?php echo $i; ?>">
                        </div>
                    </div>
                <?php
                    if ($i % 3 == 2) {
                        echo '</div>';
                    }
                    $i++;
                }
                if ($i % 3 != 0) {
                    echo '</div>';
                }
                ?>
            </div>
        </div>
    </div>

    <?php get_template_part('blocks/b_mob_social_icons'); ?>

    <?php get_template_part('blocks/b_footer'); ?>

    <?php get_template_part('blocks/b_javascripts'); ?>

</body>
</html>
