<?php
/**
 * Template name: Page with newsletter
 */
?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>


    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-default">
    <?php get_template_part('blocks/b_header'); ?>
    <div class="container">
        <div class="content-divider">
            <div class="row">
                <div class="col-sm-4 col-md-3 hidden-xs">
                    <?php get_template_part('blocks/b_sidebar_primary'); ?>
                </div>
                <div class="col-sm-8 col-md-9 print-col-12 padding-left">

                    <?php get_template_part('blocks/b_breadcrumbs'); ?>

                    <?php
                        global $infoPageId;
                        $image = get_field('default_banner_image', pll_get_post($infoPageId));
                        $text = get_field('default_banner_text', pll_get_post($infoPageId));
                    ?>
                    <?php if ($image && $text): ?>
                        <!-- <div class="top-intro hidden-xs">
                            <div class="top-intro-thumbnail">
                                <?php echo '<img src="' . $image["sizes"]["custom-banner-image"] . '" alt="' . $image["alt"] . '" class="top-intro-img">'; ?>
                            </div>
                            <h2 class="top-intro-heading">
                                <?php echo $text; ?>
                            </h2>
                        </div> -->
                    <?php endif ?>

                    <h1 class="primary-heading"><?php echo get_the_title(); ?></h1>
                    <div class="flex justify-center">
                      <div class="col-md-7 no-gutter">
                          <div class="subscribe">
                              <form class="js-subscribe-newsletter subscribe-join">
                                  <div class="subscribe-success">
                                      <span class="subscribe-success-text">
                                          <?php pll_e('Successfully joined newsletter'); ?>
                                      </span>
                                  </div>
                                  <h6 class="subscribe-title">
                                      <?php the_field('newsletter_subscription'); ?>
                                  </h6>
                                  <span style="margin-bottom: 0px;" class="subscribe-info">
                                      <?php pll_e('Campaign newsletter text'); ?>
                                    
                                  </span>
                                  <?php
                                      $langs = array(
                                          'et' => 'Estonian',
                                          'en' => 'English',
                                          'ru' => 'Russian',
                                      );
                                  ?>
                                  <input class="js-field-language hidden" value="<?php echo $langs[pll_current_language()]; ?>">
                                  <input style="max-width:70%;" class="js-field-email subscribe-join-input" placeholder="<?php pll_e('Insert email address'); ?>">
                                  <button style="margin-left: 10px;" type="submit" class="main-button main-button-larger"><?php pll_e('Join'); ?></button>

                                      <div class="subscribe-privacy">
                                          <input type="checkbox" name="checkbox" value="check" class="privacy-checkbox js-privacy-checkbox" />

                                          <a href="/privaatsuspoliitika/" target="_blank"><?php pll_e('Newsletter privacy agreement'); ?></a>
                                      </div>
                                      <div class="subscribe-privacy-error">
                                          <span class="subscribe-error-text"><?php pll_e('Newsletter privacy agreement error'); ?></span>
                                      </div>
                                      <div class="subscribe-email-error">
                                          <span class="subscribe-error-text"><?php pll_e('Newsletter field error'); ?></span>
                                      </div>


                              </form>
                          </div>
                      </div>
                    </div>

                    <div class="col-md-12 user-added-content no-gutter" style="padding-top: 40px; border-top:1px solid #333;">
                        <?php
                            while ( have_posts() ) : the_post();
                            the_content();
                            endwhile;?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php get_template_part('blocks/b_mob_social_icons'); ?>

    <?php get_template_part('blocks/b_footer'); ?>

    <?php get_template_part('blocks/b_javascripts'); ?>

</body>
</html>
