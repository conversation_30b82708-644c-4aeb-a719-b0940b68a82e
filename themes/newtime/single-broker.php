<?php
/**
 * The Template for displaying all single team members
 */

$contact = getContact([
    'contactsType' => 'postID',
    'pageID' => get_the_ID(),
]);
?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>
    

    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-broker">

    <?php get_template_part('blocks/b_header'); ?>

    <div class="container">
        <div class="content-divider">
            <div class="row">
                <div class="col-sm-4 col-md-3 hidden-xs">

                    <?php get_template_part('blocks/b_sidebar_primary'); ?>
                    
                </div>
                <div class="col-sm-8 col-md-9 print-col-12 padding-left">

                    <?php get_template_part('blocks/b_breadcrumbs'); ?>
                    <?php $broker = get_fields(); ?>
                    <div class="brokers">
                        <div class="brokers-header">
                            <h1 class="brokers-header-heading"><?php echo get_the_title(); ?></h1>
                            <?php 
                                if ($broker['occupation']) {
                                    echo '<div class="brokers-header-title">' . $broker['occupation'] . '</div>';
                                }
                            ?>
                            <div class="main-button brokers-header-button js-back"><?php pll_e('Close'); ?></div>
                        </div>
                        <div class="brokers-employee">
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="brokers-employee-picture">
                                        <?php $image = wp_get_attachment_image_src(get_post_thumbnail_id(), 'large'); ?>
                                        <img src="<?php echo $image[0]; ?>" class="brokers-employee-img">
                                    </div>
                                    <?php if ($broker['languages']): ?>
                                        <div class="brokers-employee-languages">
                                            <div class="flags">
                                                <ul class="flags-list">
                                                    <?php foreach ($broker['languages'] as $language): ?>
                                                        <li class="flag-item flag-<?php echo $language; ?>"></li> 
                                                    <?php endforeach ?>
                                                </ul>
                                            </div>
                                        </div>
                                    <?php endif ?>
                                    <ul class="brokers-employee-info">
                                        <?php $locations = wp_get_post_terms( get_the_ID(), 'active_location'); ?>
                                        <?php if ($locations){
                                            $locationsList = '';
                                            // add label
                                            $locationsList .= pll__('Area') . ': ';
                                            
                                            foreach ($locations as $key => $val) {
                                                if ($key == 0) {
                                                    // add just name for first item
                                                    $locationsList .= $val->name;
                                                } else{
                                                    // prepend coma for rest
                                                    $locationsList .= ', ' . $val->name;
                                                }
                                            }
                                            echo '<li class="brokers-employee-info-item">' . $locationsList . '</li>';
                                        } ?>

                                        <?php if ($broker['phone_number']): ?>
                                            <li class="brokers-employee-info-item">
                                                <?php pll_e('Phone'); ?>:
                                                <a class="brokers-employee-info-link" href="tel:<?php echo $broker['phone_number']; ?>">
                                                    <?php echo $broker['phone_number']; ?>
                                                </a>
                                            </li>
                                        <?php endif ?>
                                        <?php if ($broker['mobile_number']): ?>
                                            <li class="brokers-employee-info-item">
                                                <?php pll_e('Mobile'); ?>:
                                                <a class="brokers-employee-info-link" href="tel:<?php echo $broker['mobile_number']; ?>">
                                                    <?php echo $broker['mobile_number']; ?>
                                                </a>
                                            </li>
                                        <?php endif ?>
                                        <?php if ($broker['email']): ?>
                                            <li class="brokers-employee-info-item"><?php pll_e('Email'); ?>: <a class="brokers-employee-info-link" href="mailto:<?php echo $broker['email']; ?>"><?php echo $broker['email']; ?></a></li>
                                        <?php endif ?>
                                        <li class="brokers-employee-info-item">
                                            <div class="js-contact-links contact-links">
                                                <div class="make-contact js-make-contact">
                                                    <div class="make-contact-lightbox js-close-make-contact">
                                                    </div>
                                                    <div class="make-contact-box mailto">
                                                        <div class="mailto-heading">
                                                            <div class="gallery-close-button mailto-close-button js-close-make-contact"></div>
                                                            <h4 class="mailto-heading-header"><?php pll_e('Contact us') ?></h4>
                                                        </div>
                                                        <div class="mailto-content clearfix">
                                                            <div class="mailto-content-contact">
                                                                <div class="contact">
                                                                    <div class="contact-thumbnail contact-thumbnail-block mailto-contact-thumbnail">
                                                                        <?php if ($contact['thumbnail']): ?>
                                                                            <img src="<?php echo $contact['thumbnail']['sizes']['medium']; ?>" alt="<?php echo $contact['thumbnail']['alt']; ?>" class="contact-thumbnail-img">
                                                                        <?php else: ?>
                                                                            <img src="<?php echo get_template_directory_uri(); ?>/assets/img/no_pic_placeholder.png" alt="No image available" class="contact-thumbnail-img">
                                                                        <?php endif; ?>
                                                                    </div>
                                                                    <div class="contact-info contact-info-block">
                                                                        <?php
                                                                            if ($contact['name']) {
                                                                                echo '<div class="contact-info-name">' . $contact['name'] . '</div>';
                                                                            }

                                                                            if ($contact['occupation']){
                                                                                echo '<div class="contact-info-title">' . $contact['occupation'] . '</div>';
                                                                            }

                                                                            if ($contact['languages']){
                                                                                echo '<div class="contact-languages">';
                                                                                echo '<div class="flags">';
                                                                                echo '<ul class="flags-list">';
                                                                                foreach ($contact['languages'] as $lang) {
                                                                                    echo '<li class="flag-item flag-' . $lang . '"></li>';
                                                                                }
                                                                                echo '</ul>';
                                                                                echo '</div>';
                                                                                echo '</div>';
                                                                            }
                                                                        ?>
                                                                        <ul class="contact-details">
                                                                            <?php if ($contact['phone']): ?>
                                                                                <li class="contact-details-item"><a class="contact-details-item-link" href="tel:<?php echo $contact['phone']; ?>"> <!-- <?php pll_e('Phone'); ?>: --><?php echo $contact['phone'] ?><a></li>
                                                                            <?php endif; ?>
                                                                            <?php if ($contact['mobile']): ?>
                                                                                <li class="contact-details-item"><a class="contact-details-item-link" href="tel:<?php echo $contact['mobile']; ?>"><!-- <?php pll_e('Mobile'); ?>: --><?php echo $contact['mobile'] ?></a></li>
                                                                            <?php endif; ?>
                                                                            <?php if ($contact['email']): ?>
                                                                                <li class="contact-details-item"><!-- <?php pll_e('Email'); ?>: --><a class="contact-details-item-link" href="mailto:<?php echo $contact['email']; ?>"><?php echo $contact['email'] ?></a></li>
                                                                            <?php endif; ?>
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <?php
                                                                $gaIdentifier = '';
                                                                $curUrl = get_the_permalink();
                                                                $siteUrl = site_url();
                                                                $gaIdentifier = str_replace($siteUrl, '', $curUrl);
                                                            ?>
                                                            <form class="ga-contact-form js-contact-person-form mailto-content-form" identifier-action="sendMailTakeContact" ga-identifier="<?php echo $gaIdentifier; ?>" ga-category="Maakleeri-leht">
                                                                <div class="js-form-submitted-message mailto-form-after-submit-message"></div>
                                                                <div class="mailto-form-group js-field">
                                                                    <input type="hidden" name="mailForm[url]" value="<?php echo $curUrl;?>">
                                                                    <input name="mailForm[id]" type="text" value="<?php echo $contact['contactID']; ?>" hidden>
                                                                    <label class="mailto-label">
                                                                        <?php pll_e('Name'); ?>:
                                                                    </label>
                                                                    <input name="mailForm[name]" type="text" class="js-input js-required mailto-form-input">
                                                                    <span class='js-error'></span>
                                                                </div>
                                                                <div class="mailto-form-group mailto-form-group-last js-field">
                                                                    <label class="mailto-label">
                                                                        <?php pll_e('Email'); ?>:
                                                                    </label>
                                                                    <input name="mailForm[email]" type="text" class="js-input js-required js-email mailto-form-input">
                                                                    <span class='js-error'></span>
                                                                </div>
                                                                <div class="clearfix"></div>

                                                                <div class="mailto-form-group-full js-field">
                                                                    <label class="mailto-label">
                                                                        <?php pll_e('Message content'); ?>:
                                                                    </label>
                                                                    <textarea name="mailForm[message]" class="js-input js-required mailto-form-textarea"></textarea>
                                                                    <span class='js-error'></span>
                                                                </div>
                                                                <button class="js-contact-person-form-submit btn main-button mailto-button" type="button"><?php pll_e('Send message'); ?></button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>

                                                <button class="btn main-button contact-links-button js-make-contact-button">
                                                    <?php pll_e('Take contact') ?>
                                                </button>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-sm-8">
                                    <div class="user-added-content">
                                        <?php
                                            while ( have_posts() ) : the_post();
                                            the_content();
                                            endwhile;
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                            $brokerPortfolio = array();

                            if ($broker['certificates_editor']) {
                                array_push(
                                    $brokerPortfolio,
                                    array(
                                        'label' => pll__('Certificates'),
                                        'value' => $broker['certificates_editor'],
                                    )
                                );
                            }
                            if ($broker['articles_editor']) {
                                array_push(
                                    $brokerPortfolio,
                                    array(
                                        'label' => pll__('Articles'),
                                        'value' => $broker['articles_editor'],
                                    )
                                );
                            }
                            if ($broker['projects_editor']) {
                                array_push(
                                    $brokerPortfolio,
                                    array(
                                        'label' => pll__('Projects'),
                                        'value' => $broker['projects_editor'],
                                    )
                                );
                            }
                            if ($broker['success_stories_editor']) {
                                array_push(
                                    $brokerPortfolio,
                                    array(
                                        'label' => pll__('Success stories'),
                                        'value' => $broker['success_stories_editor'],
                                    )
                                );
                            }
                            if ($broker['awards_editor']) {
                                array_push(
                                    $brokerPortfolio,
                                    array(
                                        'label' => pll__('Awards'),
                                        'value' => $broker['awards_editor'],
                                    )
                                );
                            }

                            if ($brokerPortfolio) { ?>
                                <div class="brokers-portfolio">
                                    <div class="brokers-portfolio-tabs">
                                        <ul class="tabs clearfix">
                                            <?php
                                                $activeIndex = -1;
                                                $reorderedPortfolio = array();
                                                
                                                // First, find the "kliendi tagasiside" tab and add it first
                                                foreach ($brokerPortfolio as $i => $brokerPortfolioItem) {
                                                    if (strpos(strtolower($brokerPortfolioItem['label']), 'kliendi tagasiside') !== false) {
                                                        $reorderedPortfolio[] = $brokerPortfolioItem;
                                                        $activeIndex = count($reorderedPortfolio) - 1;
                                                    }
                                                }
                                                
                                                // Then add all other tabs
                                                foreach ($brokerPortfolio as $i => $brokerPortfolioItem) {
                                                    if (strpos(strtolower($brokerPortfolioItem['label']), 'kliendi tagasiside') === false) {
                                                        $reorderedPortfolio[] = $brokerPortfolioItem;
                                                    }
                                                }
                                                
                                                // If no "kliendi tagasiside" tab was found, use the original order
                                                if ($activeIndex === -1) {
                                                    $reorderedPortfolio = $brokerPortfolio;
                                                    $activeIndex = 0;
                                                }
                                                
                                                // Display the reordered tabs
                                                foreach ($reorderedPortfolio as $i => $brokerPortfolioItem) {
                                                    echo '<li class="tabs-item ' . ($i == $activeIndex ? 'active' : '') . '" link-identifier="' . $i . '">' . $brokerPortfolioItem['label'] . '</li>';
                                                }
                                            ?>
                                        </ul>
                                    </div>

                                    <?php
                                        // Display the reordered content
                                        foreach ($reorderedPortfolio as $i => $brokerPortfolioItem) { ?>
                                            <div class="brokers-portfolio-tab <?php echo ($i == $activeIndex ? 'active' : ''); ?>" content-indentifier="<?php echo $i; ?>">
                                                <div class="user-added-content">
                                                    <?php echo $brokerPortfolioItem['value']; ?>
                                                </div>
                                            </div>
                                        <?php
                                        }
                                    ?>
                                </div>
                            <?php
                            }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    

    <?php get_template_part('blocks/b_mob_social_icons'); ?>

    <?php get_template_part('blocks/b_footer'); ?> 

    <?php get_template_part('blocks/b_javascripts'); ?> 

</body>
</html>
