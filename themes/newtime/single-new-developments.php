<?php
/**
 * The Template for displaying a single object
 */
?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>


    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-offer">
    <?php
    $postIdByTemplateArgs = [
        'post_type' => 'page',
        'fields' => 'ids',
        'nopaging' => true,
        'meta_key' => '_wp_page_template',
        'meta_value' => 'page-templates/new_developments.php'
    ];
    $postIdByTemplate = array_values(get_posts($postIdByTemplateArgs))[0];

    global $wp_query;
    $save_query = $wp_query->queried_object_id;
    $wp_query->queried_object_id = $postIdByTemplate;

    get_template_part('blocks/b_header');

    $wp_query->queried_object_id = $save_query;
    ?>

    <div class="container">
    <?php
        $fields = get_fields();
    ?>

        <div class="offer">
            <div class="offer-header hidden-xs">
                <h1 class="main-header dev-main-heading"><?php echo get_the_title(); ?></h1>
                <div class="js-back main-button dev-header-button">
                    <?php pll_e('Back'); ?>
                </div>
            </div>
            <div class="row">

                <div class="col-sm-6">
                    <div class="offer-details">
                        <div class="offer-details-block dev-offer-details-block">
                            <div class="offer-details-label"><?php pll_e('Added at'); ?>:</div>
                            <div class="offer-details-content"><?php echo get_the_date('d.m.Y'); ?></div>
                        </div>

                        <?php
                        if ($fields['general_information']) {
                            foreach ($fields['general_information'] as $row) {
                                if ($row['general_information_label'] && $row['general_information_data']) { ?>
                                    <div class="offer-details-block dev-offer-details-block">
                                        <div class="offer-details-label"><?php echo $row['general_information_label']; ?></div>
                                        <div class="offer-details-content"><?php echo $row['general_information_data']; ?></div>
                                    </div>
                                <?php
                                }
                            }
                        }
                        $finishedBy = get_field('finished_by');
                        if ($finishedBy) { ?>
                        <div class="offer-details-block dev-offer-details-block">
                            <div class="offer-details-label"><?php pll_e('Finished by'); ?>: </div>
                            <div class="offer-details-content"><?php echo $finishedBy; ?></div>
                        </div>
                        <?php
                        }
                        ?>

                        <div class="offer-price visible-xs">
                            <div class="offer-price-amount">
                                <?php echo $fields['price']; ?>
                            </div>
                        </div>
                    </div>

                    <?php
                    $images = $fields['product_images'];
                    if ($images) { ?>
                    <div class="mob-slider js-mob-slider visible-xs">
                        <?php
                        foreach ($images as $image) { ?>
                        <div class="mob-slider-item-wrap">
                            <div class="mob-slider-item">
                                <div class="mob-slider-img-wrap">
                                    <div class="offer-thumbnail-img-wrap">
                                        <img class="mob-slider-img" src="<?php echo $image['image']['sizes']['large']; ?>" alt="<?php echo $image['image']['alt']; ?>"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php } ?>
                    </div>
                    <?php
                    }
                    ?>

                    <div class="visible-xs text-center">
                        <a href="http://maps.google.com/maps?q=<?php echo $location['lat'] . ',' . $location['lng']; ?>" target="_BLANK" class="main-button object-locale-button js-object-locale-button"><?php pll_e('Location from map') ?></a>
                    </div>

                    <div class="offer-price hidden-xs">
                        <div class="offer-price-amount">
                            <?php echo $fields['price']; ?>
                        </div>
                    </div>

                    <div class="offer-content hidden-xs">
                        <?php if ($fields['new_development_display_link'] && $fields['new_development_link']) { ?>
                            <a href="<?php echo $fields['new_development_display_link']; ?>" class="offer-content-link">
                                <?php echo $fields['new_development_link']; ?>
                            </a>
                        <?php } ?>

                        <div class="offer-content-text">
                            <div class="user-added-content">
                                <?php
                                    while ( have_posts() ) : the_post();
                                    the_content();
                                    endwhile;
                                ?>
                            </div>
                        </div>
                    </div>

                    <?php
                    $objectType = get_field('object_type');

                    global $infoPageId;
                    if ($objectType == 'living') {
                        $title = get_field('residential_links_title', pll_get_post($infoPageId));
                        $links = get_field('residential_links', pll_get_post($infoPageId));
                    } elseif($objectType == 'business'){
                        $title = get_field('commercial_links_title', pll_get_post($infoPageId));
                        $links = get_field('commercial_links', pll_get_post($infoPageId));
                    } elseif($objectType == 'land'){
                        $title = get_field('land_links_title', pll_get_post($infoPageId));
                        $links = get_field('land_links', pll_get_post($infoPageId));
                    }

                    if ($links) { ?>
                        <div class="offer-links hidden-xs">
                            <div class="services offer-services">
                                <?php
                                    if ($title) { ?>
                                        <h1 class="services-secondary-heading"><?php echo $title; ?></h1>
                                    <?php
                                    }
                                ?>
                                <ul class="services-links">
                                    <?php
                                        foreach ($links as $link) {
                                            if ($link['page_directed_to'] && $link['link_name_displayed']) {
                                                echo '<li class="services-list-item"><a href="' . $link['page_directed_to'] . '" class="services-links-redirect">' . $link['link_name_displayed'] . '</a></li>';
                                            }
                                        }
                                    ?>
                                </ul>
                            </div>
                        </div>
                    <?php } ?>


                </div>

                <div class="col-sm-6">

                    <?php
                    $images = $fields['product_images'];
                    if ($images) { ?>
                        <div class="offer-thumbnails hidden-xs">
                            <div class="thumbnails">
                                <?php
                                $i = 0;
                                foreach ($images as $image) {
                                    if($i==5) break;
                                    if ($i == 0) { //special case for first image ?>
                                        <div class="thumbnails-main">
                                            <div class="thumbnails-main-abs">
                                                <img src="<?php echo $image['image']['sizes']['large']; ?>" alt="<?php echo $image['image']['alt']; ?>" class="js-image-thumbnail thumbnails-main-img" image-identifier="<?php echo $i; ?>">
                                                <?php
                                                    $location = get_field('google_map_object');
                                                    if($location) { ?>
                                                        <div class="regional-contact-map object-regional-contact-map">
                                                            <div class="locale-map object-locale-map">
                                                                <div class="map-canvas object-map-canvas acf-map">
                                                                    <div class="marker" data-lat="<?php echo $location['lat']; ?>" data-lng="<?php echo $location['lng']; ?>"></div>
                                                                </div>
                                                            </div>
                                                            <div class="object-locale-misc">
                                                                <a href="http://maps.google.com/maps?q=<?php echo $location['lat'] . ',' . $location['lng']; ?>" target="_BLANK" class="main-button object-locale-button js-object-locale-button"><i class="fa fa-search"></i></a>
                                                                <button class="main-button object-locale-close"><?php pll_e('Close'); ?></button>
                                                            </div>
                                                        </div>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    <?php
                                    } elseif ($i == 1){ //create containing div for rest of the images but leaves it open
                                        echo '<div class="thumbnails-block">';?>
                                            <div class="thumbnails-block-item"  data-src="<?php echo $image['image']['sizes']['large']; ?>">
                                                <div class="thumbnails-block-item-wrap">
                                                    <img src="<?php echo $image['image']['sizes']['large']; ?>" alt="<?php echo $image['image']['alt']; ?>" class="js-image-thumbnail thumbnails-main-img thumbnails-block-img" image-identifier="<?php echo $i; ?>">
                                                </div>
                                            </div>
                                    <?php
                                    } else{ //put rest of the images in ?>
                                        <div class="thumbnails-block-item"  data-src="<?php echo $image['image']['sizes']['large']; ?>">
                                            <div class="thumbnails-block-item-wrap">
                                                <img src="<?php echo $image['image']['sizes']['large']; ?>" alt="<?php echo $image['image']['alt']; ?>" class="js-image-thumbnail thumbnails-main-img thumbnails-block-img" image-identifier="<?php echo $i; ?>">
                                            </div>
                                        </div>
                                    <?php
                                    }
                                    $i++;
                                }
                                if (count($images) > 1) { // this closes rest of the images container
                                    echo '</div>';
                                }
                                ?>
                            </div>
                        </div>
                        <div class="offer-more hidden-xs">
                            <div class="offer-more-pics js-offer-more-pics">
                                <?php
                                    echo pll__('Total images') . ' (' . count($images) . ')';
                                ?>
                            </div>
                            <div class="offer-more-map js-offer-more-map">
                                <?php pll_e('See location on map'); ?>
                            </div>
                        </div>

                        <div class="offer-images js-gallery hidden-xs">
                            <div class="js-close lightbox">
                            </div>
                            <div class="gallery-title">
                                <h1 class="gallery-title-heading">
                                    <?php
                                    echo get_the_title();
                                    if ($object->Country) { ?>
                                        <div class="offer-name-extra gallery-offer-name-extra">
                                            <?php echo $object->Country . ($object->level1 ? ', ' . $object->level1 : ''); ?>
                                        </div>
                                    <?php } ?>
                                </h1>
                                <i class=""></i>
                                <span class="gallery-close-button js-close"></span>
                            </div>
                            <!--<div class="gallery js-gallery-container">-->
                            <div class="gallery">
                                <div class="gallery-images js-images">
                                    <div class="gallery-images-img-wrap">
                                    <div class="gallery-images-img js-main-image gallery-main-img">
                                        <ul class="gallery-images-misc">
                                            <li class="gallery-images-misc-item">
                                                <span class="gallery-btn gallery-btn-prev js-gallery-btn-prev"></span>
                                            </li>
                                            <li class="gallery-images-misc-item">
                                                <div class="gallery-counter">
                                                    <span class="js-current-amount"></span>
                                                    /
                                                    <span class="js-total-amount"></span>
                                                </div>
                                            </li>
                                            <li class="gallery-images-misc-item">
                                                <span class="gallery-btn gallery-btn-next js-gallery-btn-next"></span>
                                            </li>
                                        </ul>
                                    </div>
                                    </div>
                                </div>
                                <div class="gallery-images-list-wrap">
                                    <div class="gallery-container js-gallery-container">
                                        <div class="gallery-thumbs-warp js-gallery-container">
                                            <button class="gallery-thumbs-scroll gallery-thumbs-scroll-right js-scroll-right">
                                                <i class="fa fa-angle-right"></i>
                                            </button>
                                            <button class="gallery-thumbs-scroll gallery-thumbs-scroll-left js-scroll-left">
                                                <i class="fa fa-angle-left"></i>
                                            </button>
                                        </div>
                                        <ul class="gallery-images-list js-images-list">
                                            <?php
                                            $i = 0;
                                            foreach ($images as $imageItem){
                                                $image = $imageItem['image'];
                                                // print_r($image);
                                                echo '<li class="gallery-images-item-wrap js-image-wrap">';
                                                echo '<div class="gallery-images-item-wrap-abs">';
                                                echo '<div class="gallery-images-item js-image" style="background-image: url(' . $image['url'] . ');" image-identifier="' . $i . '"></div>';
                                                echo '</div>';
                                                echo '</li>';
                                                $i++;
                                            }
                                            ?>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php } ?>

                    <div class="offer-content visible-xs">
                        <div class="offer-content-text">
                            <div class="user-added-content">
                                <?php
                                    while ( have_posts() ) : the_post();
                                    the_content();
                                    endwhile;
                                ?>
                            </div>
                        </div>
                    </div>

                    <?php
                        if ($fields['related_contact']) { ?>
                            <div class="offer-contact">
                                <?php $args = array(
                                    'contactsType' => 'field',
                                    'customFieldsName' => 'related_contact',
                                    'pageID' => get_the_ID(),
                                    'title' => pll__('Ask more info from broker'),
                                );
                                $contact = getContact($args);

                                $renderArgs = array(
                                    'gaCategory' => 'Uusarendus',
                                    'singleButtonClass' => 'hidden',
                                );
                                renderContact($contact, $renderArgs); ?>
                            </div>
                        <?php
                        }
                    ?>
                    <?php
                        if ($links) { ?>
                        <div class="offer-links visible-xs">
                            <div class="services offer-services">
                                <?php
                                    if ($title) { ?>
                                        <h1 class="services-secondary-heading"><?php echo $title; ?></h1>
                                    <?php
                                    }
                                ?>
                                <ul class="services-links">
                                    <?php
                                        foreach ($links as $link) {
                                            if ($link['page_directed_to'] && $link['link_name_displayed']) {
                                                echo '<li class="services-list-item"><a href="' . $link['page_directed_to'] . '" class="services-links-redirect">' . $link['link_name_displayed'] . '</a></li>';
                                            }
                                        }
                                    ?>
                                </ul>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>


    <?php get_template_part('blocks/b_mob_social_icons'); ?>

    <?php get_template_part('blocks/b_footer'); ?>

    <?php get_template_part('blocks/b_javascripts'); ?>

</body>
</html>
