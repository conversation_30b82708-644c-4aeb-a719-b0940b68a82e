<?php
class NnaAjax
{
    private static $instance;

    private function __construct()
    {
        add_action('wp_ajax_nopriv_nnaCaller', array($this, 'nnaCaller'));
        add_action('wp_ajax_nopriv_nnaGetObjects', array($this, 'getObjects'));
        add_action('wp_ajax_nopriv_nnaGetFavourites', array($this, 'getFavouritedObjects'));
        add_action('wp_ajax_nopriv_nnaSetFavourites', array($this, 'setFavouritedObject'));
        add_action('wp_ajax_nopriv_nnaRemoveFavourites', array($this, 'removeFavouritedObject'));
        add_action('wp_ajax_nopriv_sendMailTakeContact', array($this, 'sendMailTakeContact'));
        add_action('wp_ajax_nopriv_sendMailAskOffer', array($this, 'sendMailAskOffer'));
        add_action('wp_ajax_nopriv_filterBrokersByTitle', array($this, 'filterBrokersByTitle'));
        add_action('wp_ajax_nopriv_filterNewDevsByTitle', array($this, 'filterNewDevsByTitle'));
        add_action('wp_ajax_nnaGetFavourites', array($this, 'getFavouritedObjects'));
        add_action('wp_ajax_nnaSetFavourites', array($this, 'setFavouritedObject'));
        add_action('wp_ajax_nnaRemoveFavourites', array($this, 'removeFavouritedObject'));
        add_action('wp_ajax_nnaGetObjects', array($this, 'getObjects'));
        add_action('wp_ajax_nnaSyncObjects', array($this, 'syncObjects'));
        add_action('wp_ajax_nnaSyncBrenollisObjects', array($this, 'syncBrenollisObjects'));
        add_action('wp_ajax_nnaFastSyncBrenollisObjects', array($this, 'fastSyncBrenollisObjects'));
        add_action('wp_ajax_nnaCreateMissingPosts', array($this, 'createMissingPosts'));
        add_action('wp_ajax_nnaSyncUsers', array($this, 'syncUsers'));
        add_action('wp_ajax_sendMailTakeContact', array($this, 'sendMailTakeContact'));
        add_action('wp_ajax_sendMailAskOffer', array($this, 'sendMailAskOffer'));
        add_action('wp_ajax_filterBrokersByTitle', array($this, 'filterBrokersByTitle'));
        add_action('wp_ajax_filterNewDevsByTitle', array($this, 'filterNewDevsByTitle'));

        add_action('wp_ajax_nopriv_sendWriteToUsForm', array($this, 'sendWriteToUsForm'));
        add_action('wp_ajax_sendWriteToUsForm', array($this, 'sendWriteToUsForm'));

        add_action('wp_ajax_nopriv_sendFormTemplateResponse', array($this, 'sendFormTemplateResponse'));
        add_action('wp_ajax_sendFormTemplateResponse', array($this, 'sendFormTemplateResponse'));
    }

    public static function getInstance()
    {
        if (null == self::$instance) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    public function allowedMethods()
    {
        return array(
            'syncUsers',
            'syncObjects',
            'syncBrenollisObjects',
        );
    }

    public function getObjects()
    {
        $ret = array();
        $options = NnaApi::getInstance()->handlePagination();
        if (isset($_GET['sort'])) {
            $sort = $_GET['sort'];
        } else {
            $sort = array('lastModified' => -1);
        }
        $options = NnaApi::getInstance()->handlePagination();
        $options['sort'] = NnaApi::getInstance()->handleSort($sort);
        // print_r($options);

        $condition = array();
        if (isset($_GET['filters'])) {
            $condition = NnaApi::getInstance()->handleObjectFilters($_GET['filters']);
        }

        if (!is_user_logged_in()) {
            $condition['Object.hidden'] = array(
                '$ne' => 1,
            );
        }

        $data = NnaApi::getInstance()->getObjects($condition, $options);
        $offset = $options['skip'];
        $ret['objects'] = NnaApi::getInstance()->fixObjects($data, true, $offset);
        echo json_encode($ret);
        wp_die();
    }

    public function syncUsers()
    {
        ScoroSync::getInstance()->syncUsers();
    }

    public function syncObjects()
    {
        // NnaApi::getInstance()->startObjectSync(false);
        ScoroSync::getInstance()->startObjectSync(false);
        wp_die();
    }

    public function syncBrenollisObjects()
    {
        error_log('AJAX: syncBrenollisObjects called at ' . date('Y-m-d H:i:s'));
        file_put_contents(NNA_PATH . '/log/synclog.log', 'AJAX: syncBrenollisObjects started at ' . date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        try {
            $result = BrenollisSync::getInstance()->startObjectSync();
            error_log('AJAX: syncBrenollisObjects completed successfully');
            file_put_contents(NNA_PATH . '/log/synclog.log', 'AJAX: syncBrenollisObjects completed successfully' . PHP_EOL, FILE_APPEND);
            echo json_encode(['status' => 'success', 'result' => $result]);
        } catch (Exception $e) {
            error_log('AJAX: syncBrenollisObjects error: ' . $e->getMessage());
            file_put_contents(NNA_PATH . '/log/synclog.log', 'AJAX: syncBrenollisObjects error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
            echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        }

        wp_die();
    }

    public function fastSyncBrenollisObjects()
    {
        error_log('AJAX: fastSyncBrenollisObjects called at ' . date('Y-m-d H:i:s'));
        file_put_contents(NNA_PATH . '/log/synclog.log', 'AJAX: fastSyncBrenollisObjects started at ' . date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        try {
            $result = BrenollisSync::getInstance()->fastSync();
            error_log('AJAX: fastSyncBrenollisObjects completed successfully');
            file_put_contents(NNA_PATH . '/log/synclog.log', 'AJAX: fastSyncBrenollisObjects completed successfully' . PHP_EOL, FILE_APPEND);
            echo json_encode(['status' => 'success', 'result' => $result]);
        } catch (Exception $e) {
            error_log('AJAX: fastSyncBrenollisObjects error: ' . $e->getMessage());
            file_put_contents(NNA_PATH . '/log/synclog.log', 'AJAX: fastSyncBrenollisObjects error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
            echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        }

        wp_die();
    }

    public function createMissingPosts()
    {
        error_log('AJAX: createMissingPosts called at ' . date('Y-m-d H:i:s'));
        file_put_contents(NNA_PATH . '/log/synclog.log', 'AJAX: createMissingPosts started at ' . date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        try {
            $result = BrenollisSync::getInstance()->createMissingWordPressPosts();
            error_log('AJAX: createMissingPosts completed successfully');
            file_put_contents(NNA_PATH . '/log/synclog.log', 'AJAX: createMissingPosts completed successfully' . PHP_EOL, FILE_APPEND);
            echo json_encode(['status' => 'success', 'result' => $result]);
        } catch (Exception $e) {
            error_log('AJAX: createMissingPosts error: ' . $e->getMessage());
            file_put_contents(NNA_PATH . '/log/synclog.log', 'AJAX: createMissingPosts error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
            echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        }

        wp_die();
    }

    public function nnaCaller()
    {
        $this->validate();
        if (isset($_POST['method']) && in_array($_POST['method'], $this->allowedMethods())) {
            call_user_func(array($this, $_POST['method']));
        }
        die();
    }

    public function isError($value, $die = true)
    {
        if ($value) {
            if ($value instanceof WP_Error) {
                if ($die) {
                    http_response_code(500);
                    print_r($value);
                    print_r($_POST);
                    die();
                }
                return false;
            }
        } else {
            if ($die) {
                http_response_code(404);
                die();
            }
            return false;
        }
        return true;
    }

    public function validate()
    {
        if (isset($_SERVER['HTTP_X_API_TOKEN'])) {
            $token = $_SERVER['HTTP_X_API_TOKEN'];
            $options = get_option('nna_connection_settings');
            if ($options['token'] == $token) {
                return;
            }
        }
        http_response_code(403);
        die();
    }

    public function getFavouritedObjects(){
        if (!session_id()) {
            session_start();
        }
        echo json_encode($_SESSION['favourites']);
        die();
    }

    public function setFavouritedObject()
    {
        $itemId = $_POST['objectId'];
        if (!session_id()) {
            session_start();
        }
        if (!isset($_SESSION['favourites'])) {
            $_SESSION['favourites'] = array($itemId);
        } elseif (!in_array($itemId, $_SESSION['favourites'])) {
            array_push($_SESSION['favourites'], $itemId);
        }

        $ret['count'] = count($_SESSION['favourites']);
        echo json_encode($ret);

        wp_die();
    }

    public function removeFavouritedObject()
    {
        $itemId = $_POST['objectId'];
        if (!session_id()) {
            session_start();
        }

        if (isset($_SESSION['favourites'])) {
            if(($key = array_search($itemId, $_SESSION['favourites'])) !== false) {
                unset($_SESSION['favourites'][$key]);
            }
        }

        $ret['count'] = count($_SESSION['favourites']);
        echo json_encode($ret);

        wp_die();
    }

    public function sendMailAskOffer()
    {
        // get mail data
        $data = array();
        parse_str($_POST['formData'], $data);
        $email = $data['mailForm']['email'];
        // set message
        $offer = $data['mailForm']['price'];
        $objectID = $data['mailForm']['objectID'];
        $objectAddress = $data['mailForm']['address'];
        $objectUrl = $data['mailForm']['url'];

// message
$message = 'Objektile (' . $objectAddress . ' - #' . $objectID . ') tuli pakkumine hinnaga ' . $offer . '.
Vaata: ' . $objectUrl;

        $recieverEmail = get_field('email', $data['mailForm']['brokerID']);

        $mail = new PHPMailer(true);
        // from
        $mail->setFrom($email); // replace with the address you want to be seen as sent from
        // to
        $mail->addAddress($recieverEmail); // replace with the e-mail address you want the message sent to
        // subject
        $mail->Subject = 'Teade Pindi Kinnisvara veebist'; // replace with the e-mail subject
        $mail->Body = $message;

        // send mail
        $templates = array(
            'success' => 'askOfferFormSuccess',
            'error' => 'askOfferFormError',
        );
        $mailingMessage = $this->sendPhpMail($mail, $templates);
        echo json_encode($mailingMessage);
        die();
    }

    public function sendMailTakeContact()
    {
        // get mail data
        $data = array();
        parse_str($_POST['formData'], $data);
        $name = $data['mailForm']['name'];
        $url = $data['mailForm']['url'];
        $email = $data['mailForm']['email'];
        // set message
        $message = $data['mailForm']['message'];
        $recieverEmail = get_field('email', $data['mailForm']['id']);
        $message = $message.' '.$url;
        $mail = new PHPMailer(true);
        // from
        $mail->setFrom($email, $name); // replace with the address you want to be seen as sent from

        // for testing
        // if (is_user_logged_in()) {
        //    $recieverEmail = '<EMAIL>';
        // }
        $mail->CharSet = 'UTF-8';

        // to
        $mail->addAddress($recieverEmail); // replace with the e-mail address you want the message sent to
        // subject
        $mail->Subject = 'Teade Pindi Kinnisvara veebist'; // replace with the e-mail subject
        $mail->Body = $message;
        // send mail
        $templates = array(
            'success' => 'takeContactFormSuccess',
            'error' => 'takeContactFormError',
        );
        $mailingMessage = $this->sendPhpMail($mail, $templates);
        echo json_encode($mailingMessage);
        die();
    }

    /**
     * Email sending for "/pindimaakler" page
     * "Write to us" form snippet
     */
    public function sendWriteToUsForm()
    {
        // Parse form data string to array
        parse_str($_POST['formData'], $data);

        // Get form fields
        $name = $data['mailForm']['name'];
        $email = $data['mailForm']['email'];
        $location = $data['mailForm']['location'];
        $phone = $data['mailForm']['phone'];

        $message = 'Nimi: ' . $name . '\n'
                 . 'Email: ' . $email . '\n'
                 . 'Asukoht: ' . $location . '\n'
                 . 'Telefon: ' . $phone;

        $admin_email = get_field('email', $data['mailForm']['id']);

        // Set up email content
        $mail = new PHPMailer(true);
        $mail->setFrom($email, $name); // From and FromName properties
        $mail->CharSet = 'UTF-8';
        $mail->Subject = 'Pindimaakler kampaania veebivorm';
        $mail->addAddress($admin_email); // Send to
        $mail->Body = $message;

        // Send email
        $mailingMessage = $this->sendPhpMail($mail, [
            'success' => 'takeContactFormSuccess',
            'error' => 'takeContactFormError',
        ]);

        // Echo response info for javascript caller
        echo json_encode($mailingMessage);
        die();
    }

    public function sendFormTemplateResponse()
    {
        $test = '';

        wp_send_json_success();
    }

    public function sendPhpMail($mailer, $templates = array()){
        // $mail->SMTPDebug = 1;                               // Enable verbose debug output

        // $mail->isSMTP();                                      // Set mailer to use SMTP
        // $mail->Host = 'smtp1.example.com';  // Specify main and backup SMTP servers
        // $mail->SMTPAuth = true;                               // Enable SMTP authentication
        // $mail->Username = '<EMAIL>';                 // SMTP username
        // $mail->Password = 'secret';                           // SMTP password
        // $mail->SMTPSecure = 'ssl';                            // Enable TLS encryption, `ssl` also accepted
        // $mail->Port = 25;                                    // TCP port to connect to

        if(!$mailer->send()) {
            return array(
                'status' => false,
                'error' => 'Mailer Error: ' . $mailer->ErrorInfo,
                'template' => $templates['error'],
            );
        }
        return array(
            'status' => true,
            'template' => $templates['success'],
        );
    }

    public function filterBrokersByTitle(){
        $data = array();
        parse_str($_POST['formData'], $data);
        // print_r($data);
        $taxonomy = 'active_location';
        $search_term = '';
        if ($data['filters']['searchQuery']) {
            $search_term = $data['filters']['searchQuery'];
        }

        $args = array(
            'post_title_like' => $search_term,

            'post_type' => 'broker',
            'post_status' => 'publish',
            'posts_per_page' => '-1',
            'tax_query' => array(
                'relation' => 'AND',
                array(
                    'taxonomy' => 'team-group',
                    'field' => 'term_id',
                    'terms' => (int)$data['filters']['catID'],
                )
            ),
            'orderby' => 'date',
            'order' => 'DESC'
        );
        if ($data['filters']['term']) {
            $args['tax_query'][] = [
                'taxonomy' => $taxonomy,
                'field' => 'term_id',
                'terms' => (int)$data['filters']['term'],
            ];
        } else {
            $args['meta_query'] = [
                'relation' => 'OR',
                [
                    'key' => 'show_on_local_grupp',
                    'compare' => 'NOT EXISTS'
                ],
                [
                    'key' => 'show_on_local_grupp',
                    'value' => '0',
                    'compare' => '='
                ]
            ];
        }

        $posts = query_posts($args);
        $posts_array = array();
        $tmpArray = array(
            'contacts' => array(),
        );
        $i = 0;

        foreach ($posts as $post) {
            $orderVal = '';
            $terms = get_the_terms($post->ID, 'display-order');
            if (isset($terms) && !empty($terms)) {
                $orderVal = $terms[0]->name;
            }
            if ($orderVal) {
                $post->displayOrder = $orderVal;
            } else{
                $post->displayOrder = '9999999';
            }
        }
        usort($posts, 'sortByVal');

        foreach ($posts as $post) {

            $args = array(
                'contactsType' => 'postID',
                'pageID' => $post->ID,
            );
            $contact = getContact($args);
            //fix lang
            $contact['languagesFixed'] = array();

            foreach ($contact['languages'] as $key => $lang) {
                $langTmp = array(
                    'lang' => $lang,
                );
                array_push($contact['languagesFixed'], $langTmp);
            }

            array_push($tmpArray['contacts'], $contact);
            if ($i % 2) {
                array_push($posts_array, $tmpArray);
                $tmpArray = array(
                    'contacts' => array(),
                );
            }
            $i++;
        }
        if ($i % 2) {
            array_push($posts_array, $tmpArray);
        }


        echo json_encode($posts_array);
        die();
    }

    public function filterNewDevsByTitle(){
        $data = array();
        $argsTerms = array();
        parse_str($_POST['formData'], $data);
        $taxonomy = 'new-developments-property-type';
        $search_term = '';
        if ($data['filters']['searchQuery']) {
            $search_term = $data['filters']['searchQuery'];
        }
        $argsDefault = array(
            'post_title_like' => $search_term,

            'post_type' => 'new-developments',
            'post_status' => 'publish',
            'posts_per_page' => '-1',
            'orderby' => 'date',
            'order' => 'DESC',
        );
        if ($data['filters']['term']) {
            $argsTerms = array(
                'tax_query' => array(
                    array(
                        'taxonomy' => $taxonomy,
                        'field' => 'term_id',
                        'terms' => (int)$data['filters']['term'],
                    ),
                ),
            );
        }
        $args = array_merge_recursive($argsDefault, $argsTerms);
        $posts = query_posts($args);
        $postsArray = array();
        foreach ($posts as $post) {
            $link = get_permalink($post->ID);
            $linkExternal = get_field('new_development_link', $post->ID);
            $linkText = get_field('new_development_display_link', $post->ID);
            $images = get_field('product_images', $post->ID);
            if ($images) {
                $img = $images[0]['image'];
            }
            $title = $post->post_title;
            $finishedBy = get_field('finished_by', $post->ID);


            $content_post = get_post($post->ID);
            $exerpt = strip_tags($content_post->post_content);

            $tmpArray = array(
                'link' => $link,
                'linkExternal' => $linkExternal,
                'linkText' => $linkText,
                'image' => $img,
                'title' => $title,
                'finishedBy' => $finishedBy,
                'content' => $exerpt,
            );
            array_push($postsArray, $tmpArray);
        }

        echo json_encode($postsArray);
        die();
    }
}

