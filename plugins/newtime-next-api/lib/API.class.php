<?php
class NnaApi
{
    private static $instance;
    protected $_manager;
    protected $_dbName;
    protected $_languages;

    public static function getInstance()
    {
        if (null == self::$instance) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    public function languageCodes()
    {
        return array(
            'et' => 'est',
            'en' => 'eng',
            'ru' => 'rus',
            'fi' => 'fin',
        );
    }

    public function getLanguageCode($code, $reversed = false)
    {
        $languages = $this->languageCodes();
        if ($reversed) {
            $languages = array_flip($languages);
        }
        if (isset($languages[$code])) {
            return $languages[$code];
        }
        return false;
    }

    public function getLanguages()
    {
        if (!$this->_languages && function_exists('pll_languages_list')) {
            $this->_languages = pll_languages_list();
        }
        return $this->_languages;
    }

    public function getBrenollisValueMap($reverse = false)
    {
        $map = array(
            // object state (alacrity)
            'CUSTOM_FIELD_OBJECT_SANITARY_FURNITURE_STATE_SANITARY_REPAIR_NEEDED' => 'requiresSanitaryRepairs',
            'CUSTOM_FIELD_OBJECT_SANITARY_FURNITURE_STATE_FOUNDATION' => 'foundation',
            'CUSTOM_FIELD_OBJECT_SANITARY_FURNITURE_STATE_NEW' => 'newBuilding',
            'CUSTOM_FIELD_OBJECT_SANITARY_FURNITURE_STATE_GOOD_CONDITION' => 'newFinishing',
            'CUSTOM_FIELD_OBJECT_SANITARY_FURNITURE_STATE_RENOVATED' => 'renovated',
            'CUSTOM_FIELD_OBJECT_SANITARY_FURNITURE_STATE_ROOFLESS_BOX' => 'inCompletionStage',
            'CUSTOM_FIELD_OBJECT_SANITARY_FURNITURE_STATE_SANITARY_REPAIR_DONE' => 'sanitaryRepairsMade',
            'CUSTOM_FIELD_OBJECT_SANITARY_FURNITURE_STATE_READY' => 'completed',
            'CUSTOM_FIELD_OBJECT_SANITARY_FURNITURE_STATE_CLOSED_BOX' => 'inCompletionStage',
            'CUSTOM_FIELD_OBJECT_SANITARY_FURNITURE_STATE_RENOVATION_NEEDED' => 'requiresRepair',
            'CUSTOM_FIELD_OBJECT_SANITARY_FURNITURE_STATE_ROOF_BOX' => 'inCompletionStage',
            // object type
            'OBJECT_TYPE_KASI_FARM_BUILDING' => 'OBJECT_TYPE_KASI_FARM_BUILDING',
            'OBJECT_TYPE_KASI_STORE' => 'commercialTrade',
            'OBJECT_TYPE_KASI_OFFICE' => 'OBJECT_TYPE_KASI_OFFICE',
            'OBJECT_TYPE_KASI_WAREHOUSE_MANUFACTURING' => 'commercialWarehouse',
            'OBJECT_TYPE_KASI_CATERING' => 'OBJECT_TYPE_KASI_CATERING',
            'OBJECT_TYPE_KASI_OTHER' => 'OBJECT_TYPE_KASI_OTHER',
            'OBJECT_TYPE_KASI_GARAGE' => 'garage',
            'OBJECT_TYPE_KASI_TRANSPORT' => 'OBJECT_TYPE_KASI_TRANSPORT',
            'OBJECT_TYPE_KASI_ACCOMMODATION' => 'commercialHostel',
            'OBJECT_TYPE_KASI_SERVICES' => 'OBJECT_TYPE_KASI_SERVICES',
            'OBJECT_TYPE_KOM_WAREHOUSE_MANUFACTURING' => 'commercialWarehouse',
            'OBJECT_TYPE_KOM_TRANSPORT' => 'OBJECT_TYPE_KOM_TRANSPORT',
            'OBJECT_TYPE_SUMMER_HOUSE' => 'cottage',
            'OBJECT_TYPE_APARTMENT' => 'apartment',
            'OBJECT_TYPE_KOM_OFFICE' => 'commercialBureau',
            'OBJECT_TYPE_UNKNOWN' => 'OBJECT_TYPE_UNKNOWN',
            'OBJECT_TYPE_KOM_BOX_ROOM' => 'OBJECT_TYPE_KOM_BOX_ROOM',
            'OBJECT_TYPE_KOM_FARM_BUILDING' => 'OBJECT_TYPE_KOM_FARM_BUILDING',
            'OBJECT_TYPE_KOM_OTHER' => 'OBJECT_TYPE_KOM_OTHER',
            'OBJECT_TYPE_KOM_SERVICES' => 'commercialService',
            'OBJECT_TYPE_TRANSPORTATION_LAND' => 'transportLand',
            'OBJECT_TYPE_SOCIAL_LAND' => 'socialLand',
            'OBJECT_TYPE_BUSINESS_LAND' => 'businessLand',
            'OBJECT_TYPE_HOUSING_LAND' => 'residentialLand',
            'OBJECT_TYPE_KOM_PARKING_SPACE' => 'OBJECT_TYPE_KOM_PARKING_SPACE',
            'OBJECT_TYPE_KOM_GARAGE' => 'garage',
            'OBJECT_TYPE_MANUFACTURING_LAND' => 'manufacturingLand',
            'OBJECT_TYPE_HOUSE' => 'house',
            'OBJECT_TYPE_KOM_CATERING' => 'commercialDining',
            'OBJECT_TYPE_KOM_STORE' => 'commercialTrade',
            'OBJECT_TYPE_KOM_ACCOMMODATION' => 'commercialHostel',
            'OBJECT_TYPE_HOUSE_PART' => 'housePart',
            'OBJECT_TYPE_APARTMENT_BUILDING' => 'OBJECT_TYPE_APARTMENT_BUILDING',
            'OBJECT_TYPE_AGRICULTURE_LAND' => 'arableLand',
            'OBJECT_TYPE_TERRACED_HOUSE' => 'rowHouse',
        );
        return $reverse ? array_flip($map) : $map;
    }

    public function optionGroups()
    {
        return array(
            'garage' => 'living',
            'apartment' => 'living',
            'house' => 'living',
            'housePart' => 'living',
            'pairHouse' => 'living',
            'pairHouseBox' => 'living',
            'rowHouse' => 'living',
            'rowHouseBox' => 'living',
            'cottage' => 'living',
            'commercialTrade' => 'business',
            'commercialWarehouse' => 'business',
            'commercialFare' => 'business',
            'commercialService' => 'business',
            'commercialBureau' => 'business',
            'commercialManufacture' => 'business',
            'commercialDining' => 'business',
            'commercialHostel' => 'business',
            'commercialClub' => 'business',
            'commercialSalon' => 'business',
            'residentialLand' => 'land',
            'arableLand' => 'land',
            'forestLand' => 'land',
            'noPurposeLand' => 'land',
            'socialLand' => 'land',
            'manufacturingLand' => 'land',
            'transportLand' => 'land',
            'businessLand' => 'land',
        );
    }

    public function filterTypes()
    {
        return array(
            'objectTypes.type' => 'string',
            'transactions.transaction.type' => 'string',
            'alacrity' => 'string',
            'level1' => 'string',
            'level2' => 'string',
            'level3' => 'string',
            'level4' => 'string',
            'level5' => 'string',
            'level6' => 'string',
            'transactions.transaction.price._' => 'float',
            'transactions.transaction.pricem2._' => 'float',
            'numberOfRooms' => 'int',
            'floorNumber' => 'int',
            'numberOfRooms' => 'int',
            'lastModified' => 'string',
            'areaSize._' => 'int',
            'keyWord' => array(
                'originalId' => 'int',
                'street._' => 'string',
                'brokerName' => 'string',
                'title.et' => 'string',
                'title.en' => 'string',
                'title.ru' => 'string',
                'transactions.transaction.info' => array(
                    '_type' => '$elemMatch',
                    'key' => '_',
                    'value' => 'string',
                ),
                'houseNumber._' => 'string',
                'fullAddress' => 'string',
            ),
        );
    }

    public function regexFields()
    {
        return array(
            'street._' => 'i',
            'transactions.transaction.info' => 'i',
            'brokerName' => 'i',
            'fullAddress' => 'i',
            'title.et' => 'i',
            'title.en' => 'i',
            'title.ru' => 'i',
        );
    }

    public function sortableFields()
    {
        return array(
            'transactions.transaction.price._' => array(
                '1' => pll__('Price ascending'),
                '-1' => pll__('Price descending'),
            ),
            'transactions.transaction.pricem2._' => array(
                '1' => pll__('Price/m2 ascending'),
                '-1' => pll__('Price/m2 descending'),
            ),
            'lastModified' => array(
                '-1' => pll__('Newest first'),
                '1' => pll__('Oldest first'),
            ),
            'areaSize' => array(
                '1' => pll__('Area ascending'),
                '-1' => pll__('Area descending'),
            ),
        );
    }

    public function getManager()
    {
        if (!$this->_manager) {
            $options = get_option(NnaSettings::$mongoSettingsName);
            $url = '';
            if (isset($options['dbUrl'])) {
                $url = $options['dbUrl'];
            }
            if (isset($options['user']) && isset($options['password'])) {
                $url = "mongodb://".urlencode($options['user']).":".urlencode($options['password'])."@".$url."/".urlencode($options['dbName'])."?authSource=pindi";
            } else {
                $url = 'mongodb://'.$url;
            }
            $this->_manager = new MongoDB\Driver\Manager($url);
        }
        return $this->_manager;
    }

    public function getCollection($name, $collection)
    {
        return new MongoDB\Collection($this->getManager(), $name, $collection);
    }

    public function getDbName()
    {
        if (!$this->_dbName) {
            $options = get_option(NnaSettings::$mongoSettingsName);
            if (!isset($options['dbName'])) {
                throw new Exception('Mongo database name not set');
            }
            $this->_dbName = $options['dbName'];
        }
        return $this->_dbName;
    }

    public function syncUsers() {

    }

    public function startObjectSync($all = false) {

    }

    public function deleteBrokers($condition = array(), $stopOnFail = true)
    {
        return $this->delete('brokers', $condition, $stopOnFail);
    }

    public function deleteObjects($condition = array(), $stopOnFail = true)
    {
        return $this->delete('objects', $condition, $stopOnFail);
    }

    public function delete($collection, $condition = array(), $stopOnFail = true)
    {
        $options = array(
            'projection' => array(
                'PostIds' => true,
            ),
            'noCursorTimeout' => true,
        );

        $collection = $this->getCollection($this->getDbName(), $collection);
        foreach ($collection->find($condition, $options) as $item) {
            foreach ($this->getLanguages() as $code) {
                if ($item->PostIds->$code) {
                    if (!wp_delete_post($item->PostIds->$code, true)) {
                        if ($stopOnFail) {
                            throw new Exception("Couldn't delete ".$collection." ".$item->PostIds->$code);
                        } else {
                            echo "Couldn't delete ".$collection." ".$item->PostIds->$code.PHP_EOL;
                        }
                    }
                }
            }
        }
        // $delete = $collection->deleteMany($condition);
        // echo 'deleted '.$delete->getDeletedCount().' records';
        return $collection->deleteMany($condition);
    }

    public function deleteWpObjects($args = array())
    {
        $args['post_type'] = NewtimeNextApi::$objectPostType;
        WpHandler::getInstance()->deleteWpPosts($args);
    }

    public function deleteWpBrokers($args = array())
    {
        $args['post_type'] = NewtimeNextApi::$brokerPostType;
        WpHandler::getInstance()->deleteWpPosts($args);
    }

    public function mongoCount($collection, $condition = array(), $options = array())
    {
        $manager = $this->getManager();
        $command = new MongoDB\Driver\Command([
            'count' => $collection,
            'query' => (object)$condition
        ]);
        
        $result = $manager->executeCommand($this->getDbName(), $command);
        $cursor = $result->toArray();
        return $cursor[0]->n;
    }

    public function getObjectsCount($condition = array(), $options = array())
    {
        return $this->mongoCount('objects', $condition, $options);
    }

    public function getMongoCursor($collection, $condition = array(), $options = array())
    {
        $manager = $this->getManager();
        $query = new MongoDB\Driver\Query($condition, $options);
        return $manager->executeQuery($this->getDbName() . '.' . $collection, $query);
    }

    public function getMongoData($collection, $condition = array(), $options = array())
    {
        return $this->getMongoCursor($collection, $condition, $options)->toArray();
    }

    public function getMongoSingle($collection, $condition = array(), $options = array())
    {
        $options['limit'] = 1;
        $result = $this->getMongoData($collection, $condition, $options);
        return !empty($result) ? $result[0] : null;
    }

    public function getObjectsCursor($condition = array(), $options = array())
    {
        return $this->getMongoCursor('objects', $condition, $options);
    }

    public function getObject($condition = array(), $options = array())
    {
        return $this->getMongoSingle('objects', $condition, $options);
    }

    public function getObjects($condition = array(), $options = array())
    {
        return $this->getMongoData('objects', $condition, $options);
    }

    public function getBrokers($condition = array(), $options = array())
    {
        return $this->getMongoData('brokers', $condition, $options);
    }

    public function setBrokers($object, $condition = array())
    {
        $collection = $this->getCollection($this->getDbName(), "objects");
        $collection->updateOne(array('_id' => $object->_id), array('$set' => array('Brokers' => $condition)));
    }

    public function setBrokerPostIds($object, $condition = array())
    {
        $collection = $this->getCollection($this->getDbName(), "brokers");
        $collection->updateOne(array('Broker.originalId' => $object->Object->Brokers->originalId), array('$set' => array('PostIds' => $condition)));
    }

    public function mongoAggregate($collection, $pipeline = array(), $options = array())
    {
        $manager = $this->getManager();
        $command = new MongoDB\Driver\Command([
            'aggregate' => $collection,
            'pipeline' => $pipeline,
            'cursor' => new stdClass()
        ]);
        
        $cursor = $manager->executeCommand($this->getDbName(), $command);
        return $cursor->toArray();
    }

    public function aggregateObjects($pipeline = array(), $options = array())
    {
        return $this->mongoAggregate('objects', $pipeline, $options);
    }

    public function aggregateForSelector($pipeline = array(), $options = array())
    {
        $data = $this->mongoAggregate('objects', $pipeline, $options);
        // print_r($data);
        $map = $this->getBrenollisValueMap(true);
        // $mappedData = array_map(function($item) use ($map) {
        //     if (isset($map[$item->_id])) {
        //         $item->_id = $map[$item->_id];
        //     }
        //     return $item;
        // }, $data);
        $finalData = array();
        $existCheck = array();
        foreach ($data as $item) {
            if (isset($map[$item->_id])) {
                $item->_id = $map[$item->_id];
            }
            if (!isset($existCheck[$item->_id])) {
                $existCheck[$item->_id] = 1;
                $finalData[] = $item;
            }
        }
        return $finalData;
    }

    public function handleObjectFilters($filters = array())
    {
        $filters = array_filter($filters);
        $condition = array();
        foreach (array_intersect_key($this->filterTypes(), $filters) as $field => $type) {
            if (is_array($type)) {
                if (!isset($condition['$or'])) {
                    $condition['$or'] = array();
                }
                foreach ($type as $subField => $subType) {
                    $condition['$or'][] = $this->parseField($subField, $filters[$field], $subType);
                }
            } else {
                $condition = array_merge($condition, $this->parseField($field, $filters[$field], $type));
            }
        }
        // print_r($condition);
        return $condition;
    }

    public function parseField($field, $value, $type)
    {
        $condition = array();
        $elemMatch = false;
        if (is_array($type)) {
            $elemMatch = $type;
            $type = $type['value'];
        }
        if (is_array($value)) {
            foreach (array('min' => '$gte', 'max' => '$lte') as $in => $out) {
                if (isset($value[$in]) && !empty($value[$in])) {
                    settype($value[$in], $type);
                    $condition['Object.'.$field][$out] = $value[$in];
                }
            }
        } else {
            $value = trim($value);
            settype($value, $type);
            if (isset($this->regexFields()[$field])) {
                $value = array('$regex' => $value, '$options' => $this->regexFields()[$field]);
            }
            if ($elemMatch) {
                $condition['Object.'.$field] = $this->getElemMatch($elemMatch, $value);
            } else {
                $map = $this->getBrenollisValueMap();
                if (is_array($map) && isset($map[$value])) {
                    $condition['Object.'.$field] = array(
                        '$in' => array($value, $map[$value]),
                    );
                } else {
                    $condition['Object.'.$field] = $value;
                }
            }
        }
        return $condition;
    }

    public function getElemMatch($type, $value)
    {
        return array('$elemMatch' => array($type['key'] => $value));
    }

    public function handlePagination()
    {
        $perPage = 9;
        $page = 1;

        if (isset($_GET['perPage'])) {
            $perPage = (int)$_GET['perPage'];
        }
        if (isset($_GET['curPage'])) {
            $page = (int)$_GET['curPage'];
        }

        $options = array(
            'limit' => $perPage,
            'skip' => ($page-1)*$perPage,
        );
        // print_r($options);
        return $options;
    }

    public function handleSort($fields = array())
    {
        $fields = array_filter($fields);
        $ret = array();
        $validFields = array_keys($this->sortableFields());
        foreach ($fields as $field => $type) {
            if (in_array($field, $validFields)) {
                $ret['Object.'.$field] = (int)$fields[$field];
            }
        }
        return $ret;
    }

    public function fixObjects($data, $objectOnly=true, $offset=0)
    {
        $code = pll_current_language();
        $objects = array();
        foreach ($data as $item) {
            $item->Object->offset = $offset;

            $postId = $item->PostIds->$code;

            // $title = pll__($item->Object->objectTypes->type).$this->getAddress($item->Object);
            $title = $this->getAddress($item->Object);
            $item->Object->title = $title;
            $item->Object->url = get_permalink($postId);

            $images = get_field('images', $item->PostIds->$code);
            if ($images) {
                $firstImage = $images[0]['image'];
                $item->Object->thumbnail = $firstImage;
            }

            // translated transaction type  and object type
            if ($item->Object->transactions && $item->Object->transactions->transaction->type) {
                $item->Object->transactions->transaction->typePlural = $item->Object->transactions->transaction->type;
                $item->Object->transactions->transaction->type = NnaTranslations::getInstance()->__($item->Object->transactions->transaction->type);
                $item->Object->transactions->transaction->typePlural = NnaTranslations::getInstance()->__($item->Object->transactions->transaction->typePlural . 's');
            }
            if ($item->Object->objectTypes) {
                $item->Object->objectTypes->typePlural = $item->Object->objectTypes->type;
                $item->Object->objectTypes->type = NnaTranslations::getInstance()->__($item->Object->objectTypes->type);
                $item->Object->objectTypes->typePlural = NnaTranslations::getInstance()->__($item->Object->objectTypes->typePlural . 's');
            }

            // remove 0 values from area
            // print_r($item->Object);
            if ($item->Object->areaSize && $item->Object->areaSize->_ == '0') {
                $item->Object->areaSize = false;
            }
            // set info rows keys to lang keys (not working, changes reference, change when actually needed)
            if ($item->Object->transactions->transaction->info) {
            $infoRows = $item->Object->transactions->transaction->info;
                foreach ($infoRows as $key => $infoRow) {
                    $langKey = $this->getLanguageCode($infoRow->language, true);
                    $fixedKeys[$langKey] = $infoRow;

                    $infoRowStripped = $infoRow;
                    $infoRowStripped->_ = strip_tags($infoRow->_);
                    $fixedKeysStripped[$langKey] = $infoRowStripped;

                }
                $item->Object->transactions->transaction->info = $fixedKeys;
                $item->Object->transactions->transaction->infoStripped = $fixedKeysStripped;
            }
            // print_r($item);

            if ($objectOnly) {
                $objects[] = $item->Object;
            } else {
                $objects[] = $item;
            }

            if ($item->Object->areaSize) {
                $item->Object->areaSize->_ = trimTrailingZeroes(number_format((float)$item->Object->areaSize->_, 2, '.', ' '));
            }
            $item->Object->transactions->transaction->price->_ = trimTrailingZeroes(number_format((float)$item->Object->transactions->transaction->price->_, 2, '.', ' '));
            $item->Object->transactions->transaction->pricem2->_ = trimTrailingZeroes(number_format((float)$item->Object->transactions->transaction->pricem2->_, 2, '.', ' '));

            // get excerpt
            $infoRows = array();
            foreach ($item->Object->transactions->transaction->info as $key => $row) {
                $infoRows[$key] = (object) array(
                    '_' => $this->getExerpt($row->_),
                    'language' => $row->language
                );
            }
            $item->Object->transactions->transaction->infoExcerpt = $infoRows;
            $offset++;
        }
        return $objects;
    }

    public function getExerpt($text){
        $excerpt = $text;
        $ret = '';
        $charlength = 340;
        if ( mb_strlen( $excerpt ) > $charlength ) {
            $subex = mb_substr( $excerpt, 0, $charlength - 5 );
            $exwords = explode( ' ', $subex );
            $excut = - ( mb_strlen( $exwords[ count( $exwords ) - 1 ] ) );
            if ( $excut < 0 ) {
                $ret = mb_substr( $subex, 0, $excut );
            } else {
                $ret = $subex;
            }
            $ret .= '...';
        } else {
            $ret = $excerpt;
        }
        return $ret;
    }

    public function getAddress($object)
    {
        $address = array();
        foreach (array('1','2','3','4','5','6') as $number) {
            if (!$object->{'level'.$number}) {
                break;
            }
            $address[] = trim($object->{'level'.$number});
        }
        if ($object->street) {
            $address[] = trim($object->street->_);
        }
        $address = implode(', ', $address);
        if ($object->houseNumber) {
            $address .= (!strlen($address) ? '' : ' ').trim($object->houseNumber->_);
        }
        return $address;
    }

    public function getDuplicateObjects()
    {
        $pipeline = array(
            array(
                '$group' => array(
                    '_id' => '$Object.originalId',
                    'ids' =>  array(
                        '$push' => '$_id',
                    ),
                    'count' =>  array(
                        '$sum' => 1
                    ),
                )
            ),
            array(
                '$match' => array(
                    'count' =>  array(
                        '$gt' => 1
                    )
                )
            )
        );

        return $this->aggregateObjects($pipeline);
    }

    public function deleteDuplicateObjects()
    {
        echo 'Delete duplicates'.PHP_EOL;
        $objects = $this->getDuplicateObjects();

        $ids = array();
        foreach ($objects as $object) {
            for ($i=1; $i < $object->count; $i++) {
                $ids[] = $object->ids[$i];
            }
        }

        $condition = array(
            '_id' => array(
                '$in' => $ids,
            ),
        );

        return $this->deleteObjects($condition, false);
    }

    public function fixBrokerTranslations()
    {
        $collection = $this->getCollection($this->getDbName(), 'brokers');
        $sobject=array();
        foreach ($collection->find($sobject) as $object) {
            $missing = array();
            $wrongLang = array();
            $newPostIds = (array) $object->PostIds;
            foreach ($object->PostIds as $code => $postId) {
                $wpLang = pll_get_post_language($postId);
                if (!$wpLang) {
                    $post = get_post($postId);
                    if (!$post) {
                        $newPostIds[$code] = null;
                    } else {
                        pll_set_post_language($postId, $code);
                    }
                } else if ($wpLang != $code) {
                    $wrongLang[] = 'wpLang: '.$wpLang.' mongoLang: '.$code.' post id: '.$postId;
                    $newPostIds[$wpLang] = $object->PostIds->$code;
                }
            }

            $postId = null;
            foreach ($this->getLanguages() as $code) {
                if (!isset($newPostIds[$code]) || !$newPostIds[$code]) {
                    $missing[] = $code;
                    $postId = WpHandler::getInstance()->insertBrokerPost($object->Broker, $code);
                    if ($postId) {
                        WpHandler::getInstance()->insertFeaturedImage($postId, $object->Broker->pictureUrl, $object->name);
                        $newPostIds[$code] = $postId;
                    }
                }
            }
            if (count($missing) || count($wrongLang)) {
                echo $object->Broker->originalId. ' - '.$object->Broker->name.PHP_EOL;
                print_r($object->PostIds);
                print_r($newPostIds);
                echo 'missing: '.implode(', ', $missing).PHP_EOL;
                echo 'wrong language: '.PHP_EOL.implode(PHP_EOL, $wrongLang).PHP_EOL;
                pll_save_post_translations($newPostIds);
                $collection->updateOne(array('_id' => $object->_id), array('$set' => array('PostIds' => $newPostIds)));
            }
        }
    }
}
