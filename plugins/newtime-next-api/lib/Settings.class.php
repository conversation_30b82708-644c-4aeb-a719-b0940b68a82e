<?php
class NnaSettings
{
    public static $menuSlug = 'newtime-next-api';
    public static $settingsSlug = 'nna-settings';
    public static $connectionSettingsName = 'nna_connection_settings';
    public static $mongoSettingsName = 'nna_mongo_settings';

    public function __construct()
    {
        // Register menu item
        add_action('admin_menu', array($this, 'registerMenuItem'));
        add_action('admin_init', array($this, 'registerSettings'));

    }

    public function registerMenuItem()
    {
        add_submenu_page(self::$menuSlug, 'Settings', 'Settings', 'manage_options', self::$settingsSlug, array($this, 'viewIndex'));
    }

    public function viewIndex()
    {

        echo '<form method="POST" action="options.php">';
        settings_fields(self::$settingsSlug);
        do_settings_sections(self::$settingsSlug);
        submit_button();
        echo '</form>';
    }

    public function registerSettings()
    {
        $connectorSectionFields = array(
            array(
                'id' => 'apiKey',
                'label' => 'Brenollis api key',
            ),
            array(
                'id' => 'apiUrl',
                'label' => 'Brenollis api url',
            ),
            array(
                'id' => 'user',
                'label' => 'Next user name',
            ),
            array(
                'id' => 'password',
                'label' => 'Next password',
            ),
            array(
                'id' => 'specialUser',
                'label' => 'Next user name for hidden objects',
            ),
            array(
                'id' => 'specialPassword',
                'label' => 'Next password for hidden objects',
            ),
        );

        $mongoSectionFields = array(
            array(
                'id' => 'dbUrl',
                'label' => 'Mongo database url',
            ),
            array(
                'id' => 'dbName',
                'label' => 'Mongo database name',
            ),
            array(
                'id' => 'user',
                'label' => 'Mongo database user name',
            ),
            array(
                'id' => 'password',
                'label' => 'Mongo database password',
            ),
        );

        $this->registerSection(
            'nna_connection_section',
            'Connection settings',
            self::$settingsSlug,
            self::$connectionSettingsName,
            'nna_connection_section',
            $connectorSectionFields
        );

        $this->registerSection(
            'nna_mongo_section',
            'Mongo settings',
            self::$settingsSlug,
            self::$mongoSettingsName,
            'nna_mongo_section',
            $mongoSectionFields
        );
    }

    public function registerSection($name, $title, $page, $settingName, $parentSection, $fields, $sectionDescription = '')
    {
        add_settings_section(
            $name,
            $title,
            array($this, 'sectionInfo'),
            $page
        );

        if (!empty($settingName)) {
            if (!empty($fields)) {
                $this->populateFields($fields, $page, $name, $settingName);
            }

            register_setting($page, $settingName);
        }
    }

    public function populateFields($fields, $page, $section, $setting)
    {
        foreach ($fields as $field) {
            add_settings_field(
                $field['id'],
                $field['label'],
                array($this, 'createFormField'),
                $page,
                $section,
                array(
                    'name' => $field['id'],
                    'setting' => $setting,
                    'type' => isset($field['type']) ? $field['type'] : null,
                    'default' => isset($field['default']) ? $field['default'] : '',
                    'options' => isset($field['options']) ? $field['options'] : '',
                    'description' => isset($field['description']) ? $field['description'] : '',
                )
            );
        }
    }

    public function sectionInfo($sectionID)
    {
        // echo $this->$sectionID['id'];
        // print_r($sectionID);
    }

    public function createFormField($args)
    {
        $option = get_option($args['setting']);
        $option = isset($option[$args['name']]) ? $option[$args['name']] : null;

        if ($args['type'] == 'checkbox'): ?>
            <input type="checkbox"
                name="<?=$args['setting']?>[<?=$args['name']?>]"
                id="nna_setting[<?=$args['name']?>]"
                value="1"
                <?php checked(1, $option, true)?> />
        <?php elseif ($args['type'] == 'select'): ?>
            <select name="<?=$args['setting']?>[<?=$args['name']?>]" id="nna_setting[<?=$args['name']?>]">
                <?php foreach ($args['options'] as $k => $v): ?>
                    <option value="<?php echo $k?>" <?php selected($option, $k);?>><?php echo $v?></option>
                <?php endforeach;?>
            </select>
        <?php elseif ($args['type'] == 'textarea'): ?>
            <textarea
            style="height: 200px; width: 450px;"
            name="<?=$args['setting']?>[<?=$args['name']?>]" id="nna_setting[<?=$args['name']?>]"><?=$option;?></textarea>
        <?php else: ?>
            <input style="width: 300px;" type="text"
                id="nna_setting[<?=$args['name']?>]"
                name="<?=$args['setting']?>[<?=$args['name']?>]"
                value="<?=$option;?>" />
        <?php
endif;

        echo !empty($args['description']) ? '<br><small>' . $args['description'] . '</small>' : '';
    }
}
