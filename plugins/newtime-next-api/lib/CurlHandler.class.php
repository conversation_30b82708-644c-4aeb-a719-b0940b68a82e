<?php
class CurlHandler {
	private static $instance;
	private $url = 'https://maps.google.com/maps/api/geocode/json';

	public static function getInstance()
    {
        if (null == self::$instance) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public function getCoordinates($object)
    {	//key
		$key = WPHandler::getInstance()->getGoogleMapKey();
		//file_put_contents(NNA_PATH . '/log/debug.log', $key.PHP_EOL, FILE_APPEND);
		$apiKey = 'AIzaSyAE75olbNpn95Y4h4lxqECjO-gKyaK4X4o';
		$query = http_build_query(array('address' => NnaApi::getInstance()->getAddress($object)));
    	$url = $this->url.'?'.$query.'&key='.$apiKey;
		$data = json_decode($this->getData($url));
    	if ($data->status == 'OK') {
    		foreach ($data->results as $result) {
    			if (isset($result->geometry->location)) {
					//file_put_contents(NNA_PATH . '/log/debug.log', json_encode($object->originalId).' '.json_encode($result->geometry->location).' '.$query.PHP_EOL, FILE_APPEND);
					//file_put_contents(NNA_PATH . '/log/debug.log', $query.PHP_EOL, FILE_APPEND);
    				return $result->geometry->location;
    			}
    		}
    	}
    	return false;
    }

    /* gets the data from a URL */
	public function getData($url) {
		$ch = curl_init();
		$timeout = 5;
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
		$data = curl_exec($ch);
		//file_put_contents(NNA_PATH . '/log/debug.log', $data.PHP_EOL, FILE_APPEND);
		curl_close($ch);
		return $data;
	}
}