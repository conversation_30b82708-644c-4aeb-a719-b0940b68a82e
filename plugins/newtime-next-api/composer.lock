{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "7a0a445756ec8046f93d49a56b7d2d0f", "packages": [{"name": "mongodb/mongodb", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/mongodb/mongo-php-library.git", "reference": "04cd7edc6a28950e3fab6eccb2869d72a0541232"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mongodb/mongo-php-library/zipball/04cd7edc6a28950e3fab6eccb2869d72a0541232", "reference": "04cd7edc6a28950e3fab6eccb2869d72a0541232", "shasum": ""}, "require": {"composer-runtime-api": "^2.0", "ext-mongodb": "^2.0", "php": "^8.1", "psr/log": "^1.1.4|^2|^3"}, "replace": {"mongodb/builder": "*"}, "require-dev": {"doctrine/coding-standard": "^12.0", "phpunit/phpunit": "^10.5.35", "rector/rector": "^1.2", "squizlabs/php_codesniffer": "^3.7", "vimeo/psalm": "6.5.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"MongoDB\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "MongoDB driver library", "homepage": "https://jira.mongodb.org/browse/PHPLIB", "keywords": ["database", "driver", "mongodb", "persistence"], "support": {"issues": "https://github.com/mongodb/mongo-php-library/issues", "source": "https://github.com/mongodb/mongo-php-library/tree/2.0.0"}, "time": "2025-04-10T08:34:11+00:00"}, {"name": "phpmailer/phpmailer", "version": "v5.2.28", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "acba50393dd03da69a50226c139722af8b153b11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/acba50393dd03da69a50226c139722af8b153b11", "reference": "acba50393dd03da69a50226c139722af8b153b11", "shasum": ""}, "require": {"ext-ctype": "*", "php": ">=5.0.0"}, "require-dev": {"doctrine/annotations": "1.2.*", "jms/serializer": "0.16.*", "phpdocumentor/phpdocumentor": "2.*", "phpunit/phpunit": "4.8.*", "symfony/debug": "2.8.*", "symfony/filesystem": "2.8.*", "symfony/translation": "2.8.*", "symfony/yaml": "2.8.*", "zendframework/zend-cache": "2.5.1", "zendframework/zend-config": "2.5.1", "zendframework/zend-eventmanager": "2.5.1", "zendframework/zend-filter": "2.5.1", "zendframework/zend-i18n": "2.5.1", "zendframework/zend-json": "2.5.1", "zendframework/zend-math": "2.5.1", "zendframework/zend-serializer": "2.5.*", "zendframework/zend-servicemanager": "2.5.*", "zendframework/zend-stdlib": "2.5.1"}, "suggest": {"league/oauth2-google": "Needed for Google XOAUTH2 authentication"}, "type": "library", "autoload": {"classmap": ["class.phpmailer.php", "class.phpmaileroauth.php", "class.phpmaileroauthgoogle.php", "class.smtp.php", "class.pop3.php", "extras/EasyPeasyICS.php", "extras/ntlm_sasl_client.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "support": {"issues": "https://github.com/PHPMailer/PHPMailer/issues", "source": "https://github.com/PHPMailer/PHPMailer/tree/v5.2.28"}, "funding": [{"url": "https://marcus.bointon.com/donations/", "type": "custom"}, {"url": "https://github.com/Synchro", "type": "github"}, {"url": "https://www.patreon.com/marcusbointon", "type": "patreon"}], "time": "2020-03-19T14:29:37+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {"ext-curl": "*", "ext-json": "*"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}