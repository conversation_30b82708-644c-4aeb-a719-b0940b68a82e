<?php
/**
 * Real-time sync monitoring script
 * Run this in terminal to monitor sync activity
 */

if (php_sapi_name() !== 'cli') {
    die("This script must be run from command line");
}

$logFiles = [
    'Sync Log' => __DIR__ . '/log/synclog.log',
    'WordPress Debug' => dirname(dirname(dirname(__DIR__))) . '/debug.log'
];

echo "=== Sync Monitor Started ===\n";
echo "Monitoring log files:\n";
foreach ($logFiles as $name => $path) {
    echo "- $name: $path\n";
}
echo "\nPress Ctrl+C to stop monitoring\n";
echo "================================\n\n";

// Get initial file sizes
$lastSizes = [];
foreach ($logFiles as $name => $path) {
    $lastSizes[$name] = file_exists($path) ? filesize($path) : 0;
}

while (true) {
    foreach ($logFiles as $name => $path) {
        if (file_exists($path)) {
            $currentSize = filesize($path);
            if ($currentSize > $lastSizes[$name]) {
                // File has grown, read new content
                $handle = fopen($path, 'r');
                fseek($handle, $lastSizes[$name]);
                $newContent = fread($handle, $currentSize - $lastSizes[$name]);
                fclose($handle);
                
                if (trim($newContent)) {
                    echo "[" . date('H:i:s') . "] [$name] " . trim($newContent) . "\n";
                }
                
                $lastSizes[$name] = $currentSize;
            }
        }
    }
    
    usleep(500000); // Sleep for 0.5 seconds
}
