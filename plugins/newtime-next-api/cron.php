<?php
if( php_sapi_name() !== 'cli' ) {
    die("Meant to be run from command line");
}

function find_wordpress_base_path() {
    $dir = dirname(__FILE__);
    do {
        //it is possible to check for other files here
        if( file_exists($dir."/wp-config.php") ) {
            return $dir;
        }
    } while( $dir = realpath("$dir/..") );
    return null;
}

define( 'BASE_PATH', find_wordpress_base_path()."/" );
define('WP_USE_THEMES', false);
global $wp, $wp_query, $wp_the_query, $wp_rewrite, $wp_did_header;
require(BASE_PATH . 'wp-load.php');

//ScoroSync::getInstance()->syncUsers();
//ScoroSync::getInstance()->startObjectSync(false);
BrenollisSync::getInstance()->startObjectSync();
BrenollisSync::getInstance()->fixObjectBrokerPostIds();

echo 'deleted '.NnaApi::getInstance()->deleteDuplicateObjects()->getDeletedCount().' objects';
// NnaHiddenObjectUpdater::getInstance()->startObjectSync();

BrenollisSync::getInstance()->fixObjectFields();

// BrenollisSync::getInstance()->deleteAll();
// print_r(NnaApi::getInstance()->deleteObjects(array(), false));
// print_r(NnaApi::getInstance()->deleteBrokers(array(), false));
// NnaApi::getInstance()->deleteWpObjects();
