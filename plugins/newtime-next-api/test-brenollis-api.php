<?php
/**
 * Test Brenollis API connection
 */

if (php_sapi_name() !== 'cli') {
    die("This script must be run from command line");
}

// Load WordPress
define('BASE_PATH', dirname(dirname(dirname(__DIR__))) . '/');
define('WP_USE_THEMES', false);
require(BASE_PATH . 'wp-load.php');

echo "=== Testing Brenollis API Connection ===\n";

try {
    // Get settings
    $options = get_option('nna_connection_settings');
    if (!$options || !isset($options['apiKey']) || !isset($options['apiUrl'])) {
        throw new Exception('Brenollis API settings not found');
    }
    
    echo "API URL: " . $options['apiUrl'] . "\n";
    echo "API Key: " . substr($options['apiKey'], 0, 20) . "...\n\n";
    
    // Test basic connection (BrenollisSync adds /api/ to the URL)
    $parsedUrl = parse_url($options['apiUrl']);
    $baseUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . '/api/';
    $url = $baseUrl . 'adverts?size=1&page=0';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'X-API-KEY: ' . $options['apiKey'],
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    echo "Testing connection to: $url\n";
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        throw new Exception("cURL error: $error");
    }
    
    echo "HTTP Status Code: $httpCode\n";
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        if ($data) {
            echo "✓ API connection successful!\n";
            echo "Total elements: " . (isset($data['totalElements']) ? $data['totalElements'] : 'unknown') . "\n";
            echo "Total pages: " . (isset($data['totalPages']) ? $data['totalPages'] : 'unknown') . "\n";
            echo "Content count: " . (isset($data['content']) ? count($data['content']) : 'unknown') . "\n";
            
            if (isset($data['content']) && count($data['content']) > 0) {
                $firstAd = $data['content'][0];
                echo "\nFirst ad sample:\n";
                echo "- ID: " . (isset($firstAd['id']) ? $firstAd['id'] : 'unknown') . "\n";
                echo "- Status: " . (isset($firstAd['status']) ? $firstAd['status'] : 'unknown') . "\n";
                echo "- Type: " . (isset($firstAd['type']) ? $firstAd['type'] : 'unknown') . "\n";
            }
        } else {
            echo "✗ Invalid JSON response\n";
            echo "Response: " . substr($response, 0, 500) . "\n";
        }
    } else {
        echo "✗ API request failed\n";
        echo "Response: " . substr($response, 0, 500) . "\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
