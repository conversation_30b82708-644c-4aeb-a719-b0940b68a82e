<?php
/**
 * Plugin Name: Next API sync client
 * Plugin URI: http://www.newtime.ee/
 * Description: Sync objects from SCORO.
 * Version: 1.0.0
 * Author: Newtime
 * Author URI: http://www.newtime.ee/
 * License: GPL3
 */

require_once plugin_dir_path(__FILE__) . '/loader.php';

class NewtimeNextApi
{
    public static $objectPostType = 'object';
    public static $brokerPostType = 'broker';
    private static $optionsSlug = 'nna-global-options';
    private static $options = array();

    public function __construct()
    {
        // Register admin styles and scripts
        // add_action('admin_print_styles', array($this, 'registerAdminStyles'));
        add_action('admin_enqueue_scripts', array($this, 'registerAdminScripts'));

        // Register frontend styles
        add_action('wp_enqueue_scripts', array($this, 'registerFrontendStyles'));
        add_action('wp_enqueue_scripts', array($this, 'registerFrontendScripts'));

        // Register menu
        add_action('admin_menu', array($this, 'registerPluginMenu'));

        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'loadFirst'));
        add_action('plugins_loaded', array(NnaTranslations::getInstance(), 'registerTranslations'));
        add_action('plugins_loaded', array('NnaPageTemplate', 'get_instance'));
        add_action('plugins_loaded', array('WpHandler', 'handleScoroRedirect'));

        // Setup deactivation for cleaning up after plugin
        // register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_activation_hook(__FILE__, array($this, 'activate'));
    }

    public function activate()
    {
        //check for user rights
        if (!current_user_can('activate_plugins')) {
            return;
        }

        //security check
        $plugin = isset($_REQUEST['plugin']) ? $_REQUEST['plugin'] : '';
        check_admin_referer("activate-plugin_{$plugin}");

        //add table
        $this->createTranslationTable();
    }

    public function loadFirst()
    {
        add_filter('pll_get_post_types', array($this, 'addProductTypeToPll'), 10, 2);
        NnaAjax::getInstance();
    }

    public function init()
    {
        $this->settings = new NnaSettings();
        //register taxonomy and custom post type
        $this->registerPostType();
    }

    public function route()
    {
        $this->viewIndex();
    }

    public function viewIndex()
    {
        echo '<h1>Tere! Muuda settingute alt seaded sobivaks.</h1>';
        echo '<div>';
            echo '<button class="button button-primary" id="nna-sync-objects">Sünkroniseeri objektid</button>';
        echo '</div>';
        echo '<div>';
            echo '<button class="button button-primary" id="nna-sync-users">Sünkroniseeri kasutajad</button>';
        echo '</div>';
        echo '<div>';
            echo '<button class="button button-primary" id="nna-sync-brenollis-objects">Sünkroniseeri objektid brenollisest</button>';
            echo ' <button class="button button-secondary" id="nna-fast-sync-brenollis-objects" style="background-color: #ff6b35; border-color: #ff6b35; color: white;">🚀 Kiire sünkroniseerimine</button>';
        echo '</div>';
        echo '<div style="margin-top: 10px;">';
            echo '<button class="button button-secondary" id="nna-create-missing-posts" style="background-color: #28a745; border-color: #28a745; color: white;">🔧 Loo puuduvad WordPress postitused</button>';
        echo '</div>';
    }

    public function createTranslationTable()
    {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        $tableName = $wpdb->prefix . "nna_string_translations";

        $sql = "CREATE TABLE $tableName (
            prefix VARCHAR(50),
            translation_string VARCHAR(190),
            PRIMARY KEY (translation_string)
        ) $charset_collate;";

        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        $data = dbDelta($sql);

        $fieldName = NnaTranslations::STRING_FIELD;

        $wpdb->insert($tableName, array($fieldName => 'apartment'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'arableLand'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'businessLand'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'commercialBureau'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'commercialDining'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'commercialHostel'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'commercialManufacture'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'commercialService'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'commercialTrade'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'commercialWarehouse'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'cottage'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'forestLand'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'house'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'housePart'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'manufacturingLand'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'noPurposeLand'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'pairHouseBox'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'residentialLand'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'rowHouseBox'), '%s');
        $wpdb->insert($tableName, array($fieldName => 'socialLand'), '%s');
        // var_dump($data);
        // exit();
    }

    /*
    ======================================================================================
    Register PostType
    ======================================================================================
     */

    public function registerPostType()
    {
        register_post_type(
            self::$objectPostType,
            array(
                'labels' => array(
                    'name' => 'Objects',
                    'singular_name' => 'Object',
                ),
                'public' => true,
                'show_ui' => true,
                'hierarchical' => false,
                'supports' => array(
                    'title',
                    'editor', // (content)
                    'author',
                    'thumbnail', // (featured image, current theme must also support post-thumbnails)
                    'excerpt',
                    'trackbacks',
                    'custom-fields',
                    'comments', // (also will see comment count balloon on edit screen)
                    'page-attributes', // (menu order, hierarchical must be true to show Parent option)
                    'post-formats', // add post formats, see Post Formats
                ),
                'menu_position' => 23,
            )
        );

        register_post_type(
            self::$brokerPostType,
            array(
                'labels' => array(
                    'name' => 'Brokers',
                    'singular_name' => 'Broker',
                ),
                'public' => true,
                'show_ui' => true,
                'hierarchical' => false,
                'supports' => array(
                    'title',
                    'editor', // (content)
                    'author',
                    'thumbnail', // (featured image, current theme must also support post-thumbnails)
                    'excerpt',
                    'trackbacks',
                    'custom-fields',
                    'comments', // (also will see comment count balloon on edit screen)
                    'page-attributes', // (menu order, hierarchical must be true to show Parent option)
                    'post-formats', // add post formats, see Post Formats
                ),
                'menu_position' => 24,
            )
        );
    }

    public function addProductTypeToPll($post_types, $hide)
    {
        $post_types[self::$objectPostType] = self::$objectPostType;
        $post_types[self::$brokerPostType] = self::$brokerPostType;
        return $post_types;
    }

    /*
    ======================================================================================
    Options
    ======================================================================================
     */

    public static function getOption($slug)
    {
        if (!isset(self::$options[$slug])) {
            self::$options = get_option(self::$optionsSlug);
            if (!isset(self::$options[$slug])) {
                self::setOption($slug, array());
            }
        }
        return self::$options[$slug];
    }

    public static function setOption($slug, $value)
    {
        self::$options[$slug] = $value;
        return update_option(self::$optionsSlug, self::$options);
    }

    public static function setOptionKey($slug, $key, $value)
    {
        self::getOption($slug);
        self::$options[$slug][$key] = $value;
        return update_option(self::$optionsSlug, self::$options);
    }

    /*
    ======================================================================================
    Enqueue Stylesheets & Scripts
    ======================================================================================
     */

    public function registerAdminScripts()
    {
        // $options = get_option(NnaSettings::$connectionSettingsName);
        // $ajaxObject = array(
        //     'ajaxUrl' => admin_url('admin-ajax.php'),
        //     'nonce' => wp_create_nonce('ec-nonce'),
        //     'apiUrl' => $options['apiUrl'],
        // );
        wp_enqueue_script('jquery');
        // wp_localize_script('nnaAdminScript', 'nnaAjaxObject', $ajaxObject);
        wp_enqueue_script('nnaAdminScript', NNA_URL . '/assets/js/admin.js', 'jquery');
    }

    public function registerFrontendStyles()
    {
        wp_enqueue_style( 'nna-styles', NNA_URL . '/assets/css/style.css');
    }

    public function registerFrontendScripts()
    {
        // $options = get_option(NnaSettings::$connectionSettingsName);
        $ajaxObject = array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
        //     'apiUrl' => $options['apiUrl'],
        //     'exportType' => 'Wordpress',
            'language' => pll_current_language(),
        //     'url' => $options["apiUrl"]
        );
        wp_enqueue_script('jquery');
        wp_enqueue_script('nnaMainScript', NNA_URL . '/assets/js/main.js', array('jquery', 'ICanHaz'));
        wp_localize_script('nnaMainScript', 'nnaAjaxObject', $ajaxObject);
        // wp_enqueue_script('nna', NNA_URL . '/assets/js/nna.js');
        wp_enqueue_script('ICanHaz', NNA_URL . '/assets/js/vendor/ICanHaz.min.js', 'jquery');
    }

    /*
    ======================================================================================
    Register Menu
    ======================================================================================
     */

    public function registerPluginMenu()
    {
        add_menu_page('Newtime Next API', 'Newtime Next API', 'manage_options', NnaSettings::$menuSlug, array($this, 'route'), null, null);
    }
}
new NewtimeNextApi;
