<?php
if( php_sapi_name() !== 'cli' ) {
    die("Meant to be run from command line");
}

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

function find_wordpress_base_path() {
    $dir = dirname(__FILE__);
    do {
        //it is possible to check for other files here
        if( file_exists($dir."/wp-config.php") ) {
            return $dir;
        }
    } while( $dir = realpath("$dir/..") );
    return null;
}

define( 'BASE_PATH', find_wordpress_base_path()."/" );
define('WP_USE_THEMES', false);
global $wp, $wp_query, $wp_the_query, $wp_rewrite, $wp_did_header;
require(BASE_PATH . 'wp-load.php');

try {
    echo "Starting Brenollis sync...\n";
    
    // Get active IDs
    echo "Getting active IDs...\n";
    $ids = BrenollisSync::getInstance()->getActiveIds();
    echo "Found " . count($ids) . " active IDs\n";
    
    // Delete objects not in active IDs
    echo "Deleting objects not in active IDs...\n";
    $deleted = BrenollisSync::getInstance()->deleteObjectsByIdsNotIn($ids);
    echo "Deleted " . $deleted . " objects\n";
    
    // Sync first page only
    echo "Syncing first page...\n";
    $info = BrenollisSync::getInstance()->updatePage(0, 10);
    echo "Updated: " . $info['updated'] . " inserted: " . $info['inserted'] . "\n";
    
    // Fix broker post IDs
    echo "Fixing broker post IDs...\n";
    BrenollisSync::getInstance()->fixObjectBrokerPostIds();
    echo "Broker post IDs fixed\n";
    
    // Delete duplicate objects
    echo "Deleting duplicate objects...\n";
    $deleted = NnaApi::getInstance()->deleteDuplicateObjects()->getDeletedCount();
    echo "Deleted " . $deleted . " duplicate objects\n";
    
    // Fix object fields
    echo "Fixing object fields...\n";
    BrenollisSync::getInstance()->fixObjectFields();
    echo "Object fields fixed\n";
    
    echo "Sync completed successfully!\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
