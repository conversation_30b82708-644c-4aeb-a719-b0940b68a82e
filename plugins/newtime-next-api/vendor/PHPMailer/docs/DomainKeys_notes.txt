CREATE DKIM KEYS and DNS Resource Record:
=========================================

To create DomainKeys Identified Mail keys, visit:
http://dkim.worxware.com/
... read the information, fill in the form, and download the ZIP file 
containing the public key, private key, DNS Resource Record and instructions
to add to your DNS Zone Record, and the PHPMailer code to enable DKIM
digital signing.

/*** PROTECT YOUR PRIVATE & PUBLIC KEYS ***/

You need to protect your DKIM private and public keys from being viewed or
accessed. Add protection to your .htaccess file as in this example:

# secure htkeyprivate file
<Files .htkeyprivate>
  order allow,deny
  deny from all
</Files>

# secure htkeypublic file
<Files .htkeypublic>
  order allow,deny
  deny from all
</Files>

(the actual .htaccess additions are in the ZIP file sent back to you from 
http://dkim.worxware.com/ 

A few notes on using DomainKey Identified Mail (DKIM):

You do not need to use PHPMailer to DKIM sign  emails IF:
- you enable DomainKey support and add the DNS resource record
- you use your outbound mail server

If you are a third-party emailer that works on behalf of domain owners to
send their emails from your own server:
- you absolutely have to DKIM sign outbound emails
- the domain owner has to add the DNS resource record to match the 
  private key, public key, selector, identity, and domain that you create
- use caution with the "selector" ... at least one "selector" will already
  exist in the DNS Zone Record of the domain at the domain owner's server
  you need to ensure that the "selector" you use is unique
Note: since the IP address will not match the domain owner's DNS Zone record
you can be certain that email providers that validate based on DomainKey will
check the domain owner's DNS Zone record for your DNS resource record. Before
sending out emails on behalf of domain owners, ensure they have entered the 
DNS resource record you provided them.

Enjoy!
Andy

PS. if you need additional information about DKIM, please see:
http://www.dkim.org/info/dkim-faq.html
