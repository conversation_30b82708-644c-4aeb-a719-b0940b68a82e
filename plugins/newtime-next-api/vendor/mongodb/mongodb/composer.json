{"name": "mongodb/mongodb", "description": "MongoDB driver library", "keywords": ["database", "driver", "mongodb", "persistence"], "homepage": "https://jira.mongodb.org/browse/PHPLIB", "license": "Apache-2.0", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "ext-mongodb": "^2.0", "composer-runtime-api": "^2.0", "psr/log": "^1.1.4|^2|^3"}, "require-dev": {"doctrine/coding-standard": "^12.0", "phpunit/phpunit": "^10.5.35", "rector/rector": "^1.2", "squizlabs/php_codesniffer": "^3.7", "vimeo/psalm": "6.5.*"}, "replace": {"mongodb/builder": "*"}, "autoload": {"psr-4": {"MongoDB\\": "src/"}, "files": ["src/functions.php"]}, "autoload-dev": {"psr-4": {"MongoDB\\Tests\\": "tests/"}}, "scripts": {"pre-install-cmd": "git submodule update --init", "pre-update-cmd": "git submodule update --init", "bench": "cd benchmark && composer update && vendor/bin/phpbench run --report=aggregate", "checks": ["@check:cs", "@check:psalm", "@check:rector"], "check:cs": "phpcs", "check:psalm": "psalm", "check:rector": "rector --ansi --dry-run", "fix:cs": "phpcbf", "fix:psalm:baseline": "psalm --set-baseline=psalm-baseline.xml", "fix:rector": "rector process --an<PERSON>", "test": "phpunit"}, "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}, "sort-packages": true}}