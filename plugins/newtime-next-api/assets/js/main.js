jQuery(function($) {
	$('body').on('click', '.nna-load-objects', function() {
		var $button = $(this);
		if ($button.hasClass('loading')) {
			return;
		}
		$button.addClass('loading');
		$('.nna-filters input[name=curPage]').val($button.data('page'));
		$button.data('page', parseInt($button.data('page'))+1);
		$filters = $('.nna-filters').serialize();
		$('.nna-loading-product').addClass('loading');

		$.ajax({
			url: nnaAjaxObject.ajaxUrl+'?'+$filters,
			type: 'post',
			dataType: 'json',
			data: {
				action: 'nnaGetObjects',
			},
			success: function(data) {
				if (!data.objects.length) {
					$button.removeClass('nna-load-objects');
				}
				$button.removeClass('loading');
				history.pushState(null, null, '?' + $('.nna-filters').serialize());
				$('.nna-objects-grid').append(ich.objectListGrid(data));
				$('.nna-objects-list').append(ich.objectListList(data));
				markFavourites();
				// console.log(data);
				$('.nna-loading-product').removeClass('loading');
			}
		});
	}).on('submit', '.nna-filters', function() {
		$(this).find('input[name="curPage"]').val('1');

	}).on('change', '.nna-change-pricetype', function(e) {
		$('.nna-change-pricetype-field-min').attr('name', $(this).val()+"['min']");
		$('.nna-change-pricetype-field-max').attr('name', $(this).val()+"['max']");
	}).on('change', '.nna-hierarchical-selector', function(e) {
		var $this = $(this);
		// trigger change on parent select
		if ($this.val()) {
			$('input[name="'+$this.data('parent')+'"]').trigger('changeChild');
		}
		// trigger change on child select
		$('input[data-parent="'+$this.attr('name')+'"]').trigger('changeParent');
	}).on('changeChild', '.nna-hierarchical-selector', function(e) {
		var $this = $(this);
		var $child = $('input[data-parent="'+$this.attr('name')+'"]');
		if ($child.length) {
			var valueToChangeTo = $child.closest('.nna-dropdown').find('[filter-value="' + $child.val() + '"]').data('parent');
			$this.val(valueToChangeTo).closest('.nna-dropdown').find('.nna-dropdown-btn').text(valueToChangeTo).addClass('active');
			// trigger change on parent select
			$('input[name="'+$this.data('parent')+'"]').trigger('changeChild');
		}
	}).on('changeParent', '.nna-hierarchical-selector', function(e) {
		var $this = $(this).val('');
		$this.closest('.nna-dropdown').find('.nna-dropdown-btn').text($this.closest('.nna-dropdown').find('.nna-filter-option.all').html()).removeClass('active');
		var $parent = $('input[name="'+$this.data('parent')+'"]');
		if ($parent.length) {
			$this.closest('.nna-dropdown').find('.nna-filter-option').not('.all').addClass('hidden');
			if ($parent.val()) {
				$this.closest('.nna-dropdown').find('.nna-filter-option[data-parent="'+$parent.val()+'"]').removeClass('hidden');
			} else {
				$.each($parent.closest('.nna-dropdown').find('.nna-filter-option:not(.hidden)'), function(i, item) {
					$this.closest('.nna-dropdown').find('.nna-filter-option[data-parent="'+$(item).attr('filter-value')+'"]').removeClass('hidden');
				});
			}
			// trigger change on child select
			$('input[data-parent="'+$this.attr('name')+'"]').trigger('changeParent');
		}
	}).on('click', '.nna-detailed-search-button', function() {
		$(this).parents('.nna-filters').toggleClass('not-detail');
		if ($(this).parents('.nna-filters').hasClass('not-detail')) {
			$('.nna-filter-open').val(0);
		} else{
			$('.nna-filter-open').val(1);
		}
	}).on('click', '.nna-sort-selector', function() {
		var val = $(this).attr('val').split(':');
		var text = $(this).text();
		$(this).closest('.nna-objects-order').find('.nna-dropdown-btn').html(text);
		$('.nna-filters .nna-sort-field').attr('name', 'sort['+val[0]+']').val(val[1]);
		$('.nna-filters').submit();
	}).on('change', '.nna-filters select', function() {
		var group = $(this).find(':selected').data('group');
		$(this).data('group', group);
		if (group == 'land') {
			$(this).parents('.nna-filters').addClass('not-detail');
			$('.nna-detailed-search-button').prop('disabled', true);
			$('.nna-filter-open').val(0);
		} else {
			$('.nna-detailed-search-button').prop('disabled', false);
		}
		if (group) {
			$('.nna-filters .nna-toggleable').not('.nna-'+group).hide();
			$('.nna-filters .nna-'+group).show();
		}
	}).on('click', '.nna-add-favourite', function(e){
		e.stopPropagation();
    	e.preventDefault();
		var favItemId = $(this).attr('item-id');
		var favBtn = $('.nna-add-favourite[item-id="' + favItemId + '"]');
		var	action = 'nnaSetFavourites';
		var adding = true;
		if ($(this).hasClass('active')) {
			action = 'nnaRemoveFavourites';
			adding = false;
		}

		$.ajax({
			url: nnaAjaxObject.ajaxUrl,
			type: 'post',
			dataType: 'json',
			data: {
				action: action,
				objectId: favItemId,
			},
			success: function(data) {
				if (data.count > 0) {
					$('.nna-favourited-items').addClass('active');
				} else{
					$('.nna-favourited-items').removeClass('active');
				}

				if (adding) {
					favBtn.addClass('active');
					if (favBtn.hasClass('nna-single-object')) {
						favBtn.addClass('fa-star');
						favBtn.removeClass('fa-star-o');
					}

					$('.nna-favourited-items-btn').html(data.count);
				} else{
					favBtn.removeClass('active');
					if (favBtn.hasClass('nna-single-object')) {
						favBtn.addClass('fa-star-o');
						favBtn.removeClass('fa-star');
					}
					$('.nna-favourited-items-btn').html(data.count);
				}
			}
		});

	}).on('click', '.nna-favourited-items', function(){
		if (getUrlParameter('favourites') == 1) {
			$('.nna-has-favourites').val(0);
		} else{
			$('.nna-has-favourites').val(1);
		}
		$('.nna-filters').submit();
	}).on('click', '.nna-submit-filter', function(){
		$('.nna-filters').submit();
	});

	function getUrlParameter(sParam) {
	    var sPageURL = decodeURIComponent(window.location.search.substring(1)),
	        sURLVariables = sPageURL.split('&'),
	        sParameterName,
	        i;

	    for (i = 0; i < sURLVariables.length; i++) {
	        sParameterName = sURLVariables[i].split('=');

	        if (sParameterName[0] === sParam) {
	            return sParameterName[1] === undefined ? true : sParameterName[1];
	        }
	    }
	}

	// $('body').on('checkVisibility', '.nna-hierarchical-selector option', function(e) {
	// 	var $this = $(this);
	// 	var $option = $this.find(':selected');
	// 	if ($this.data('parent') && $option.data('parent')) {
	// 		$('select[name="filters['+$this.data('parent')+']"]').val($option.data('parent')).trigger('change');
	// 	}
	// });

	//filter dropdown
	$('body').on('click', '.nna-filter-option', function() {
		var filterOption = $(this).attr('filter-value');
		$(this).closest('.nna-dropdown').find('input').val(filterOption).trigger('change');
		if (filterOption) {
			$(this).closest('.nna-dropdown').find('.nna-dropdown-btn').addClass('active');
		} else{
			$(this).closest('.nna-dropdown').find('.nna-dropdown-btn').removeClass('active');
		}

		var filterButton = $(this).html();
		$(this).closest('.nna-dropdown').find('.nna-dropdown-btn').html(filterButton);
	}).on('click', '.nna-filter-option[filter-pick-child]', function(){
		var target = $(this).attr('filter-pick-child');
		$('.nna-filter-option[filter-value="' + target + '"]').trigger('click');
	});

	//Keywords input width
	var keyLabel = $('.js-keywords-label').outerWidth();
	var keyData = $(".js-filter-table-data").width();
	var inputOffset = $('.js-keywords-input').outerWidth() - $('.js-keywords-input').width();
	var keyInputWidth = keyData - keyLabel - inputOffset;


	$('.js-keywords-input').css({'width' : keyInputWidth + "px"});


	//js-filter-td

	var filtertdWidth = $('.js-filter-td').outerWidth();
	var filterTableGroup = $('.js-filter-table-group').outerWidth();
	var filterTableGroupWidth = (filtertdWidth / 2) - 4;

	$('.js-filter-table-group').css({'width' : filterTableGroupWidth + "px"});


	var filterButtonWidth = $('.js-filter-button').outerWidth(true);

	$('.js-filter-button').closest('td').css({'width' : filterButtonWidth + "px"});


	$(window).resize(function() {


		var filtertdWidth = $('.js-filter-td').outerWidth();
		var filterTableGroup = $('.js-filter-table-group').outerWidth();
		var filterTableGroupWidth = (filtertdWidth / 2) - 14;

		$('.js-filter-table-group').css({'width' : filterTableGroupWidth + "px"});


		var rowWidth = $('.nna-filters').width();
		var filterButtonWidth = 0;
		$.each($('.js-filter-button'), function(){
			filterButtonWidth = filterButtonWidth + $(this).outerWidth();
		});
		$('.js-filter-button').closest('td').css({'width' : filterButtonWidth + "px"});

		// from full width - buttons width - (assuming only one other container and that the container has same padding and divide the padding of the other container)
		var widthLeft = rowWidth - $('.js-filter-button').closest('td').outerWidth() - ($('.js-filter-button').closest('td').outerWidth() - $('.js-filter-button').closest('td').width());

		var keyLabel = $('.js-keywords-label').outerWidth();
		// var keyData = $('.js-filter-table-data').width();
		var keyData = widthLeft;
		var inputOffset = $('.js-keywords-input').outerWidth() - $('.js-keywords-input').width();
		var keyInputWidth = keyData - keyLabel - inputOffset;

		$('.js-keywords-input').css({'width' : keyInputWidth + "px"});
	});

	//Dropdowns "placeholder"
	// var filterInputs = $('.nna-filter-option').closest('.nna-dropdown').find('input').val();
	// console.log(filterInpzuts);

	// if (typeof filterInputs === 'undefined') {
	// 	$('.nna-dropdown-btn').addClass('gray');
	// }
	// else {
	// 	$('.nna-dropdown-btn').addClass('yolo');
	// };

	$('body').on('click', '.js-contact-person-form-submit', function(e){
		e.preventDefault();
	    var validForm = true;
	    var form = $(this).closest('.js-contact-person-form');
	    var gaForm = $(this).closest('.ga-contact-form');
	    var action = form.attr('identifier-action');
	    form.find('.js-error').empty();
	   	$.each(form.find('.js-field'), function(){
	   		var input = $(this).find('.js-input');
	   		if (input.hasClass('js-required') && !input.val()) {
	   			validForm = false;
	   			$(this).find('.js-error').empty().append(ich.formErrorRequired());
	   		} else if(input.hasClass('js-required') && input.hasClass('js-email')){
	   			var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
	   			if(!input.val().match(re)) {
					validForm = false;
					$(this).find('.js-error').empty().append(ich.formErrorEmail());
				}
	   		}
	   	});

	   	if (validForm) {
		    $.ajax({
		        url: nnaAjaxObject.ajaxUrl,
		        type: 'post',
		        dataType: 'json',
		        data: {
		            action: action,
		            formData: form.serialize(),
		        },
		        success: function(data) {
		        	var messageBox = $('.js-form-submitted-message');
	        		messageBox.addClass('active').empty();
	        		switch(data.template) {
	        		    case 'takeContactFormSuccess':
	        				messageBox.append(ich.takeContactFormSuccess());
	        		        break;
	        		    case 'takeContactFormError':
	        				messageBox.append(ich.takeContactFormError());
	        		        break;
	        		    case 'askOfferFormSuccess':
	        				messageBox.append(ich.askOfferFormSuccess());
	        		        break;
	        		    case 'askOfferFormError':
	        				messageBox.append(ich.askOfferFormError());
	        		        break;
	        		}
	        		form.trigger('showMessage');
	        		gaForm.trigger('gaTakeContact');
		        	// if (data.status) {
		        	// 	$(form).find('.js-form-submitted-message').addClass('active').empty().append(ich.formSuccess());
		        	// } else{
		        	// 	$(form).find('.js-form-submitted-message').addClass('active').empty().append(ich.formFail());
		        	// }
		        },
		    });
	   	}
	}).on('submit', '.js-contact-person-form', function(e){
		e.preventDefault();
		$(this).find('.js-contact-person-form-submit').trigger('click');
	}).on('showMessage', '.js-offer-price-send', function(){
		$(this).removeClass('active');
		$('.js-form-submitted-message').addClass('active');
	}).on('click', '.js-offer-price-sent-submit', function(){
		$('.js-form-submitted-message').removeClass('active');
		$('.js-offer-price-amount').addClass('active');
	});

	// google analytics
	$('body').on('gaTakeContact', '.ga-contact-form', function(){
		gaTakeContact($(this));
	});


	function markFavourites(){
		$.ajax({
		    url: nnaAjaxObject.ajaxUrl,
		    type: 'post',
		    dataType: 'json',
		    data: {
		        action: 'nnaGetFavourites',
		    },
		    success: function(data) {
		    	if (data) {
		    		console.log(data);
		    		$('.nna-add-favourite').removeClass('active');
					$.each($('.nna-add-favourite'), function(i, item){
						var favItemId = $(item).attr('item-id');
						$.each(data, function(j, id){
							if (id == favItemId) {
								$(item).addClass('active');
							}
						});
					});
		    	}
		    }
		});
	}
});

