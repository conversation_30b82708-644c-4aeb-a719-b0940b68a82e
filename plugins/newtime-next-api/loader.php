<?php
define('NNA_PATH', plugin_dir_path(__FILE__));
define('NNA_URL', plugins_url('', __FILE__));

require_once NNA_PATH . "/vendor/autoload.php";

// Don't include on development site
if ( ! defined('WS_SERVER')) {
	require_once NNA_PATH . '/vendor/RealEstateDataExchange.php';
}

//Load Core Classes
require_once NNA_PATH . '/lib/helpers/Url.class.php';
require_once NNA_PATH . '/lib/exceptions/ApiException.class.php';
require_once NNA_PATH . '/lib/Settings.class.php';
require_once NNA_PATH . '/lib/WpHandler.class.php';
require_once NNA_PATH . '/lib/CurlHandler.class.php';
require_once NNA_PATH . '/lib/API.class.php';
require_once NNA_PATH . '/lib/Sync.class.php';
require_once NNA_PATH . '/lib/ScoroSync.class.php';
require_once NNA_PATH . '/lib/BrenollisSync.class.php';
require_once NNA_PATH . '/lib/HiddenUpdater.class.php';
require_once NNA_PATH . '/lib/Ajax.class.php';
require_once NNA_PATH . '/lib/Translations.class.php';
require_once NNA_PATH . '/templates/Templates.class.php';
